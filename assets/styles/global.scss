@font-face {
  font-family: "logo";
  src: url("/fonts/ProtestGuerrilla-Regular.ttf");
}
.hidden-scrollbar {
  -ms-overflow-style: none; /* IE, Edge 対応 */
  scrollbar-width: none; /* Firefox 対応 */
}
.hidden-scrollbar::-webkit-scrollbar {
  /* Chrome, Safari 対応 */
  display: none;
}

.rtl-grid {
  direction: rtl;
}

.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost > div {
  opacity: 0.9;
  border: 1px dashed #8e8e8e;
}

.list-group {
  min-height: 20px;
}

.list-group-item {
  cursor: move;
}

.list-group-item i {
  cursor: pointer;
}

.no-move {
  opacity: 0;
  cursor: move;
}

.remove-inline-flex > [role="button"] {
  display: flex !important;
}
