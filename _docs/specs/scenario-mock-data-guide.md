# シナリオ管理 - Mock Data 設定

## 概要
シナリオ管理機能は現在 Mock Data を使用しています。実際の API が実装されるまで、サンプルデータで機能をテストできます。

## Mock Data の構成

### シナリオサンプルデータ
- **製品フィードバック**: 製品満足度を確認するシナリオ
- **サポート問い合わせ**: サポート種類を分類するシナリオ  
- **予約システム**: 予約関連の問い合わせシナリオ（無効状態）

### 終了テンプレートサンプルデータ
- **一般的な終了**: 基本的な終了処理
- **フィードバック収集**: フィードバック後の処理
- **部門転送**: 適切な部門への転送

## 設定の変更

### Mock モードの切り替え
`composables/useDevConfig.ts` で設定を変更できます：

```typescript
const config = {
  // シナリオ用 Mock Data
  USE_MOCK_SCENARIOS: true, // false にすると実際の API を使用
  
  // 終了テンプレート用 Mock Data  
  USE_MOCK_END_TEMPLATES: true, // false にすると実際の API を使用
  
  // API 遅延シミュレーション（ミリ秒）
  MOCK_API_DELAY: 500,
  
  // デバッグログ
  DEBUG_LOGGING: isDevelopment,
}
```

### Mock Data の編集
`composables/useMockScenarioData.ts` でサンプルデータを編集できます：

- `mockScenarios`: シナリオのサンプルデータ
- `mockEndTemplates`: 終了テンプレートのサンプルデータ

## 実装済み機能

### CRUD操作
- ✅ シナリオ一覧取得（ページネーション対応）
- ✅ シナリオ詳細取得
- ✅ シナリオ新規作成
- ✅ シナリオ更新
- ✅ シナリオ削除
- ✅ シナリオ有効/無効切り替え
- ✅ 終了テンプレート一覧取得

### データの永続化
Mock Data モードでは：
- データは **ブラウザセッション中のみ** 保持されます
- ページリロードするとデータがリセットされます
- 作成/更新/削除の操作は一時的なメモリ内のみで実行されます

## API 実装時の移行

実際の API が実装されたら：

1. `useDevConfig.ts` で Mock フラグを `false` に変更
2. API エンドポイントが正しく設定されていることを確認
3. `useScenarioService.ts` の else 分岐が適切に動作することを確認

## デバッグ

開発モードでは：
- コンソールに Mock API の操作ログが表示されます
- UI に「開発モード」のバナーが表示されます
- API 遅延がシミュレートされます

## トラブルシューティング

### よくある問題

1. **データが保存されない**
   - Mock モードでは一時的なデータのため、ページリロードでリセットされます

2. **API エラーが発生する**
   - `useDevConfig.ts` の設定を確認してください
   - Mock フラグが `true` になっているか確認してください

3. **レスポンスが遅い**
   - `MOCK_API_DELAY` の値を調整してください（デフォルト: 500ms）

## 今後の予定

- [ ] 実際の API エンドポイント実装
- [ ] LocalStorage による永続化（オプション）
- [ ] エクスポート/インポート機能
- [ ] より複雑なシナリオパターンの追加