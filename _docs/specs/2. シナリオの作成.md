# シナリオ管理仕様書 (Scenario Management Specification)

## 概要 / Overview
本仕様は、チャットボット用シナリオの作成・編集・削除・一覧表示機能について定義します。  
This specification defines how to manage chatbot scenarios: listing, creating, deleting, and editing.  
**AI Agentへの指示**: この仕様をもとに UI コンポーネント、API呼び出し、Unit Test、Nuxt Test、ドキュメントを生成すること。

---

## 機能一覧 / Features
- シナリオ一覧表示 (Scenario List)
- 新規作成 (Create New Scenario)
- シナリオ編集 (Edit Scenario)
- シナリオ削除 (Delete Scenario)

---

## シナリオ作成・編集仕様 / Scenario Create & Edit

### 基本ルール / Basic Rules
- **シナリオ名**: シナリオの名称を登録し保存する  
- 最初の設問が黄色でハイライトされ、**[設定]** をクリック  
- 設問・選択肢・終了後の挙動を登録し、**[保存]** をクリック  

### 設問 / Question
- 文字数: 最大 **100文字以内**  
- 1つの設問につき最大 **4つの選択肢**  

### 選択肢 / Choices
- 文字数: 最大 **全角150文字以内**  
- 各選択肢の下に「選択時のメッセージ」を設定可能  

#### 選択肢の挙動 (Choice Behaviors)
1. 終了アンケートを表示する  
2. 終了テンプレートを選択してチャットボットを終了する  
3. ☆トークを開設する  
4. 次の設問に進む  
5. 終了する  

---

## 管理画面要件 / Management Screens
1. シナリオ一覧 (Scenario List)  
2. シナリオ新規作成 (Create Scenario)  
3. シナリオ編集 (Edit Scenario)  
4. シナリオ削除 (Delete Scenario)  

---

## バリデーション / Validation
- シナリオ名: 必須  
- 設問: 必須, 100文字以内  
- 選択肢: 最大4件, 各150文字以内  
- 挙動: 必須  

---

## 利用例 / Example Input
```yaml
シナリオ名: "製品フィードバック"

設問1:
  質問: "この製品に満足していますか？"
  選択肢:
    - テキスト: "はい"
      挙動: "終了アンケートを表示する"
    - テキスト: "いいえ"
      挙動: "次の設問に進む"
設問2:
  質問: "不満な点を教えてください。"
  選択肢:
    - テキスト: "価格"
      挙動: "終了テンプレートを選択してチャットボットを終了する"
    - テキスト: "品質"
      挙動: "終了テンプレートを選択してチャットボットを終了する"
```

## テスト仕様 / Test Specification
- ドキュメントテスト (Document Tests)
- 各機能に対して入力条件・期待される出力をまとめた仕様書を生成すること。
- Markdown形式で保存し、シナリオごとにテストケースを記録。

---

## ユニットテスト (Unit Tests)
- 各コンポーネント (ScenarioList.vue, ScenarioForm.vue, ScenarioEditor.vue など) に対するテストを作成する。
- バリデーション: 必須入力, 文字数制限, 最大選択肢数をテスト。
- コンポーネントレンダリング: 正しいUI要素 (input, select, button) が描画されることを確認。

---

## Nuxt Test (Integration Tests)
- シナリオ新規作成 → 保存 → 一覧に反映されるフローをテスト。
- シナリオ編集 → 更新内容が一覧に反映されるフローをテスト。
- シナリオ削除 → 確認ダイアログ → 一覧から削除されることをテスト。

---

## 再利用方法 / Reusability
- この仕様ファイルをAI Agentに与えることで、以下を自動生成可能:
- Vue/Nuxt コンポーネント (Scenario List, Create, Edit, Delete)
- 単体テスト (Unit Test, Nuxt Test)
- ドキュメントテスト (Document-based Test Case)
- APIリクエスト仕様 (POST/PUT/DELETE/GET)
- ドキュメント生成 (開発者向け仕様書・テスト仕様書)