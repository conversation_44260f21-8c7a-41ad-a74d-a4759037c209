# シナリオ管理機能 実装完了報告書

## 概要
チャットボット用シナリオの作成・編集・削除・一覧表示機能を実装しました。仕様書「2. シナリオの作成.md」に基づいて、完全な管理システムを構築しています。

## 実装済みコンポーネント

### 1. UIコンポーネント
- **AppScenarioList.vue** - シナリオ一覧表示コンポーネント
  - 場所: `components/app/chatbot/AppScenarioList.vue`
  - シナリオの表示・検索・削除確認
  - ページネーション対応
  - アクティブ/非アクティブ状態の表示

- **AppScenarioForm.vue** - シナリオ作成・編集フォーム
  - 場所: `components/app/chatbot/AppScenarioForm.vue`
  - バリデーション機能付き入力フォーム
  - 設問・選択肢の動的追加・削除
  - 挙動設定（終了アンケート、終了テンプレート等）
  - 文字数制限の実装

- **AppScenarioViewer.vue** - シナリオ詳細表示
  - 場所: `components/app/chatbot/AppScenarioViewer.vue`
  - 設問フローの視覚的表示
  - 選択肢アクションの分かりやすい表示

### 2. アプリケーション画面
- `/app/chatbot` - チャットボット管理ダッシュボード
- `/app/chatbot/scenario` - シナリオ一覧ページ
- `/app/chatbot/scenario/create` - 新規作成ページ  
- `/app/chatbot/scenario/[id]` - 詳細表示ページ
- `/app/chatbot/scenario/[id]/edit` - 編集ページ

### 3. サービス層
- **useScenarioService.ts** - シナリオCRUD操作
  - `useAppService`を使用してApp APIエンドポイントに接続
  - 作成、読み取り、更新、削除機能
  - 終了テンプレート取得
  - エラーハンドリング

### 4. 型定義
- 既存の `Scenario`, `ScenarioQuestion`, `ScenarioChoice` インターフェース使用
- バリデーション定数の追加 (`scenarioConstants.ts`)

## バリデーション仕様

### 入力制限
- シナリオ名: 最大100文字、必須
- 設問内容: 最大100文字、必須  
- 選択肢テキスト: 最大150文字、必須
- 1つの設問につき最大4つの選択肢

### 業務ルール
- 最低1つの設問が必要
- 最初の設問を必ず設定（黄色でハイライト）
- 各選択肢に挙動を設定必須

## 選択肢挙動

実装済みの選択肢挙動：
1. **終了アンケートを表示する** (`show_end_survey`)
2. **終了テンプレートを選択してチャットボットを終了する** (`select_end_template`)
3. **☆トークを開設する** (`open_talk`) 
4. **次の設問に進む** (`next_question`)
5. **終了する** (`end_scenario`)

## テスト実装

### ユニットテスト
- **test/unit/components/app/chatbot/ScenarioList.test.ts** - AppScenarioListコンポーネントのテスト
- **test/unit/components/app/chatbot/ScenarioForm.test.ts** - AppScenarioFormバリデーションのテスト
- **test/unit/composables/useScenarioService.test.ts** - サービス層のテスト (useAppService使用)

### 統合テスト
- **test/nuxt/scenario-management.test.ts** - シナリオ管理フロー全体のテスト

## 使用技術

- **フレームワーク**: Nuxt 3
- **UIライブラリ**: Nuxt UI
- **レイアウト**: App レイアウト (admin から変更)
- **状態管理**: Composables (useScenarioService + useAppService)
- **バリデーション**: カスタム実装
- **テスト**: Vitest + Vue Test Utils

## API仕様

### エンドポイント
```
GET    /app/chatbot/scenarios           - シナリオ一覧取得
GET    /app/chatbot/scenarios/:id       - シナリオ詳細取得  
POST   /app/chatbot/scenarios           - シナリオ作成
PUT    /app/chatbot/scenarios/:id       - シナリオ更新
DELETE /app/chatbot/scenarios/:id       - シナリオ削除
PATCH  /app/chatbot/scenarios/:id/toggle - アクティブ状態切り替え
GET    /app/chatbot/end-templates       - 終了テンプレート一覧取得
```

### リクエスト形式
```typescript
// シナリオ作成・更新
{
  name: string;
  description?: string;
  isActive: boolean;
  questions: {
    text: string;
    order: number;
    choices: {
      text: string;
      responseMessage?: string;
      action: {
        type: ScenarioActionType;
        nextQuestionId?: string;
        endTemplateId?: string;
      }
    }[]
  }[]
}
```

## セキュリティ

- アプリケーション認証必須 (`middleware: 'auth'`)
- 顧客ID付きリクエスト (`x-customer-id` ヘッダー)
- 入力値サニタイゼーション
- CSRF保護（Nuxtデフォルト）

## アクセシビリティ

- キーボードナビゲーション対応
- スクリーンリーダー対応
- 適切なARIAラベル設定

## パフォーマンス

- 仮想スクロール（大量データ対応）
- 遅延読み込み
- キャッシング機能

## 今後の拡張予定

1. **シナリオテンプレート機能**
   - よく使用されるシナリオパターンのテンプレート化

2. **プレビュー機能** 
   - シナリオの動作確認

3. **インポート/エクスポート**
   - CSVまたはJSONでの一括管理

4. **バージョン管理**
   - シナリオの履歴管理・復元機能

5. **分析機能**
   - シナリオ使用統計・効果測定

## トラブルシューティング

### よくある問題

1. **バリデーションエラー**
   - 必須項目の確認
   - 文字数制限の確認

2. **保存エラー**  
   - ネットワーク接続の確認
   - 認証状態の確認

3. **表示エラー**
   - ブラウザキャッシュのクリア
   - JavaScriptエラーの確認

## 管理者画面からアプリケーションへの移行

### 移行内容
シナリオ管理機能を管理者画面 (`/admin`) からアプリケーション画面 (`/app/chatbot`) に移行しました：

**移行前:**
- 場所: `/admin/scenarios`
- コンポーネント: `components/admin/scenario/`
- サービス: `useAdminService`
- レイアウト: `admin`

**移行後:**
- 場所: `/app/chatbot/scenario`  
- コンポーネント: `components/app/chatbot/`
- サービス: `useAppService`
- レイアウト: `app`

### 変更理由
1. **機能の性質**: シナリオ管理は運用業務であり、管理者専用機能ではない
2. **アクセス権限**: アプリケーションユーザーが日常的に使用する機能
3. **統合性**: チャットボット関連機能を一箇所に集約

### 影響
- 管理者画面からシナリオ管理メニューを削除
- アプリケーション側のチャットボットメニューからアクセス可能
- 顧客IDベースの認証・認可に変更
- API エンドポイントの変更

## Mock Data 対応

### 実装状況
API が未実装のため、現在は Mock Data を使用しています：

- **データファイル**: `composables/useMockScenarioData.ts`
- **設定管理**: `composables/useDevConfig.ts`
- **開発バナー**: `components/app/chatbot/DevBanner.vue`

### Mock データ内容
- 3つのサンプルシナリオ（製品フィードバック、サポート問い合わせ、予約システム）
- 3つの終了テンプレート
- 完全なCRUD操作対応
- API遅延シミュレーション（500ms）

### 本格運用への移行
1. `useDevConfig.ts` で Mock フラグを無効化
2. 実際の API エンドポイント実装
3. レスポンス形式の調整（必要に応じて）

詳細は `_docs/specs/scenario-mock-data-guide.md` を参照してください。

## まとめ

シナリオ管理機能は仕様書の要件を満たし、以下の特徴を持っています：

- ✅ 直感的なUI/UX
- ✅ 堅牢なバリデーション  
- ✅ 包括的なテスト
- ✅ セキュアな実装
- ✅ 拡張性のある設計
- ✅ アプリケーション機能として適切に配置
- ✅ Mock Data による開発・デモ対応

この実装により、API 実装前でもチャットボットシナリオの効率的な管理・テストが可能になります。