import type { AxiosInstance } from "axios";
import Mock<PERSON>dapter from "axios-mock-adapter";
import dayjs from "dayjs";
import _random from "lodash/random";
import {
  CounselorRole,
  CounselorAccountRole,
  CounselorDetailRole,
  TagFormType,
  ReportExportType,
  ReportExportStatus,
  SurveyStatus,
  CaseStatus,
} from "~/types/enums.d";
import type {
  User,
  AuthLoginPayload,
  Customer,
  CaseTag,
  ReportExport,
  Survey,
  SurveyResult,
  Counselor,
} from "~/types";
import { faker } from "@faker-js/faker/locale/ja";
const CounselorMocks: Counselor[] = [
  {
    counselorId: "1",
    organizationName: "PlayNext Lab",
    fullName: "レホアンハオ",
    role: CounselorAccountRole.COUNSELOR,
    loggedInAt: dayjs().subtract(1, "day").toISOString(),
    customerRoles: [
      {
        id: "1",
        name: "PlayNext相談",
        role: CounselorDetailRole.SUPERVISOR,
      },
    ],
    email: "<EMAIL>",
    profileImage: faker.image.avatar(),
  },
];

const CounselorMocksRandom = (length = _random(10, 20)) => {
  const counselors: Counselor[] = [];
  for (let i = 0; i < length; i++) {
    // random from CounselorAccountRole
    const role = _random(0, 1);
    const randomRole = Object.values(CounselorAccountRole)[role];
    const customerRole = _random(0, 2);
    const randomCustomerRole = Object.values(CounselorDetailRole)[customerRole];
    counselors.push({
      counselorId: `${i}`,
      organizationName: "PlayNext Lab",
      fullName: faker.person.fullName(),
      role: randomRole,
      loggedInAt: dayjs().subtract(_random(1, 20), "day").toISOString(),
      customerRoles: [
        {
          id: "1",
          name: "PlayNext相談",
          role: CounselorDetailRole.SUPERVISOR,
        },
      ],
      profileImage: faker.image.avatar(),
      email: faker.internet.email(),
      customerRole: randomCustomerRole,
    });
  }

  return { counselors, total: counselors.length };
};

const CustomerAccountMocks: Customer[] = [
  {
    customerId: "1",
    theme: "pink",
    line: {
      isActive: false,
      login: {
        channelId: "**********",
        secret: "secret",
        liffId: "liffId",
        liffUrl: "liffUrl",
      },
      messaging: {
        channelId: "**********",
        secret: "",
        accessToken: "",
      },
    },
    application: {
      isActive: false,
      accessCode: "accessCode",
      browserAccessCode: "browserAccessCode",
    },
    facebook: {
      isActive: false,
      pageId: "pageId",
      pageAccessToken: "dsfadfaggzdgrgrgsf",
    },
    setting: {
      watchWords: ["死にたい", "帰りたくない"],
      chatType: "line",
      followWord: "string",
      useFollowWord: false,
      beforeSurveyWord: "string",
      useBeforeSurveyWord: false,
      beforeWorkTimeWord: "string",
      useBeforeWorkTimeWord: false,
      notStartedWord: "string",
      useNotStartedWord: false,
      crowdedWord: "string",
      useCrowdedWord: false,
      afterWorkTimeWord: "string",
      useAfterWorkTimeWord: false,
      closeDayWord: "string",
      useCloseDayWord: false,
      notTextMessageWord: "string",
      useNotTextMessageWord: false,
      useWebView: false,
    },
    basic: {
      customerImage: faker.image.avatar(),
      customerName: "東京都相談",
    },
    roles: [],
    counseleeLimit: 10000,
    ipWhiteLists: [],
    startDate: new Date("2024-01-04"),
    endDate: new Date("2024-02-28"),
    contractedLines: 10,
  },
];

let CaseTagsMocks: CaseTag[] = [
  {
    tagId: "T-1",
    tagName: "住所",
    formType: TagFormType.TEXT,
    options: [],
    customerId: "PNL",
    createdAt: new Date().toISOString(),
  },
  {
    tagId: "T-2",
    tagName: "意見",
    formType: TagFormType.TEXT,
    options: [],
    customerId: "PNL",
    createdAt: new Date().toISOString(),
  },
  {
    tagId: "P-1",
    tagName: "年齢",
    formType: TagFormType.PULLDOWN,
    options: ["10代", "20代", "30代", "40代", "50代", "60歳以上"],
    customerId: "PNL",
    createdAt: new Date().toISOString(),
  },
  {
    tagId: "R-1",
    tagName: "業種",
    formType: TagFormType.RADIO,
    options: ["IT", "製造業", "サービス業", "その他"],
    customerId: "PNL",
    createdAt: new Date().toISOString(),
  },
  {
    tagId: "C-1",
    tagName: "話せる言語",
    formType: TagFormType.CHECKBOX,
    options: ["日本語", "英語", "ベトナム語", "韓国語"],
    customerId: "PNL",
    createdAt: new Date().toISOString(),
  },
];

let ReportExportMocks: ReportExport[] = [
  {
    exportId: "1",
    outputColumnNames: ["事案ID", "ステータス", "属性情報", "重要度"],
    exportType: ReportExportType.CASE,
    customerId: "PNL",
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    status: ReportExportStatus.DONE,
  },
  {
    exportId: "2",
    outputColumnNames: ["事案ID", "ステータス", "属性情報"],
    exportType: ReportExportType.CHAT,
    customerId: "PNL",
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    status: ReportExportStatus.IN_PROGRESS,
  },
  {
    exportId: "3",
    outputColumnNames: ["事案ID", "ステータス"],
    exportType: ReportExportType.TOTALLING,
    customerId: "PNL",
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    status: ReportExportStatus.ERROR,
  },
];

let SegmentDeliveryMocks: any[] = [
  {
    segmentId: "XXXXXX-0001",
    segmentName: "冬キャンペーン情報",
    filter: {
      tagIds: ["T-1", "T-2"],
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      isOnline: true,
      status: "対応済",
      counselorId: "C1",
      caseID: "Case1",
      counseleeId: "Counselee1",
    },
    status: "送信中",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    totalDelivery: 100,
    totalSuccess: 50,
  },
  {
    segmentId: "XXXXXX-0002",
    segmentName: "消費税減す",
    filter: {
      tagIds: ["T-1", "T-2"],
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      status: "対応済",
      counseleeId: "Counselee1",
    },
    status: "送信完了",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    totalDelivery: 100,
    totalSuccess: 100,
  },
  {
    segmentId: "XXXXXX-0003",
    segmentName: "児童手当のお知らせ",
    filter: {
      tagIds: ["T-1", "T-2"],
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      status: "対応済",
    },
    status: "エラー",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    totalDelivery: 100,
    totalSuccess: 0,
  },
];

let SurveysMocks: Survey[] = [
  {
    surveyId: "1",
    surveyName: "アンケート1",
    status: SurveyStatus.ACTIVE,
    formTemplate: [
      {
        id: "name",
        _id: "0",
        title: "氏名",
        type: "text",
        required: true,
        tags: ["export_csv"],
        value: "ハオ",
      },
      {
        _id: "email",
        id: "email",
        type: "textMail",
        title: "メールアドレス",
        required: true,
        value: "<EMAIL>",
      },
      {
        _id: "age",
        id: "age",
        type: "selectPulldown",
        title: "年代",
        required: true,
        values: ["〜15才", "16〜19才", "20代", "30代", "40代", "50代", "60代~"],
        value: "20代",
      },
      {
        _id: "gender",
        id: "gender",
        type: "selectSingle",
        title: "性別",
        required: true,
        values: ["男性", "女性", "その他"],
        value: "男性",
      },
      {
        _id: "topic",
        id: "topic",
        type: "selectMulti",
        title: "話したいこと",
        description:
          "話したいことを選択してください。リストにない場合は、その他を選択してください。",
        required: true,
        values: [
          "学校・いじめ",
          "仕事・くらし・お金",
          "暴力・いやがらせ",
          "メンタル・からだ",
          "恋愛・性・性別",
        ],
        hasOtherOption: true,
        value: ["学校・いじめ", "仕事・くらし・お金"],
      },
    ],
    liffUrl: "https://liff.line.me/1655612345-xxxxxx",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    createdBy: "haolh",
  },
  {
    surveyId: "2",
    surveyName: "アンケート2",
    status: SurveyStatus.INACTIVE,
    formTemplate: [
      {
        id: "name",
        _id: "0",
        title: "氏名",
        type: "text",
        required: true,
        tags: ["export_csv"],
        value: "ハオ",
      },
      {
        _id: "email",
        id: "email",
        type: "textMail",
        title: "メールアドレス",
        required: true,
        value: "<EMAIL>",
      },
      {
        _id: "age",
        id: "age",
        type: "selectPulldown",
        title: "年代",
        required: true,
        values: ["〜15才", "16〜19才", "20代", "30代", "40代", "50代", "60代~"],
        value: "20代",
      },
      {
        _id: "gender",
        id: "gender",
        type: "selectSingle",
        title: "性別",
        required: true,
        values: ["男性", "女性", "その他"],
        value: "男性",
      },
      {
        _id: "topic",
        id: "topic",
        type: "selectMulti",
        title: "話したいこと",
        description:
          "話したいことを選択してください。リストにない場合は、その他を選択してください。",
        required: true,
        values: [
          "学校・いじめ",
          "仕事・くらし・お金",
          "暴力・いやがらせ",
          "メンタル・からだ",
          "恋愛・性・性別",
        ],
        hasOtherOption: true,
        value: ["学校・いじめ", "仕事・くらし・お金"],
      },
    ],
    liffUrl: "https://liff.line.me/1655612345-xxxxxx",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    createdBy: "haolh",
  },
  {
    surveyId: "3",
    surveyName: "アンケート3",
    status: SurveyStatus.DRAFT,
    formTemplate: [
      {
        id: "name",
        _id: "0",
        title: "氏名",
        type: "text",
        required: true,
        tags: ["export_csv"],
        value: "ハオ",
      },
      {
        _id: "email",
        id: "email",
        type: "textMail",
        title: "メールアドレス",
        required: true,
        value: "<EMAIL>",
      },
      {
        _id: "age",
        id: "age",
        type: "selectPulldown",
        title: "年代",
        required: true,
        values: ["〜15才", "16〜19才", "20代", "30代", "40代", "50代", "60代~"],
        value: "20代",
      },
      {
        _id: "gender",
        id: "gender",
        type: "selectSingle",
        title: "性別",
        required: true,
        values: ["男性", "女性", "その他"],
        value: "男性",
      },
      {
        _id: "topic",
        id: "topic",
        type: "selectMulti",
        title: "話したいこと",
        description:
          "話したいことを選択してください。リストにない場合は、その他を選択してください。",
        required: true,
        values: [
          "学校・いじめ",
          "仕事・くらし・お金",
          "暴力・いやがらせ",
          "メンタル・からだ",
          "恋愛・性・性別",
        ],
        hasOtherOption: true,
        value: ["学校・いじめ", "仕事・くらし・お金"],
      },
    ],
    liffUrl: "https://liff.line.me/1655612345-xxxxxx",
    customerId: "PNL",
    createdAt: new Date().toISOString(),
    createdBy: "haolh",
  },
];

let SurveyResultsMocks: SurveyResult[] = [
  {
    surveyId: "1",
    surveyName: "アンケート1",
    createdAt: new Date().toISOString(),
    counseleeId: "xxxxxxx",
    counseleeName: "ハオ",
    pictureUrl: faker.image.avatar(),
    formTemplate: SurveysMocks[0].formTemplate,
    answer: [],
    segmentId: "XXXXXX-0001",
    segmentName: "冬キャンペーン情報",
    segmentStatus: "送信中",
  },
  {
    surveyId: "2",
    surveyName: "アンケート2",
    createdAt: new Date().toISOString(),
    counseleeId: "xxxxxxx",
    counseleeName: "太郎",
    pictureUrl: faker.image.avatar(),
    formTemplate: SurveysMocks[1].formTemplate,
    answer: [],
    segmentId: "XXXXXX-0002",
    segmentName: "消費税減す",
    segmentStatus: "送信完了",
  },
  {
    surveyId: "3",
    surveyName: "アンケート3",
    createdAt: new Date().toISOString(),
    counseleeId: "xxxxxxx",
    counseleeName: "山田",
    pictureUrl: faker.image.avatar(),
    formTemplate: SurveysMocks[2].formTemplate,
    answer: [],
  },
];

let CounselorsMocks = consultantsMock(500);

let CasesMocks = (total: number, fixedStatus: CaseStatus) => {
  const cases = [];
  for (let i = 0; i < total; i++) {
    const _messages = [];
    for (let i = 0; i < 50; i++) {
      _messages.push({
        isUserMessage: i % 2 === 0,
        message: "これは古いメッセージです。",
      });
    }
    const status = fixedStatus || randomCaseStatus();
    const customerRandom = randomFromTo(0, CustomerAccountMocks.length - 1);

    const caseObj = {
      id: i,
      caseId: faker.string.uuid(),
      customerId: CustomerAccountMocks[customerRandom].customerId,
      assigneeName: [CaseStatus.BEFORE_START, CaseStatus.OPEN].includes(status)
        ? ""
        : faker.person.fullName(),
      status,
      openedAt: dayjs().subtract(randomFromTo(1, 5), "day").unix(),
      inProgressingAt: [CaseStatus.BEFORE_START, CaseStatus.OPEN].includes(
        status,
      )
        ? undefined
        : dayjs().subtract(randomFromTo(1, 24), "hour").unix(),
      latestMessageAt: dayjs().subtract(randomFromTo(1, 3), "hour").unix(),
      clientName: faker.person.fullName(),
      userId: faker.string.numeric({ length: 5 }),
      times: randomFromTo(1, 3),
      priority: randomCasePriority(),
      snsChannel: randomSNSChannel(),
      userAvatar: faker.image.avatar(),
      chat: _messages,
      caseConveyed: faker.lorem.sentence(),
      memos: [
        faker.lorem.sentence(),
        faker.lorem.sentence(),
        faker.lorem.sentence(),
      ],
      selectedTags: [
        {
          id: "C-1",
          label: "話せる言語",
          icon: "i-mdi-checkbox-outline",
          tagId: "C-1",
          tagName: "話せる言語",
          formTemplateId: "checkbox",
          formTemplate:
            '{ "options": ["日本語", "英語", "ベトナム語", "韓国語"] }',
          customerId: "PNL",
          createdAt: "2023-11-29T08:06:30.524Z",
          group: "checkbox",
          refIndex: 4,
          value: null,
          no: 0,
        },
        {
          id: "P-1",
          label: "年齢",
          icon: "i-tabler-select",
          tagId: "P-1",
          tagName: "年齢",
          formTemplateId: "pulldown",
          formTemplate:
            '{ "options": ["10代", "20代", "30代", "40代", "50代", "60歳以上"] }',
          customerId: "PNL",
          createdAt: "2023-11-29T08:06:30.524Z",
          group: "pulldown",
          refIndex: 2,
          value: null,
          no: 1,
        },
      ],
    };
    cases.push(caseObj);
  }
  return cases;
};
export const useMockService = (instance: AxiosInstance) => {
  const mock = new MockAdapter(instance, { delayResponse: 1000 });

  // mock login by admin user
  mock
    .onPost("/login/auth", {
      asymmetricMatch: function (actual: AuthLoginPayload) {
        return (
          ["admin-user"].includes(actual.username) &&
          actual.password === "PNL@2023"
        );
      },
    })
    .reply(200, {
      counselorId: "1",
      fullname: "Admin User",
      role: CounselorRole.ADMIN,
      email: "<EMAIL>",
      idToken: "**********",
      profileImage: faker.image.avatar(),
      organizationName: "PlayNext Lab",
    } as User);

  // mock login by normal user
  mock
    .onPost("/login/auth", {
      asymmetricMatch: function (actual: AuthLoginPayload) {
        return (
          ["supervisor-user"].includes(actual.username) &&
          actual.password === "PNL@2023"
        );
      },
    })
    .reply(200, {
      counselorId: "1",
      fullname: "Supervisor User",
      role: CounselorRole.SUPERVISOR,
      email: "<EMAIL>",
      idToken: "**********",
      profileImage: "https://avatars.githubusercontent.com/u/739984?v=1",
      organizationName: "PlayNext Lab",
    } as User);

  // mock login failed
  // mock login by admin user
  mock.onPost("/login/auth").reply(400, {
    error: "Invalid username or password",
  });

  // update consulting account
  mock.onGet("/customer/customers").reply(200, CustomerAccountMocks);
  mock.onGet("/consulting-accounts").reply(200, {
    customerAccounts: CustomerAccountMocks,
  });

  // update consulting account
  mock.onPatch("/consulting-account").reply(function (config) {
    const reqBody = JSON.parse(config.data);

    CustomerAccountMocks.splice(
      CustomerAccountMocks.findIndex(
        (consultingAccount) => consultingAccount.customerId === reqBody.id,
      ),
      1,
      reqBody,
    );
    return [
      200,
      {
        error: null,
      },
    ];
  });
  // add consulting account
  mock.onPost("/consulting-account").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    reqBody.id = randomFromTo(100, 1000000);
    CustomerAccountMocks.push(reqBody);
    return [200, reqBody];
  });

  // delete consulting account
  mock.onDelete("/consulting-account").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    CustomerAccountMocks.splice(
      CustomerAccountMocks.findIndex(
        (consultingAccount) => consultingAccount.customerId === reqBody.id,
      ),
      1,
    );
    return [
      200,
      {
        error: null,
      },
    ];
  });

  mock.onGet("/counselor/testCounselor3").reply(200, CounselorMocksRandom());
  mock
    .onGet("/counselor/counselors")
    .reply(200, CounselorMocksRandom().counselors);

  mock.onGet(/\/counselor\/.*/).reply(200, {
    counselorId: "1",
    fullName: "Admin User",
    role: CounselorRole.ADMIN,
    email: "<EMAIL>",
    accessToken: "**********",
    profileImage: faker.image.avatar(),
    organizationName: "PlayNext Lab",
  } as User);

  // add user account
  mock.onPost("/user-account").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    reqBody.id = randomFromTo(100, 1000000);
    return [200, reqBody];
  });

  // update user account
  mock.onPatch("/user-account").reply(200, {
    error: null,
  });

  // delete user account
  mock.onDelete("/user-account").reply(function () {
    return [
      200,
      {
        error: null,
      },
    ];
  });

  mock.onGet("/tags").reply(function () {
    return [
      200,
      {
        caseTags: CaseTagsMocks,
        total: CaseTagsMocks.length,
      },
    ];
  });

  mock.onPost("/tag").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    reqBody.tagId = "tag-" + randomFromTo(100, 1000000);
    reqBody.createdAt = new Date().toISOString();
    CaseTagsMocks.push(reqBody);
    return [200, reqBody];
  });

  mock.onPatch("/tag").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    CaseTagsMocks.splice(
      CaseTagsMocks.findIndex((tag) => tag.tagId === reqBody.tagId),
      1,
      reqBody,
    );
    return [200, reqBody];
  });

  mock.onGet("/reports").reply(function () {
    return [
      200,
      {
        reports: ReportExportMocks,
        total: ReportExportMocks.length,
      },
    ];
  });

  mock.onGet("/segment-delivery").reply(function () {
    return [
      200,
      {
        data: SegmentDeliveryMocks,
        total: SegmentDeliveryMocks.length,
      },
    ];
  });

  mock.onPost("/joinChatByCode").reply(function (config) {
    const reqBody = JSON.parse(config.data);
    // get code from request body
    const { code } = reqBody;
    const today = dayjs().format("YYMMDD");
    //compare code with today
    if (code === today) {
      return [
        200,
        {
          accessToken: "**********",
        },
      ];
    }
    return [400, { error: "Invalid code" }];
  });

  mock.onGet("/fetchCustomerInfo").reply(function () {
    return [200, CustomerAccountMocks[0]];
  });

  mock.onGet("/surveys").reply(function () {
    return [
      200,
      {
        surveys: SurveysMocks,
        total: SurveysMocks.length,
      },
    ];
  });

  mock.onGet("/survey").reply(function () {
    return [
      200,
      {
        survey: SurveysMocks[0],
      },
    ];
  });

  mock.onGet("/surveyResults").reply(function () {
    return [
      200,
      {
        surveyResults: SurveyResultsMocks,
        total: SurveyResultsMocks.length,
      },
    ];
  });

  mock.onGet("/segment-delivery-targets").reply(function () {
    const randomInt = randomFromTo(10, 100);
    const cases = casesMock(randomInt);
    return [
      200,
      {
        targets: cases,
        targetsCount: cases.length,
        totalCount: randomFromTo(100, 150),
      },
    ];
  });

  mock.onGet("/consultants-list").reply(function () {
    const random = randomFromTo(40, 50);
    const counselors = CounselorsMocks.slice(randomFromTo(0, 39), random);
    return [
      200,
      {
        counselors,
      },
    ];
  });

  mock.onGet(/\/counselor-detail\/*/).reply(function (config) {
    const reqBody = JSON.parse(config.data);
    const counselor = CounselorsMocks.find(
      (obj) => obj.counselorId === reqBody.counselorId,
    );
    return [
      200,
      {
        ...counselor,
        ...reqBody,
        customers: CustomerAccountMocks,
        cases: CasesMocks(
          counselor?.numberOfActiveCases || 0,
          CaseStatus.IN_PROGRESS,
        ),
      },
    ];
  });
};
