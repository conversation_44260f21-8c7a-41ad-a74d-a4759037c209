import type { Scenario, ScenarioQuestion, ScenarioChoice, EndTemplate } from '~/types'

// Mock data for scenarios
export const mockScenarios: Scenario[] = [
  {
    id: '1',
    name: '製品フィードバック',
    description: 'お客様からの製品に関するフィードバックを収集するシナリオ',
    isActive: true,
    customerId: 'customer-1',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    questions: [
      {
        id: 'q1',
        text: 'この製品に満足していますか？',
        choices: [
          {
            id: 'c1',
            text: 'はい、とても満足しています',
            responseMessage: 'ありがとうございます！',
            action: {
              type: 'show_end_survey'
            }
          },
          {
            id: 'c2',
            text: 'いいえ、不満があります',
            responseMessage: 'フィードバックをお聞かせください',
            action: {
              type: 'next_question',
              nextQuestionId: 'q2'
            }
          },
          {
            id: 'c3',
            text: 'どちらでもない',
            responseMessage: '詳しくお聞かせください',
            action: {
              type: 'select_end_template',
              endTemplateId: 'et1'
            }
          }
        ],
        isFirstQuestion: true,
        order: 1
      },
      {
        id: 'q2',
        text: '不満な点を教えてください。',
        choices: [
          {
            id: 'c4',
            text: '価格が高い',
            responseMessage: '価格についてのご意見ありがとうございます',
            action: {
              type: 'select_end_template',
              endTemplateId: 'et2'
            }
          },
          {
            id: 'c5',
            text: '品質に問題がある',
            responseMessage: '品質改善に努めます',
            action: {
              type: 'open_talk'
            }
          },
          {
            id: 'c6',
            text: '使いにくい',
            responseMessage: 'UI/UX改善の参考にします',
            action: {
              type: 'end_scenario'
            }
          }
        ],
        isFirstQuestion: false,
        order: 2
      }
    ]
  },
  {
    id: '2',
    name: 'サポート問い合わせ',
    description: 'お客様からのサポート問い合わせを分類するシナリオ',
    isActive: true,
    customerId: 'customer-1',
    createdAt: '2024-02-01T09:00:00Z',
    updatedAt: '2024-02-01T09:00:00Z',
    questions: [
      {
        id: 'q3',
        text: 'どのようなサポートが必要ですか？',
        choices: [
          {
            id: 'c7',
            text: '技術的な問題',
            responseMessage: '技術サポートにお繋ぎします',
            action: {
              type: 'open_talk'
            }
          },
          {
            id: 'c8',
            text: '請求・支払い',
            responseMessage: '請求部門にお繋ぎします',
            action: {
              type: 'select_end_template',
              endTemplateId: 'et3'
            }
          },
          {
            id: 'c9',
            text: '一般的な質問',
            responseMessage: 'よくある質問をご確認ください',
            action: {
              type: 'end_scenario'
            }
          }
        ],
        isFirstQuestion: true,
        order: 1
      }
    ]
  },
  {
    id: '3',
    name: '予約システム',
    description: '予約に関する問い合わせを処理するシナリオ',
    isActive: false,
    customerId: 'customer-1',
    createdAt: '2024-01-10T16:20:00Z',
    updatedAt: '2024-01-12T11:45:00Z',
    questions: [
      {
        id: 'q4',
        text: '予約に関してどのようなサポートが必要ですか？',
        choices: [
          {
            id: 'c10',
            text: '新規予約',
            responseMessage: '新規予約を承ります',
            action: {
              type: 'select_end_template',
              endTemplateId: 'et1'
            }
          },
          {
            id: 'c11',
            text: '予約変更',
            responseMessage: '予約変更を承ります',
            action: {
              type: 'open_talk'
            }
          }
        ],
        isFirstQuestion: true,
        order: 1
      }
    ]
  }
]

// Mock data for end templates
export const mockEndTemplates: EndTemplate[] = [
  {
    id: 'et1',
    templateName: '一般的な終了',
    finalQuestion: 'その他にご質問はございますか？',
    choices: [
      {
        id: 'etc1',
        text: 'はい、質問があります',
        responseMessage: 'オペレーターにお繋ぎします',
        openOneOnOneTalk: true
      },
      {
        id: 'etc2',
        text: 'いいえ、ありません',
        responseMessage: 'ありがとうございました',
        openOneOnOneTalk: false
      }
    ],
    isActive: true,
    customerId: 'customer-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'et2',
    templateName: 'フィードバック収集',
    finalQuestion: 'フィードバックをありがとうございました。他にもご意見はございますか？',
    choices: [
      {
        id: 'etc3',
        text: '他にも意見があります',
        responseMessage: 'ぜひお聞かせください',
        openOneOnOneTalk: true
      },
      {
        id: 'etc4',
        text: '以上です',
        responseMessage: '貴重なご意見をありがとうございました',
        openOneOnOneTalk: false
      }
    ],
    isActive: true,
    customerId: 'customer-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'et3',
    templateName: '部門転送',
    finalQuestion: '適切な部門にお繋ぎいたします。このままお待ちください。',
    choices: [
      {
        id: 'etc5',
        text: '了解しました',
        responseMessage: 'お繋ぎしています...',
        openOneOnOneTalk: true
      }
    ],
    isActive: true,
    customerId: 'customer-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
]

// Helper function to simulate API delay
export const simulateApiDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms))

// Helper function to generate new IDs
export const generateId = () => Math.random().toString(36).substr(2, 9)

// Mock API response wrapper
export const createMockApiResponse = <T>(data: T) => ({
  value: data
})