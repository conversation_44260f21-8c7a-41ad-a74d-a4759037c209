/**
 * Composable for managing development configuration
 * This allows easy switching between mock data and real API
 */
export const useDevConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  // Configuration flags
  const config = {
    // Use mock data for scenarios when API is not available
    USE_MOCK_SCENARIOS: isDevelopment || true, // Force true for now
    
    // Use mock data for end templates
    USE_MOCK_END_TEMPLATES: isDevelopment || true, // Force true for now
    
    // API delay simulation in ms
    MOCK_API_DELAY: 500,
    
    // Enable debug logging
    DEBUG_LOGGING: isDevelopment,
  }
  
  // Helper function to log debug messages
  const debugLog = (message: string, data?: any) => {
    if (config.DEBUG_LOGGING) {
      console.log(`[DEV] ${message}`, data || '')
    }
  }
  
  // Helper function to simulate API delay
  const simulateDelay = (customDelay?: number) => {
    const delay = customDelay || config.MOCK_API_DELAY
    return new Promise(resolve => setTimeout(resolve, delay))
  }
  
  return {
    config,
    debugLog,
    simulateDelay,
    isDevelopment
  }
}