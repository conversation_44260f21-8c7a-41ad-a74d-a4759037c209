import axios from "axios";
import { useAxios } from "@vueuse/integrations/useAxios";

export const usePortalService = (url: string, options?: any) => {
  const runtimeConfig = useRuntimeConfig();
  const portalStore = usePortalStore();

  const headers = {
    Authorization: `Bearer ${portalStore.tempToken}`,
    "x-customer-id": portalStore.customer?.id,
  };
  const instance = axios.create({
    baseURL: runtimeConfig.public.NUXT_API_ADMIN_BASE_URL,
    headers,
  });

  instance.interceptors.response.use(
    (config) => {
      const { data } = config;
      return config;
    },
    async (error) => {
      const _errorResponse = error.response;
      const status = _errorResponse.status;
      switch (status) {
        case 401:
          portalStore.logout();
          return Promise.reject(error);

          break;
        default:
          break;
      }
      return Promise.reject(error);
    },
  );
  return useAxios(url, options, instance);
};
