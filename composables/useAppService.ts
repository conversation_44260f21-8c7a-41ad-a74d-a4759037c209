import axios from "axios";
import { useAxios } from "@vueuse/integrations/useAxios";
import { useMockService } from "./useMockService";
import { useAppCustomersStore } from "@/stores/app/customers";
import { useSocketStore } from "@/stores/socket";

export const useAppService = (url: string, options?: any) => {
  const runtimeConfig = useRuntimeConfig();
  const accessToken = useCookie("accessToken");
  const currentCustomerId =
    useAppCustomersStore().currentCustomerId || "";
  const headers = {
    Authorization: `Bearer ${accessToken.value}`,
    "x-customer-id": currentCustomerId,
  };
  const instance = axios.create({
    baseURL: runtimeConfig.public.NUXT_API_ADMIN_BASE_URL,
    headers,
  });

  instance.interceptors.response.use(
    (config) => {
      const { data } = config;
      // check if the response is a socket message (include sendJsons)
      if (data?.sendJsons) {
        const socketStore = useSocketStore();
        socketStore.emitMessageToAppSocket(data.sendJsons);
      }
      return config;
    },
    async (error) => {
      const _errorResponse = error.response;
      const originalRequest = error.config;
      const status = _errorResponse.status;
      const authStore = useAuthStore();
      switch (status) {
        case 401:
          authStore.logout();
          return Promise.reject(error);

          break;
        default:
          break;
      }
      return Promise.reject(error);
    },
  );

  if (localStorage.getItem("useMock") === "true") {
    useMockService(instance);
  }
  return useAxios(url, options, instance);
};
