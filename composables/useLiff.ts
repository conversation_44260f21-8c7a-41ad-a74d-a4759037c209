import liff from "@line/liff";

export const useLiff = () => {
  const runtimeConfig = useRuntimeConfig();
  const profile = ref({});
  const init = async (
    liffId: string,
    query: Record<string, string>,
  ): Promise<{ success: boolean; error?: any }> => {
    try {
      await liff.init({ liffId });
      // create query string from query object
      const queryString = Object.entries(query)
        .map(([key, value]) => `${key}=${value}`)
        .join("&");
      // const lineLoginRedirectUrl = `https://liff.line.me/${liffId}?${queryString}`;
      console.log("liff.init() done");
      if (runtimeConfig.public.NUXT_LINE_LIFF_MOCK) {
        profile.value = {
          userId: "U1234567890",
          displayName: "Mock User",
          pictureUrl: "https://picsum.photos/200/300",
          statusMessage: "Hello, world!",
          idToken: runtimeConfig.public.NUXT_LINE_ID_TOKEN,
        };
      } else {
        if (!liff.isLoggedIn()) {
          console.log("liff.login() start");
          await liff.login({
            // redirectUri: lineLoginRedirectUrl,
          });
        }
        console.log("liff.login() done");
        const lineProfile = await liff.getProfile();
        const idToken = liff.getIDToken();
        profile.value = {
          ...lineProfile,
          idToken,
        };
      }

      return { success: true };
    } catch (error) {
      console.error("liff.init() failed", error);
      return { success: false, error };
    }
  };

  const relogin = async () => {
    console.log("liff.relogin() start");
    if (liff.isLoggedIn()) {
      await liff.logout();
      await liff.login();
    } else {
      await liff.login();
    }
  };
  return { liff, init, profile, relogin };
};
