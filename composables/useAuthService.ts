import axios from "axios";
import { useAxios } from "@vueuse/integrations/useAxios";
import { useMockService } from "./useMockService";
import { useAppCustomersStore } from "@/stores/app/customers";
export const useAuthService = (url: string, options?: any) => {
  const runtimeConfig = useRuntimeConfig();
  const accessToken = useCookie("accessToken");
  const currentCustomerId =
    useAppCustomersStore().currentCustomerId || "";
  const headers = {
    Authorization: `Bearer ${accessToken.value}`,
    "x-customer-id": currentCustomerId,
  };
  const instance = axios.create({
    baseURL: runtimeConfig.public.NUXT_API_ADMIN_BASE_URL,
    headers,
  });

  instance.interceptors.response.use(
    (config) => {
      return config;
    },
    async (error) => {
      const _errorResponse = error.response;
      const originalRequest = error.config;
      const status = _errorResponse?.status;
      const authStore = useAuthStore();
      switch (status) {
        case 401:
          // check if path is not logout
          if (!originalRequest.url?.includes("logout")) {
            authStore.logout();
          }
          return Promise.reject(error);

        default:
          break;
      }
      return Promise.reject(error);
    },
  );

  if (localStorage.getItem("useMock") === "true") {
    useMockService(instance);
  }
  return useAxios(url, options, instance);
};
