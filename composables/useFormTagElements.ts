import FormText from "@/components/form/FormText.vue";
import FormCheckbox from "@/components/form/FormCheckbox.vue";
import FormPulldown from "@/components/form/FormPulldown.vue";
import FormTextAndPulldown from "@/components/form/FormTextAndPulldown.vue";
import FormRadio from "@/components/form/FormRadio.vue";
import { TagFormType } from "~/types/enums.d";

export const useFormTagElements = (): TagFormTypeElement => {
  const FormElements: TagFormTypeElement = {
    [TagFormType.TEXT]: FormText,
    [TagFormType.PULLDOWN]: FormPulldown,
    [TagFormType.RADIO]: FormRadio,
    [TagFormType.CHECKBOX]: FormCheckbox,
    [TagFormType.TEXT_AND_PULLDOWN]: FormTextAndPulldown,
    undefined: FormText,
  };

  return { FormElements };
};
