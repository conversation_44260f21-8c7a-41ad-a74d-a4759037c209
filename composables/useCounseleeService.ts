import axios from "axios";
import { useAxios } from "@vueuse/integrations/useAxios";
import { useMockService } from "./useMockService";
import { useSocketStore } from "@/stores/socket";

export const useCounseleeService = (url: string, options?: any) => {
  const runtimeConfig = useRuntimeConfig();

  const instance = axios.create({
    baseURL: runtimeConfig.public.NUXT_API_ADMIN_BASE_URL,
  });

  instance.interceptors.response.use(
    (config) => {
      const { data } = config;
      // check if the response is a socket message (include sendJsons)
      if (data?.body?.sendJsons) {
        const socketStore = useSocketStore();
        socketStore.emitMessageToCounseleeSocket(data?.body?.sendJsons);
      }
      return config;
    },
    async (error) => {
      const _errorResponse = error.response;
      const status = _errorResponse.status;
      const authStore = useAuthStore();
      switch (status) {
        case 401:
          authStore.logout();
          return Promise.reject(error);

          break;
        default:
          break;
      }
      return Promise.reject(error);
    },
  );

  if (localStorage.getItem("useMock") === "true") {
    useMockService(instance);
  }
  return useAxios(url, options, instance);
};
