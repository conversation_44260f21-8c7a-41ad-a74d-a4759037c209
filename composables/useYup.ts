import * as yup from "yup";
import YupPassword from "yup-password";
YupPassword(yup); // extend yup
export const useYup = () => {
  const { t } = useI18n();
  yup.setLocale({
    mixed: {
      required: t("Required"),
    },
    string: {
      min: ({ min, label }) => t("Min", { min, label }),
      length: ({ length, label }) => t("Length", { length, label }),
    },
    number: {
      min: ({ min, label }) =>
        t("Must be greater than or equal to", { min, label }),
    },
  });

  return yup;
};
