import FormSurveyTitleAndDescription from "~/components/formSurvey/FormSurveyTitleAndDescription.vue";
import FormSurveyText from "~/components/formSurvey/FormSurveyText.vue";
import FormSurveyTextName from "~/components/formSurvey/FormSurveyTextName.vue";
import FormSurveyTextMail from "~/components/formSurvey/FormSurveyTextMail.vue";
import FormSurveyTextAddress from "~/components/formSurvey/FormSurveyTextAddress.vue";
import FormSurveyTextArea from "~/components/formSurvey/FormSurveyTextArea.vue";
import FormSurveySelectSingle from "~/components/formSurvey/FormSurveySelectSingle.vue";
import FormSurveySelectMulti from "~/components/formSurvey/FormSurveySelectMulti.vue";
import FormSurveySelectPulldown from "~/components/formSurvey/FormSurveySelectPulldown.vue";
import FormSurveyDate from "~/components/formSurvey/FormSurveyDate.vue";
import FormSurveyTime from "~/components/formSurvey/FormSurveyTime.vue";

export const useFormSurveyElements = (): FormElements => {
  const FormElements: FormElements = {
    titleAndDescription: FormSurveyTitleAndDescription,
    text: FormSurveyText,
    textName: FormSurveyTextName,
    textMail: FormSurveyTextMail,
    textAddress: FormSurveyTextAddress,
    textArea: FormSurveyTextArea,
    selectSingle: FormSurveySelectSingle,
    selectMulti: FormSurveySelectMulti,
    selectPulldown: FormSurveySelectPulldown,
    date: FormSurveyDate,
    time: FormSurveyTime,
  };

  const OtherOptionKey = "#OTHER_OPTION:";
  return { FormElements, OtherOptionKey };
};
