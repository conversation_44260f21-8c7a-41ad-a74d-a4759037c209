import WizardTitleAndDescription from "~/components/wizard/WizardTitleAndDescription.vue";
import WizardButtons from "~/components/wizard/WizardButtons.vue";
import WizardSelectSingle from "~/components/wizard/WizardSelectSingle.vue";
import WizardTextArea from "~/components/wizard/WizardTextArea.vue";
import WizardText from "~/components/wizard/WizardText.vue";
import WizardSelectPulldown from "~/components/wizard/WizardSelectPulldown.vue";

const FormElementTypes = ["selectSingle", "textArea", "text", "selectPulldown"];

export const useWizard = (): WizardElement => {
  const WizardElement: WizardElement = {
    titleAndDescription: WizardTitleAndDescription,
    selectSingle: WizardSelectSingle,
    buttons: WizardButtons,
    textArea: WizardTextArea,
    text: WizardText,
    selectPulldown: WizardSelectPulldown,
  };

  return { WizardElementComponent: WizardElement, FormElementTypes };
};
