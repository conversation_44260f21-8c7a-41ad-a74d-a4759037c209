import type { <PERSON><PERSON><PERSON>, EndTemplate } from '~/types'
import { 
  mockScenarios, 
  mockEndTemplates, 
  simulateApiDelay, 
  generateId, 
  createMockApiResponse 
} from './useMockScenarioData'
import { useDevConfig } from './useDevConfig'

export const useScenarioService = () => {
  const scenarios = ref<Scenario[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // Development configuration
  const { config, debugLog, simulateDelay } = useDevConfig()

  // Fetch scenarios list
  const fetchScenarios = async (page = 1, limit = 10) => {
    loading.value = true
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Fetching mock scenarios', { page, limit })
        
        // Simulate API delay
        await simulateDelay()
        
        // Simulate pagination
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        const paginatedScenarios = mockScenarios.slice(startIndex, endIndex)
        
        scenarios.value = paginatedScenarios
        totalCount.value = mockScenarios.length
        currentPage.value = page
      } else {
        const appService = useAppService(`/chatbot/scenarios?page=${page}&limit=${limit}`)
        const response = await appService.data
        
        scenarios.value = response.value?.scenarios || []
        totalCount.value = response.value?.totalCount || 0
        currentPage.value = page
      }
    } catch (error) {
      console.error('Failed to fetch scenarios:', error)
      scenarios.value = []
      totalCount.value = 0
    } finally {
      loading.value = false
    }
  }

  // Get scenario by ID
  const getScenario = async (id: string): Promise<Scenario | null> => {
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Getting mock scenario by ID', id)
        await simulateDelay()
        const scenario = mockScenarios.find(s => s.id === id)
        return scenario || null
      } else {
        const appService = useAppService(`/chatbot/scenarios/${id}`)
        const response = await appService.data
        return response.value?.scenario || null
      }
    } catch (error) {
      console.error('Failed to get scenario:', error)
      return null
    }
  }

  // Create scenario
  const createScenario = async (scenarioData: Partial<Scenario>) => {
    saving.value = true
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Creating mock scenario', scenarioData)
        await simulateDelay()
        
        // Create new scenario with generated ID
        const newScenario: Scenario = {
          id: generateId(),
          name: scenarioData.name || '',
          description: scenarioData.description,
          isActive: scenarioData.isActive ?? true,
          customerId: 'customer-1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          questions: scenarioData.questions?.map(q => ({
            ...q,
            id: q.id || generateId(),
            choices: q.choices.map(c => ({
              ...c,
              id: c.id || generateId()
            }))
          })) || []
        }
        
        // Add to mock data
        mockScenarios.push(newScenario)
        return newScenario
      } else {
        const appService = useAppService('/chatbot/scenarios', {
          method: 'POST',
          data: scenarioData
        })
        const response = await appService.data
        return response.value?.scenario
      }
    } catch (error) {
      console.error('Failed to create scenario:', error)
      throw error
    } finally {
      saving.value = false
    }
  }

  // Update scenario
  const updateScenario = async (id: string, scenarioData: Partial<Scenario>) => {
    saving.value = true
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Updating mock scenario', { id, scenarioData })
        await simulateDelay()
        
        // Find and update scenario in mock data
        const index = mockScenarios.findIndex(s => s.id === id)
        if (index !== -1) {
          mockScenarios[index] = {
            ...mockScenarios[index],
            ...scenarioData,
            id, // Keep original ID
            updatedAt: new Date().toISOString(),
            questions: scenarioData.questions?.map(q => ({
              ...q,
              id: q.id || generateId(),
              choices: q.choices.map(c => ({
                ...c,
                id: c.id || generateId()
              }))
            })) || mockScenarios[index].questions
          }
          return mockScenarios[index]
        }
        throw new Error('Scenario not found')
      } else {
        const appService = useAppService(`/chatbot/scenarios/${id}`, {
          method: 'PUT',
          data: scenarioData
        })
        const response = await appService.data
        return response.value?.scenario
      }
    } catch (error) {
      console.error('Failed to update scenario:', error)
      throw error
    } finally {
      saving.value = false
    }
  }

  // Delete scenario
  const deleteScenario = async (id: string) => {
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Deleting mock scenario', id)
        await simulateDelay()
        
        // Remove scenario from mock data
        const index = mockScenarios.findIndex(s => s.id === id)
        if (index !== -1) {
          mockScenarios.splice(index, 1)
        } else {
          throw new Error('Scenario not found')
        }
      } else {
        const appService = useAppService(`/chatbot/scenarios/${id}`, {
          method: 'DELETE'
        })
        await appService.data
      }
    } catch (error) {
      console.error('Failed to delete scenario:', error)
      throw error
    }
  }

  // Toggle scenario active status
  const toggleScenario = async (id: string, isActive: boolean) => {
    try {
      if (config.USE_MOCK_SCENARIOS) {
        debugLog('Toggling mock scenario status', { id, isActive })
        await simulateDelay()
        
        // Update scenario status in mock data
        const scenario = mockScenarios.find(s => s.id === id)
        if (scenario) {
          scenario.isActive = isActive
          scenario.updatedAt = new Date().toISOString()
        } else {
          throw new Error('Scenario not found')
        }
      } else {
        const appService = useAppService(`/chatbot/scenarios/${id}/toggle`, {
          method: 'PATCH',
          data: { isActive }
        })
        await appService.data
      }
    } catch (error) {
      console.error('Failed to toggle scenario:', error)
      throw error
    }
  }

  // Fetch end templates for form
  const endTemplates = ref<EndTemplate[]>([])
  const loadingEndTemplates = ref(false)

  const fetchEndTemplates = async () => {
    loadingEndTemplates.value = true
    try {
      if (config.USE_MOCK_END_TEMPLATES) {
        debugLog('Fetching mock end templates')
        await simulateDelay()
        endTemplates.value = mockEndTemplates
      } else {
        const appService = useAppService('/chatbot/end-templates')
        const response = await appService.data
        endTemplates.value = response.value?.endTemplates || []
      }
    } catch (error) {
      console.error('Failed to fetch end templates:', error)
      endTemplates.value = []
    } finally {
      loadingEndTemplates.value = false
    }
  }

  return {
    // State
    scenarios,
    loading,
    saving,
    totalCount,
    currentPage,
    pageSize,
    endTemplates,
    loadingEndTemplates,
    
    // Methods
    fetchScenarios,
    getScenario,
    createScenario,
    updateScenario,
    deleteScenario,
    toggleScenario,
    fetchEndTemplates
  }
}