import { Gender, AgeDecade, CustomerFeature } from "~/types/enums.d";
const genderOptions = [
  {
    label: "男性",
    value: Gender.Male,
  },
  {
    label: "女性",
    value: Gender.Female,
  },
  {
    label: "その他",
    value: Gender.Other,
  },
];

const ageOptions = [
  {
    label: "10代",
    value: AgeDecade.Teens,
  },
  {
    label: "20代",
    value: AgeDecade.Twenties,
  },
  {
    label: "30代",
    value: AgeDecade.Thirties,
  },
  {
    label: "40代",
    value: AgeDecade.Forties,
  },
  {
    label: "50代",
    value: AgeDecade.Fifties,
  },
  {
    label: "60代",
    value: AgeDecade.Sixties,
  },
  {
    label: "70代",
    value: AgeDecade.Seventies,
  },
  {
    label: "80代以上",
    value: AgeDecade.EightiesAndOlder,
  },
];
const chatTypes = ref([
  {
    icon: "i-uil-line",
    title: "LINEアプリ",
    iconColor: "text-green-500",
    description: "LINEアプリを利用して相談する",
    value: "line",
  },
  {
    icon: "i-twemoji-mobile-phone",
    title: "LIFFブラウザ",
    iconColor: "text-primary-500",
    description: "LINEアプリを利用して相談する",
    value: "liff",
  },
  {
    icons: [
      "i-logos-chrome",
      "i-logos-firefox",
      "i-logos-safari",
      "i-logos-microsoft-edge",
    ],
    title: "外部ブラウザ",
    iconColor: "text-green-500",
    description: "LINEアプリを利用して相談する",
    value: "browser",
  },
]);

const featureList = ref([
  {
    icon: "i-fxemoji-linkedpaperclips",
    title: "ウィザード",
    iconColor: "text-green-500",
    description: "ウィザード",
    value: CustomerFeature.Wizard,
  },
  {
    icon: "i-noto-v1-robot",
    title: "チャットボット",
    iconColor: "text-blue-500",
    description: "自動応答チャットボット",
    value: CustomerFeature.Chatbot,
  },
]);

const SCHOOL_LIST = [
  { schoolName: '東部中学校', schoolId: '10001' },
  { schoolName: '西部中学校', schoolId: '10002' },
  { schoolName: '南部中学校', schoolId: '10003' },
  { schoolName: '北部中学校', schoolId: '10004' },
  { schoolName: '中部中学校', schoolId: '10005' },
  { schoolName: '八幡中学校', schoolId: '10006' },
  { schoolName: '曳馬中学校', schoolId: '10007' },
  { schoolName: '新津中学校', schoolId: '10008' },
  { schoolName: '江西中学校', schoolId: '10009' },
  { schoolName: '蜆塚中学校', schoolId: '10010' },
  { schoolName: '天竜中学校', schoolId: '10011' },
  { schoolName: '与進中学校', schoolId: '10012' },
  { schoolName: '笠井中学校', schoolId: '10013' },
  { schoolName: '南陽中学校', schoolId: '10014' },
  { schoolName: '北星中学校', schoolId: '10015' },
  { schoolName: '都田中学校', schoolId: '10016' },
  { schoolName: '神久呂中学校', schoolId: '10017' },
  { schoolName: '入野中学校', schoolId: '10018' },
  { schoolName: '積志中学校', schoolId: '10019' },
  { schoolName: '湖東中学校', schoolId: '10020' },
  { schoolName: '篠原中学校', schoolId: '10021' },
  { schoolName: '丸塚中学校', schoolId: '10022' },
  { schoolName: '高台中学校', schoolId: '10023' },
  { schoolName: '庄内中学校', schoolId: '10024' },
  { schoolName: '江南中学校', schoolId: '10025' },
  { schoolName: '開成中学校', schoolId: '10026' },
  { schoolName: '中郡中学校', schoolId: '10027' },
  { schoolName: '三方原中学校', schoolId: '10028' },
  { schoolName: '東陽中学校', schoolId: '10029' },
  { schoolName: '佐鳴台中学校', schoolId: '10030' },
  { schoolName: '富塚中学校', schoolId: '10031' },
  { schoolName: '可美中学校', schoolId: '10032' },
  { schoolName: '萩原分校', schoolId: '10033' },
  { schoolName: '舞阪中学校', schoolId: '10034' },
  { schoolName: '雄踏中学校', schoolId: '10035' },
  { schoolName: '浜名中学校', schoolId: '10036' },
  { schoolName: '北浜中学校', schoolId: '10037' },
  { schoolName: '浜北北部中学校', schoolId: '10038' },
  { schoolName: '麁玉中学校', schoolId: '10039' },
  { schoolName: '北浜東部中学校', schoolId: '10040' },
  { schoolName: '清竜中学校', schoolId: '10041' },
  { schoolName: '光が丘中学校', schoolId: '10042' },
  { schoolName: '春野中学校', schoolId: '10043' },
  { schoolName: '水窪中学校', schoolId: '10044' },
  { schoolName: '細江中学校', schoolId: '10045' },
  { schoolName: '引佐南部中学校', schoolId: '10046' },
  { schoolName: '引佐北部中学校', schoolId: '10047' },
  { schoolName: '三ヶ日中学校', schoolId: '10048' },
  { schoolName: '佐久間中学校', schoolId: '10049' },
  { schoolName: '西小学校', schoolId: '20001' },
  { schoolName: '東小学校', schoolId: '20002' },
  { schoolName: '県居小学校', schoolId: '20003' },
  { schoolName: '相生小学校', schoolId: '20004' },
  { schoolName: '竜禅寺小学校', schoolId: '20005' },
  { schoolName: '追分小学校', schoolId: '20006' },
  { schoolName: '佐藤小学校', schoolId: '20007' },
  { schoolName: '広沢小学校', schoolId: '20008' },
  { schoolName: '曳馬小学校', schoolId: '20009' },
  { schoolName: '萩丘小学校', schoolId: '20010' },
  { schoolName: '富塚小学校', schoolId: '20011' },
  { schoolName: '白脇小学校', schoolId: '20012' },
  { schoolName: '蒲小学校', schoolId: '20013' },
  { schoolName: '浅間小学校', schoolId: '20014' },
  { schoolName: '上島小学校', schoolId: '20015' },
  { schoolName: '鴨江小学校', schoolId: '20016' },
  { schoolName: '新津小学校', schoolId: '20017' },
  { schoolName: '河輪小学校', schoolId: '20018' },
  { schoolName: '船越小学校', schoolId: '20019' },
  { schoolName: '城北小学校', schoolId: '20020' },
  { schoolName: '和田小学校', schoolId: '20021' },
  { schoolName: '与進小学校', schoolId: '20022' },
  { schoolName: '豊西小学校', schoolId: '20023' },
  { schoolName: '笠井小学校', schoolId: '20024' },
  { schoolName: '中ノ町小学校', schoolId: '20025' },
  { schoolName: '芳川小学校', schoolId: '20026' },
  { schoolName: '飯田小学校', schoolId: '20027' },
  { schoolName: '花川小学校', schoolId: '20028' },
  { schoolName: '三方原小学校', schoolId: '20029' },
  { schoolName: '豊岡小学校', schoolId: '20030' },
  { schoolName: '都田小学校', schoolId: '20031' },
  { schoolName: '神久呂小学校', schoolId: '20032' },
  { schoolName: '入野小学校', schoolId: '20033' },
  { schoolName: '積志小学校', schoolId: '20034' },
  { schoolName: '伊佐見小学校', schoolId: '20035' },
  { schoolName: '和地小学校', schoolId: '20036' },
  { schoolName: '都田南小学校', schoolId: '20037' },
  { schoolName: '篠原小学校', schoolId: '20038' },
  { schoolName: '葵が丘小学校', schoolId: '20039' },
  { schoolName: '村櫛小学校', schoolId: '20040' },
  { schoolName: '泉小学校', schoolId: '20041' },
  { schoolName: '大瀬小学校', schoolId: '20042' },
  { schoolName: '砂丘小学校', schoolId: '20043' },
  { schoolName: '中郡小学校', schoolId: '20044' },
  { schoolName: '与進北小学校', schoolId: '20045' },
  { schoolName: '佐鳴台小学校', schoolId: '20046' },
  { schoolName: '瑞穂小学校', schoolId: '20047' },
  { schoolName: '富塚西小学校', schoolId: '20048' },
  { schoolName: '芳川北小学校', schoolId: '20049' },
  { schoolName: '有玉小学校', schoolId: '20050' },
  { schoolName: '初生小学校', schoolId: '20051' },
  { schoolName: '西都台小学校', schoolId: '20052' },
  { schoolName: '和田東小学校', schoolId: '20053' },
  { schoolName: '葵西小学校', schoolId: '20054' },
  { schoolName: '可美小学校', schoolId: '20055' },
  { schoolName: '大平台小学校', schoolId: '20056' },
  { schoolName: '萩原分校', schoolId: '20057' },
  { schoolName: '舞阪小学校', schoolId: '20058' },
  { schoolName: '雄踏小学校', schoolId: '20059' },
  { schoolName: '浜名小学校', schoolId: '20060' },
  { schoolName: '北浜小学校', schoolId: '20061' },
  { schoolName: '北浜東小学校', schoolId: '20062' },
  { schoolName: '中瀬小学校', schoolId: '20063' },
  { schoolName: '赤佐小学校', schoolId: '20064' },
  { schoolName: '麁玉小学校', schoolId: '20065' },
  { schoolName: '新原小学校', schoolId: '20066' },
  { schoolName: '北浜北小学校', schoolId: '20067' },
  { schoolName: '内野小学校', schoolId: '20068' },
  { schoolName: '北浜南小学校', schoolId: '20069' },
  { schoolName: '伎倍小学校', schoolId: '20070' },
  { schoolName: '二俣小学校', schoolId: '20071' },
  { schoolName: '光明小学校', schoolId: '20072' },
  { schoolName: '上阿多古小学校', schoolId: '20073' },
  { schoolName: '下阿多古小学校', schoolId: '20074' },
  { schoolName: '熊小学校', schoolId: '20075' },
  { schoolName: '横山小学校', schoolId: '20076' },
  { schoolName: '犬居小学校', schoolId: '20077' },
  { schoolName: '気田小学校', schoolId: '20078' },
  { schoolName: '佐久間小学校', schoolId: '20079' },
  { schoolName: '浦川小学校', schoolId: '20080' },
  { schoolName: '水窪小学校', schoolId: '20081' },
  { schoolName: '気賀小学校', schoolId: '20082' },
  { schoolName: '西気賀小学校', schoolId: '20083' },
  { schoolName: '伊目小学校', schoolId: '20084' },
  { schoolName: '中川小学校', schoolId: '20085' },
  { schoolName: '井伊谷小学校', schoolId: '20086' },
  { schoolName: '金指小学校', schoolId: '20087' },
  { schoolName: '奥山小学校', schoolId: '20088' },
  { schoolName: '三ヶ日東小学校', schoolId: '20089' },
  { schoolName: '三ヶ日西小学校', schoolId: '20090' },
  { schoolName: '平山小学校', schoolId: '20091' },
  { schoolName: '尾奈小学校', schoolId: '20092' },
  { schoolName: '双葉小学校', schoolId: '20093' },
  { schoolName: '引佐北部小学校', schoolId: '20094' },
  { schoolName: '南の星小学校', schoolId: '20095' },
  { schoolName: '庄内小学校', schoolId: '20096' },
  { schoolName: '中部小学校', schoolId: '20097' },
];

export const useConstants = () => {
  return { chatTypes, genderOptions, ageOptions, featureList, SCHOOL_LIST };
};
