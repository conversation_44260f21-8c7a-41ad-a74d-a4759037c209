import {
  CounselorR<PERSON>,
  ReportExportType,
  ReportExportStatus,
  SurveyStatus,
} from "./enums";
export interface TableColumn {
  key: string;
  label?: string;
  sortable?: boolean;
  direction?: "asc" | "desc";
  class?: string;
}

export interface CaseStatistics {
  counseleesCount: number;
  openCount: number;
  inProgressCount: number;
  waitingCount: number;
}

export interface AuthLoginPayload {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface User {
  counselorId: string;
  role?: CounselorRole;
  profileImage?: string;
  email?: string;
  fullName?: string;
  organizationName?: string;
  loggedInAt?: string;
  createdAt?: string;
  updatedAt?: string;
  customers: {
    customerId: string;
    role: string;
  }[];
}

export interface CommonResponse {
  error: string | null;
}

export interface PaginationResponse<T> {
  items: T[];
  totalCount: number;
  page: number;
  error: string | null;
}

export interface SampleMessage extends MetaColumn {
  customerId?: string;
  textTemplateId?: string;
  type?: "personal" | "common";
  title: string;
  group: string;
  text: string;
}

export interface CaseTag extends MetaColumn {
  tagId?: string;
  tagName: string;
  formType: string;
  options: string[];
  customerId?: string;
}

export interface CaseTagsResponse {
  caseTags: CaseTag[];
  total: number;
}

export interface ReportExport {
  exportId: string;
  targetCounseleeId?: string;
  outputColumnNames: string[];
  surveys?: {
    surveyId: string;
    surveyName: string;
    outputColumns: string[];
  }[],
  exportType: ReportExportType;
  exportUrl: string;
  customerId: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  status?: ReportExportStatus;
}

export interface SurveyFormElement {
  _id: string;
  id: string;
  type: string;
  title: string;
  description?: string;
  help?: string;
  required: boolean;
  values?: string[];
  defaultValue?: string | string[];
  minItems?: string;
  maxItems?: string;
  tags?: string[];
  value?: string | string[];
  hasOtherOption?: boolean;
  otherOption?: string;
}

export interface WizardFormElement {
  _id: string;
  id: string;
  type: string;
  title?: string;
  description?: string;
  help?: string;
  required?: boolean;
  values?: any[];
  defaultValue?: string | string[];
  minItems?: string;
  maxItems?: string;
  tags?: string[];
  value?: string | string[];
  hasOtherOption?: boolean;
  otherOption?: string;
  step: number;
  valueClass?: string;
  buttons?: any[];
  img?: string;
  placeholder?: string;
}
export interface Wizard extends MetaColumn {
  wizardId?: string;
  wizardName?: string;
  wizard: WizardFormElement[];
  customerId?: string;
}

export interface SurveyResult {
  surveyId: string;
  surveyName: string;
  createdAt: string;
  counseleeId: string;
  counseleeName: string;
  counseleeAvatar: string;
  formTemplate: SurveyFormElement[];
  answer: any;
  segmentId?: string;
  segmentName?: string;
  segmentStatus?: string;
}

export interface OpenRange {
  hours: number;
  minutes: number;
}

export interface CounselingTermDay {
  key: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  open: {
    startTime: string;
    endTime: string;
  }[];
  isOpen: boolean;
  openRanges: OpenRange[][]; // [[{hours: 9, minutes: 0}, {hours: 12, minutes: 0}], [{hours: 13, minutes: 0}, {hours: 18, minutes: 0}]
}

export interface CounselingTermTarget {
  date: string[];
  term: {
    startTime: string;
    endTime: string;
  }[];
  isOpen: boolean;
}

export interface CounselingTerm extends MetaColumn {
  counselingTermId?: string;
  day: CounselingTermDay;
  target: CounselingTermTarget;
}

export interface SurveyResultsSearchConditions {
  surveyId: string[] | string;
  counseleeId: string[] | string;
  startDate: string; // yyyy-MM-dd
  endDate: string; // yyyy-MM-dd
}

export type WizardActionType =
  | "login"
  | "submit"
  | "sentMail"
  | "step"
  | number;

export interface WizardElementButton {
  text: string;
  next: {
    action: WizardActionType;
    value?: any;
  };
  icon?: string;
  isPrev?: boolean;
  isNext?: boolean;
  color?: string;
  trailing?: boolean;
}
export interface WizardElement {
  _id: string;
  id: string;
  type: string;
  title: string;
  description?: string;
  help?: string;
  required: boolean;
  values?: any[];
  defaultValue?: string | string[];
  value?: string | string[];
  hasOtherOption?: boolean;
  otherOption?: string;
  img?: string;
  valueClass?: string;
  placeholder?: string;
  buttons: WizardElementButton[];
}

export interface WizardResult {
  wizardId: string;
  wizardName: string;
  createdAt: string;
  counseleeId: string;
  counseleeName: string;
  counseleeAvatar: string;
  formTemplate: SurveyFormElement[];
  wizardResult: any;
}

export interface WizardResultsSearchConditions {
  wizardId: string[] | string;
  counseleeId: string[] | string;
  startDate: string; // yyyy-MM-dd
  endDate: string; // yyyy-MM-dd
}

// End Template interfaces
export interface EndTemplateChoice {
  id: string;
  text: string; // 選択肢として表示する内容（全角150文字以内）
  responseMessage?: string; // 選択肢が選ばれたときに返すメッセージ（任意）
  openOneOnOneTalk: boolean; // 1:1トークを開設するかどうか
}

export interface EndTemplate extends MetaColumn {
  id: string;
  templateName: string; // 終了テンプレート名（管理項目用、ユーザーには表示されない）
  finalQuestion: string; // 最終設問（100文字以内）
  choices: EndTemplateChoice[]; // 選択肢（最大4つ）
  isActive: boolean;
  customerId?: string;
}

// Scenario interfaces
export type ScenarioActionType =
  | 'show_end_survey'      // 終了アンケートを表示する
  | 'select_end_template'  // 終了テンプレートを選択
  | 'open_talk'           // トークを開設する
  | 'next_question'       // 次の設問に進む
  | 'end_scenario';       // 終了する

export interface ScenarioAction {
  type: ScenarioActionType;
  value?: string; // For end template ID, next question ID, etc.
  endTemplateId?: string; // When type is 'select_end_template'
  nextQuestionId?: string; // When type is 'next_question'
}

export interface ScenarioChoice {
  id: string;
  text: string; // 選択肢として表示する内容（全角150文字以内）
  responseMessage?: string; // 選択肢が選ばれたときに返すメッセージ（任意）
  action: ScenarioAction; // 選択肢押下時の挙動
}

export interface ScenarioQuestion {
  id: string;
  text: string; // 設問内容（100文字以内）
  choices: ScenarioChoice[]; // 選択肢（最大4つ）
  isFirstQuestion: boolean; // 最初の設問かどうか
  order: number; // 設問の順序
}

export interface Scenario extends MetaColumn {
  id: string;
  name: string; // シナリオ名
  description?: string; // シナリオの説明
  questions: ScenarioQuestion[]; // 設問リスト
  isActive: boolean;
  customerId?: string;
}