// global types
export {};
import {
  CaseStatus,
  CasePriority,
  ConsultantRole,
  SNSChannel,
} from "~/types/enums.d";

declare module "vue-number-animation";
declare module "splitpanes";
declare module "click-outside-vue3";
declare module "nuxt-permissions";
declare global {
  interface LoadingDynamic {
    [key: string]: any | LoadingDynamic;
  }
  interface ErrorDynamic {
    [key: string]: any | ErrorDynamic;
  }

  interface Pagination {
    page: number;
    pageRangeDisplayed: number;
  }

  interface Sort {
    sortBy: string;
    sortDesc: boolean;
  }
  // 案件
  interface Project {
    id: number;
    name: string;
    avatar?: string;
    messagesCount?: number;
    theme?: string;
  }
  interface ProjectColors {
    [key: string]: {
      primary: string;
      gray: string;
      darkMode: boolean;
    };
  }

  // ケース
  interface Case {
    id: number;
    assigneeName: string;
    status: CaseStatus;
    elapsedTimeBeforeStart: string;
    elapsedTimeInProgress: string;
    latestPostTime: string;
    clientName: string;
    userId: string;
    times: number;
    risk: CasePriority;
    channel?: SNSChannel;
    userAvatar?: string;
    memos?: string[];
    chat: any[];
    caseConveyed?: string;
    selectedTags?: any[];
    customerId?: string;
  }

  // 相談員
  interface Consultant {
    id: number;
    name: string;
    avatar?: string | object;
    numberOfActiveCases?: number;
    online?: boolean;
    role?: ConsultantRole;
    email?: string;
    organizationName?: string;
    fullName?: string;
    counselorId?: string;
    customers: Customer[];
    cases: Case[];
  }

  interface ButtonGroup {
    value: string | number;
    label: string;
  }

  interface TagFormTypeElement {
    [key: string]: Component;
  }

  interface CustomerSetting {
    watchWords?: string[];
  }

  interface WorkingDayTime {
    key: string;
    startTime: string;
    endTime: string;
  }

  interface SettingsAutoMessage {
    isEnable: string;
    message: string;
    icon: string;
    title: string;
    hasSurvey?: boolean;
  }

  interface SettingsChatType {
    icon?: string;
    icons?: string[];
    title: string;
    iconColor: string;
    description: string;
  }

  interface FormElements {
    [key: string]: any;
  }

  interface MetaColumn {
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string;
    createdBy?: string;
    updatedBy?: string;
    deletedBy?: string;
  }

  interface SendJsonSocket {
    action: string;
    type: string;
    payload: any;
  }

  interface WizardElement {
    [key: string]: any;
  }
}
