import type { Customer } from "./customers";
import type { CounselorAccountR<PERSON>, CounselorDetailRole } from "./enums";

export interface CustomerRole {
  id: string;
  name: string;
  role: CounselorDetailRole;
}

export interface Counselor extends MetaColumn {
  counselorId: string;
  organizationName: string;
  fullName: string;
  role: CounselorAccountRole;
  loggedInAt?: string;
  customerRoles: CustomerRole[];
  email: string;
  profileImage: string;
  customerRole?: string;
  needReissueTempPassword?: boolean;
  isAdmin?: boolean;
}

export interface CounselorWithCustomersResponse {
  counselor: Counselor;
  customers: Customer[];
}

export interface CounselorListResponse {
  counselors: Counselor[];
  total: number;
}

export interface AppCounselor extends Counselor {
  isOnline?: boolean;
  cases?: Case[];
  customers?: Customer[];
  hasCaseCount?: number;
  selectedCustomer?: any;
}