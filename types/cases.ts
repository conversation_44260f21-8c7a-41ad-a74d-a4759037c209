import {
  CaseStatus,
  CaseRisk,
  SNSChannel,
  Gender,
  AgeDecade,
} from "~/types/enums.d";

export interface CaseSearchConditions {
  caseId: string;
  customerId: string;
  counseleeId: string;
  counselorInChargeId: string[];
  startDate: string; // yyyy-MM-dd
  endDate: string; // yyyy-MM-dd
  onlyCaseInCharge: string; // true/false
  caseStatus: CaseStatus[]; // before_start, open, etc...
  includeBeforeStart: string; // true/false
  includeResolved: string; // true/false
  includeCancelled: string; // true/false
  freeKeyword: string;
}

export interface CaseChatMessage extends MetaColumn {
  chatId: string;
  content: {
    text: string;
  };
  sender: "counselor" | "counselee" | "chatBot";
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  survey?: {
    surveyId: string;
    surveyName: string;
  };

  // for FE only
  caseId?: string;
  customerId?: string;
  isSending?: boolean;
  error?: string;
}
// ケース
export interface Case extends MetaColumn {
  caseId: string;
  conveyed: string;
  channel: SNSChannel;
  counseleeId: string;
  counseleeImage: string;
  counseleeName: string;
  counselorInChargeId: string;
  counselorInChargeImage: string;
  counselorInChargeName: string;
  count: number;
  customerId: string;
  elapsedTimeBeforeStart: string;
  elapsedTimeInProgress: string;
  endTime: string;
  latestPostTime: string;
  memos: string[];
  order: number;
  risk: CaseRisk;
  startTime: string;
  status: CaseStatus;
  chat?: CaseChatMessage[];
  gender?: Gender;
  ageDecade?: AgeDecade;
  selectedTags?: any[];
  surveyResults?: any[];
  wizardResults?: any[];
  // for FE only
  messageText?: string;
  selectedSurvey?: any;
  histories?: Case[];
  currentHistory?: Case | null;
  chatExtendActiveTab?: number;
  unReadMessages?: CaseChatMessage[];
  currentSurveyResult?: any;
  currentWizardResult?: any;
  isBlock: boolean;
}
