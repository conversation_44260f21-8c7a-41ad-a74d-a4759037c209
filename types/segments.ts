import type { Customer } from "./customers";
import type { Gender, AgeDecade, CaseStatus } from "./enums";

export interface SegmentDelivery {
  segmentName: string;
  content: { text: string };
  filter: SegmentDeliveryFilter;
  survey?: {
    surveyId: string;
    surveyName: string;
  };
}

export interface SegmentDeliveryFilter {
  startDate: string | null;
  endDate: string | null;
  statuses: CaseStatus[] | null | undefined | [];
  ageDecades: AgeDecade[] | null | undefined | [];
  genders: Gender[] | null | undefined | [];
}

export interface SegmentDeliverySearchConditions {
  segmentId: string,
  startDate: string; // yyyy-MM-dd
  endDate: string; // yyyy-MM-dd
  includeBeforeStart: boolean;
  includeInProgress: boolean; // 含む場合true
  includeResolved: boolean; // 含む場合true
  includeError: boolean; // 含む場合true
  segmentName?: string;
}