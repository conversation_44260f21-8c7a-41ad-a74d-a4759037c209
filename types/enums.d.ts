export enum CaseStatus {
  BEFORE_START = "before_start", // 開始前
  OPEN = "open", // 開始前
  IN_PROGRESS = "in_progress", // 対応中 (要返信)
  WAITING = "waiting", // 待機中(返信済)
  RESOLVED = "resolved", // 対応済
  CANCELLED = "cancelled", // 無効
}
//TODO: replace with CaseRisk, delete this later
export enum CasePriority {
  HIGH = "high",
  NORMAL = "normal",
  LOW = "low",
}
export enum CaseRisk {
  HIGH = "high",
  NORMAL = "normal",
  LOW = "low",
}
export enum ConsultantRole {
  SUPERVISOR = "supervisor",
  GENERAL = "general",
}

export enum SNSChannel {
  LINE = "line",
  FACEBOOK = "facebook",
  APPLICATION = "application",
}

export enum CounselorRole {
  ADMIN = "admin",
  SUPERVISOR = "supervisor",
  GENERAL = "general",
  VIEWER = "viewer",
}

export enum CounselorAccountRole {
  ADMIN = "admin",
  COUNSELOR = "counselor",
}

export enum CounselorDetailRole {
  SUPERVISOR = "supervisor",
  GENERAL = "general",
  VIEWER = "viewer",
}

export enum SampleMessageType {
  COMMON = "common",
  PERSONAL = "personal",
}

export enum TagFormType {
  TEXT = "text",
  PULLDOWN = "pulldown",
  TEXT_AND_PULLDOWN = "text_and_pulldown",
  RADIO = "radio",
  CHECKBOX = "checkbox",
}

export enum ReportExportType {
  CASE = "case",
  CHAT = "chat",
  TOTALLING = "totalling",
  COUNSELEE = "counselee",
}

export enum ReportExportStatus {
  NOW_CREATING = "now_creating", // 作成中
  DOWNLOADABLE = "downloadable", // ダウンロード可能
  ERROR = "error", // エラー
}

export enum SurveyStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum LineChannel {
  LOGIN = "login",
  MESSAGING = "messaging",
}

export enum Gender {
  Male = "male",
  Female = "female",
  Other = "other",
}

export enum AgeDecade {
  Teens = "teens",
  Twenties = "twenties",
  Thirties = "thirties",
  Forties = "forties",
  Fifties = "fifties",
  Sixties = "sixties",
  Seventies = "seventies",
  EightiesAndOlder = "eightiesAndOlder",
}

export enum SegmentStatus {
  BEFORE_START = "before_start", // 未対応
  IN_PROGRESS = "in_progress", // 送信中
  RESOLVED = "resolved", // 送信完了
  ERROR = "error", // エラー
}

export enum CustomerFeature {
  TextTemplate = "TextTemplate",
  Case = "Case",
  Tag = "Tag",
  Counselee = "Counselee",
  CounselingTerm = "CounselingTerm",
  Segment = "Segment",
  Export = "Export",
  Survey = "Survey",
  SurveyResult = "SurveyResult",
  WizardResult = "WizardResult",
  Wizard = "Wizard",
  Chatbot = "Chatbot",
}

export type FeatureList = CustomerFeature;
