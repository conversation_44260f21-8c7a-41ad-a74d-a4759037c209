import type { CaseChatMessage } from "~/types";
import type { CounselorDetailRole, FeatureList } from "./enums";

export enum SNS_CHANNELS {
  FACEBOOK = "facebook",
  LINE = "line",
  APPLICATION = "application",
}

export interface Customer extends MetaColumn {
  customerId?: string;
  activeChannels?: SNS_CHANNELS[];
  counseleeCount?: number;
  basic: {
    customerImage: string;
    customerName: string;
  };
  slug: string;
  line: {
    isActive: boolean;
    chatType: string;
    login?: {
      channelId: string;
      secret: string;
      liffId: string;
    };
    messaging: {
      channelId: string;
      secret: string;
      accessToken: string;
    };
  };
  application: {
    isActive: boolean;
    accessCode: string;
    browserAccessCode: string;
    showBrowserAccessCode: boolean;
  };
  facebook: {
    isActive: boolean;
    pageId: string;
    pageAccessToken: string;
  };
  counseleeLimit: number;
  ipWhiteLists: string[];
  theme?: string;
  setting?: CustomerSetting;
  roles?: CounselorRoles[];
  startDate: Date;
  endDate: Date;
  contractedLines: number;
  openCaseCount?: number;
  featureList?: FeatureList[];
  chatbot?: ChatbotConfig;
  // for FE only
  unReadMessages?: CaseChatMessage[];
}

export interface CounselorRoles {
  counselorId: string;
  counselorRole: CounselorDetailRole;
}

export interface CustomerAccountListResponse {
  customerAccounts: Customer[];
  total: number;
}
export type ChatType = "liff" | "browser" | "line";

export interface ChatbotConfig {
  name: string;
  welcomeMessage: string;
  autoResponse: boolean;
  responseDelay: number;
  offHoursMessage?: string;
  keywordResponses?: Array<{
    keyword: string;
    response: string;
  }>;
}

export interface CustomerSetting {
  followWord: string;
  useFollowWord: boolean;
  beforeSurveyWord: string;
  useBeforeSurveyWord: boolean;
  beforeWorkTimeWord: string;
  useBeforeWorkTimeWord: boolean;
  notStartedWord: string;
  useNotStartedWord: boolean;
  crowdedWord: string;
  useCrowdedWord: boolean;
  afterWorkTimeWord: string;
  useAfterWorkTimeWord: boolean;
  closeDayWord: string;
  useCloseDayWord: boolean;
  notTextMessageWord: string;
  useNotTextMessageWord: boolean;
  useWebView: boolean;
  watchWords: string[];
  survey?: {
    surveyId: string;
    surveyName: string;
  };
}

export type RequestCustomer = Omit<Customer, "basic"> & {
  basic: {
    customerImage?: File;
    customerName: string;
  };
};
