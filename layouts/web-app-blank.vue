<template>
  <section
    class="bg-cover bg-no-repeat bg-[url('~/assets/images/bg.jpg')] dark:bg-blend-multiply dark:bg-primary-800"
  >
    <div
      class="flex flex-col items-center justify-center px-6 py-8 mx-auto h-screen lg:py-0"
    >
      <div
        v-if="loadings.fetchCustomerInfo"
        class="px-0 h-full w-full pt-20 pb-16 bg-black/25 flex flex-col space-y-6 items-center justify-center"
      >
        <BaseLoader />
        <div class="text-gray-200 text-sm font-light">少々お待ちください</div>
      </div>
      <slot v-else />
      <BaseCopyright
        class="absolute bottom-0 flex flex-row items-center justify-center bg-gray-50/50"
        textClass="text-black"
      />
    </div>
  </section>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useWebAppStore } from "~/stores/web-app";

  const webAppStore = useWebAppStore();

  const { loadings, customer } = storeToRefs(webAppStore);

  onMounted(() => {
    // webAppStore.fetchCustomerInfo();
    const appConfig = useAppConfig();
    const colorMode = useColorMode();
    appConfig.ui.primary = "cyan";
    colorMode.preference = "light";
  });
</script>
