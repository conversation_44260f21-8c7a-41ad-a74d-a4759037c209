<template>
  <section class="bg-slate-100">
    <div class="mx-auto min-h-screen">
      <div class="bg-white">
        <div
          class="w-full max-w-screen-xl px-3 mx-auto py-2 border-b flex flex-row items-center justify-between"
        >
          <div class="flex flex-row space-x-1 items-center">
            <UAvatar
              v-bind="{
                src: customer?.avatar,
                alt: customer?.name,
              }"
              size="lg"
              loading="lazy"
              :ui="{
                rounded: 'rounded-lg',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
              }"
            />
            <div class="text-xl text-primary-500">
              {{ $t(customer?.name || "") }}
            </div>
          </div>
          <UDropdown
            v-model="selectedLanguage"
            :items="languageOptions"
            :ui="{ item: { disabled: 'cursor-text select-text' } }"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              color="primary"
              trailing-icon="i-heroicons-chevron-down-20-solid"
              variant="soft"
              :ui="{
                rounded: 'rounded-full',
              }"
            >
              <template #leading>
                <UIcon
                  :name="selectedLanguageObj?.icon"
                  class="flex-shrink-0 h-5 w-5 text-gray-400 dark:text-gray-500 ms-auto"
                />
              </template>
              <span>
                {{ selectedLanguageObj?.label }}
              </span>
            </UButton>

            <template #account="{ item }">
              <div class="text-left">
                <p>Signed in as</p>
                <p class="truncate font-medium text-gray-900 dark:text-white">
                  {{ item.label }}
                </p>
              </div>
            </template>

            <template #item="{ item }">
              <span class="truncate">{{ item.label }}</span>

              <UIcon
                :name="item.icon"
                class="flex-shrink-0 h-5 w-5 text-gray-400 dark:text-gray-500 ms-auto"
              />
            </template>
          </UDropdown>
        </div>
      </div>

      <slot />
    </div>
  </section>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useWebAppStore } from "~/stores/web-app";

  const wizardStore = useWizardStore();
  const { selectedLanguage, loadings, customer } = storeToRefs(wizardStore);

  onMounted(() => {
    const appConfig = useAppConfig();
    const colorMode = useColorMode();
    appConfig.ui.primary = "cyan";
    colorMode.preference = "light";
  });

  const selectedLanguageObj = computed(() => {
    return languageOptions[0].find(
      (option) => option.value === selectedLanguage.value,
    );
  });

  const languageOptions = [
    [
      {
        label: "日本語",
        value: "ja",
        icon: "i-circle-flags-ja",
        click: () => (selectedLanguage.value = "ja"),
      },
      {
        label: "English",
        value: "en",
        icon: "i-circle-flags-uk",
        click: () => (selectedLanguage.value = "en"),
      },
      {
        label: "Português",
        value: "pt",
        icon: "i-circle-flags-pt",
        click: () => (selectedLanguage.value = "pt"),
      },
      {
        label: "やさしい日本語",
        value: "easy-ja",
        icon: "i-circle-flags-ja",
        click: () => (selectedLanguage.value = "easy-ja"),
      },
    ],
  ];

  const { locale } = useI18n();
  watch(
    () => selectedLanguage.value,
    (value) => {
      console.log("🚀 ~ value:", value);
      locale.value = value;
    },
  );
  const route = useRoute();
  onMounted(() => {
    const { lang } = route.query;
    if (lang) {
      selectedLanguage.value = lang as string;
    }

    locale.value = selectedLanguage.value;
  });
</script>
