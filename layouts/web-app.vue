<template>
  <section
    class="bg-cover bg-no-repeat bg-[url('~/assets/images/bg.jpg')] dark:bg-blend-multiply dark:bg-primary-800"
  >
    <div
      class="flex flex-col items-center px-0 py-0 mx-auto h-screen lg:pt-8 transition-all duration-300"
    >
      <div
        v-if="loadings.fetchCustomerInfo"
        class="px-10 h-full w-full max-w-xl pt-20 pb-16 bg-black/25 flex flex-col space-y-6 items-center justify-center"
      >
        <BaseLoader />
        <div class="text-gray-200 text-sm font-light">少々お待ちください</div>
      </div>
      <slot v-else />
    </div>
  </section>
</template>

<script setup lang="ts">
  const webAppStore = useWebAppStore();
  const { loadings, customer } = storeToRefs(webAppStore);
  onMounted(() => {
    // webAppStore.fetchCustomerInfo();
    const appConfig = useAppConfig();
    const colorMode = useColorMode();
    appConfig.ui.primary = "cyan";
    colorMode.preference = "light";
  });
</script>
