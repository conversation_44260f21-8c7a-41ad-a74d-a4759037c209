<template>
  <div class="bg-gray-50 dark:bg-gray-900">
    <AppCustomersMenu />
    <template v-if="!loadings['fetchCustomers'] && customers.length">
      <AppNavigation />
      <main
        v-if="!loadings['fetchCustomers'] && customers.length"
        class="relative h-auto min-h-screen bg-gray-100 dark:bg-gray-800 transition-all duration-200"
        :class="{
          'ml-5': isHideAppSubNavigation && isHideAppNavigation,
          'ml-20': isHideAppNavigation,
          'ml-44': !isHideAppNavigation && !isHideAppSubNavigation,
          'ml-[7.2rem]': isHideAppSubNavigation && !isHideAppNavigation,
        }"
      >
        <slot />
      </main>
    </template>
    <div v-else>
      <div v-if="!loadings['fetchCustomers'] && !customers.length">
        <div
          v-if="errors['fetchCustomers']"
          class="flex justify-center items-center h-screen"
        >
          <div class="text-center">
            <ErrorSvg class="w-2/4 mx-auto" />
            <div class="text-xl font-semibold mb-2 mt-4">
              システムエラーが発生しました。
            </div>
            <div class="text-gray-500">
              大変お手数ですが、サイト管理者までご連絡ください。
            </div>
          </div>
        </div>
        <div
          v-else
          class="relative flex justify-center items-center h-screen dark:bg-gray-800"
        >
          <div class="flex mb-4 absolute top-8 right-16">
            <SystemLogo />
          </div>
          <div
            class="flex flex-col justify-start items-center gap-4 text-gray-500"
          >
            <div class="flex flex-row items-end gap-2">
              <WelcomeSvg class="h-11 mx-auto" />
              <div class="text-4xl font-light">
                {{ $t("system_name") }}へようこそ！
              </div>
            </div>
            <p>システムを設定中です。しばらくお待ちください</p>
            <div class="pt-6">
              <UButton
                icon="i-tabler-refresh"
                size="lg"
                color="gray"
                variant="solid"
                label="再読み込み"
                :trailing="false"
                :ui="{
                  rounded: 'rounded-full',
                }"
                class="px-10"
                @click="onReload"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useCasesStore } from "~/stores/app/cases";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import WelcomeSvg from "~/assets/images/welcome.svg";
  import ErrorSvg from "~/assets/images/error.svg";
  const appCustomersStore = useAppCustomersStore();
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
  const casesStore = useCasesStore();
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini, isHideAppNavigation, isHideAppSubNavigation } =
    storeToRefs(appUIStore);
  const { customers, loadings, errors } = storeToRefs(appCustomersStore);
  onMounted(() => {
    // casesStore.fetchCases();
  });

  const onReload = () => {
    window.location.reload();
  };
</script>
