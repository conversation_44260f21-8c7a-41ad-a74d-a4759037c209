<template>
  <div class="bg-gray-50 dark:bg-gray-900">
    <div
      v-if="loadings.fetchCustomerInfo"
      class="px-10 h-screen w-screen max-w-xl mx-auto pt-20 pb-16 flex flex-col space-y-3 items-center justify-center"
    >
      <Icon icon="eos-icons:loading" class="text-5xl text-gray-500" />
      <div class="text-sm text-gray-500">少々お待ちください</div>
    </div>
    <slot v-else />
  </div>
</template>

<script lang="ts" setup>
  import { Icon } from "@iconify/vue";
  import { storeToRefs } from "pinia";
  import { useSurveyStore } from "~/stores/survey";

  const props = defineProps<{
    class?: string;
  }>();
  const surveyStore = useSurveyStore();
  const { loadings } = storeToRefs(surveyStore);
  const route = useRoute();
  onMounted(async () => {
    let customerId = route.query["c"];
    const customer = await surveyStore.fetchCustomerInfo(customerId as string);
  });
</script>
