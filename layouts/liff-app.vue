<template>
  <section
    class="bg-cover bg-no-repeat bg-[url('~/assets/images/bg.jpg')] dark:bg-blend-multiply dark:bg-primary-800"
  >
    <div
      class="flex flex-col items-center px-0 py-0 mx-auto h-screen lg:pt-8 transition-all duration-300"
      :class="props.class"
    >
      <div
        v-if="loadings.fetchCustomerInfo"
        class="px-10 h-full w-full max-w-xl pt-20 pb-16 bg-black/25 flex flex-col space-y-6 items-center justify-center"
      >
        <BaseLoader />
        <div class="text-gray-200 text-sm font-light">少々お待ちください</div>
      </div>
      <div
        class="px-10 h-full w-full max-w-xl pt-20 pb-16 bg-black/80 flex flex-col space-y-2 items-center justify-center"
        v-else-if="errors['liff'] || errors['fetchCustomerInfo']"
      >
        <Icon icon="material-symbols:error" class="text-6xl text-red-500" />
        <div class="text-red-500">
          {{ $t(errors["liff"] || errors["fetchCustomerInfo"]) }}
        </div>
        <div class="text-red-500">
          管理者にお問い合わせください
        </div>
      </div>
      
      <slot v-else />
    </div>
  </section>
</template>

<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import { useLiffAppStore } from "~/stores/liff-app";
  import { Icon } from "@iconify/vue";
  const props = defineProps<{
    class?: string;
  }>();
  const liffAppStore = useLiffAppStore();
  const { init, liff, profile, relogin } = useLiff();
  const { loadings, lineUser, errors } = storeToRefs(liffAppStore);
  const route = useRoute();
  const socketStore = useSocketStore();
  const runtimeConfig = useRuntimeConfig();

  onMounted(async () => {
    const liffState = route.query["liff.state"];
    let cLiffState: string = "";
    // parse URL from liffState
    if (liffState) {
      cLiffState = liffState.toString().split("c=")[1];
      localStorage.setItem("liffCustomerId", cLiffState);
    }
    let customerId = route.query["c"] || localStorage.getItem("liffCustomerId");
    localStorage.setItem(
      "liffCustomerId",
      (customerId || cLiffState) as string,
    );
    const customer = await liffAppStore.fetchCustomerInfo(customerId as string);
    if (customer) {
      const liffInit = await init(runtimeConfig.public.NUXT_LINE_CHAT_LIFF_ID as string, {
        c: customer.customerId,
      });
      if (liffInit.success) {
        lineUser.value = profile.value;
        const counselee = await liffAppStore.fetchCounseleeInfo(
          customerId as string,
        );
        console.log("🚀 ~ onMounted ~ counselee:", counselee)
        if(counselee) {
          socketStore.liffAppConnectSocket(counselee?.counseleeId);
        } else {
          // relogin();
        }
      } else {
        errors.value["liff"] = liffInit.error?.message;
      }
    }
  });
</script>
