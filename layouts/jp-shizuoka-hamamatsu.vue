<template>
  <section class="bg-slate-100">
    <div class="mx-auto min-h-screen">
      <div class="bg-white">
        <div
          class="w-full max-w-screen-xl px-3 mx-auto py-4 border-b flex flex-row items-center justify-between"
        >
          <ShizuokaHamamatsuLogo class="h-10" />
          <div class="text-xl text-primary-500">こころの相談室</div>
        </div>
      </div>
      <slot />
    </div>
  </section>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useWebAppStore } from "~/stores/web-app";

  const webAppStore = useWebAppStore();

  const { loadings, customer } = storeToRefs(webAppStore);

  onMounted(() => {
    const appConfig = useAppConfig();
    const colorMode = useColorMode();
    appConfig.ui.primary = "cyan";
    colorMode.preference = "light";
  });
</script>
