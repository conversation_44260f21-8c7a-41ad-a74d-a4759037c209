{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "vitest", "test:unit": "vitest run test/unit/", "test:unit:simple": "vitest run test/unit/*.simple.test.ts test/unit/permissions.test.ts", "test:unit:watch": "vitest test/unit/", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@faker-js/faker": "8.2.0", "@iconify/vue": "4.1.1", "@nuxt/devtools": "latest", "@nuxt/test-utils": "^3.19.2", "@nuxt/ui": "2.11.0", "@nuxtjs/color-mode": "3.3.0", "@nuxtjs/eslint-config-typescript": "12.1.0", "@nuxtjs/eslint-module": "4.1.0", "@nuxtjs/svg": "0.4.1", "@pinia-plugin-persistedstate/nuxt": "1.2.0", "@typescript-eslint/eslint-plugin": "6.9.1", "@typescript-eslint/parser": "6.9.1", "@vitejs/plugin-vue": "^6.0.1", "@vue/test-utils": "^2.4.6", "axios-mock-adapter": "1.22.0", "eslint": "8.52.0", "eslint-plugin-vue": "9.18.1", "happy-dom": "^18.0.1", "nuxt": "3.8.0", "playwright-core": "^1.55.1", "sass": "1.69.5", "vite-svg-loader": "4.0.0", "vitest": "^3.2.4", "yup": "1.4.0"}, "dependencies": {"@formkit/auto-animate": "0.8.0", "@iconify/json": "2.2.142", "@line/liff": "2.23.0", "@nuxtjs/i18n": "8.0.0-rc.5", "@pinia/nuxt": "0.5.1", "@popperjs/core": "2.11.8", "@vuepic/vue-datepicker": "7.3.0", "@vueuse/nuxt": "10.5.0", "animate.css": "4.1.1", "axios": "1.6.1", "chart.js": "4.4.1", "click-outside-vue3": "4.0.1", "dayjs": "1.11.10", "japanese-holidays": "1.0.10", "lodash": "4.17.21", "nuxt-permissions": "0.2.4", "pinia": "2.1.7", "splitpanes": "3.1.5", "uuidv4": "6.2.13", "v-calendar": "3.1.2", "vue-chart-3": "3.1.8", "vue-number-animation": "1.1.2", "vuedraggable": "4.1.0", "yup-password": "0.2.2"}, "packageManager": "pnpm@8.9.2+sha1.5f2fa48d614263457cf5d7fb7be8b878da318d87"}