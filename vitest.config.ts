import { defineConfig } from "vitest/config";
import { defineVitestProject } from "@nuxt/test-utils/config";
export default defineConfig({
  resolve: {
    alias: {
      "bun:test": "vitest",
    },
  },
  test: {
    globals: true,
    environment: "happy-dom",
    projects: [
      await defineVitestProject({
        test: {
          name: "unit",
          include: ["test/unit/*.{test,spec}.ts"],
          environment: "nuxt",
        },
      }),
      await defineVitestProject({
        test: {
          name: "nuxt",
          include: ["test/nuxt/**/*.{test,spec}.ts"],
          environment: "nuxt",
        },
      }),
    ],
  },
});
