<template>
  <div>
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
      <UNotifications />
      <BaseConfirmDialog />
    </NuxtLayout>
    <BaseAppLoader />
  </div>
</template>

<script setup lang="ts">
  import "animate.css";
  import "splitpanes/dist/splitpanes.css";
  import "@vuepic/vue-datepicker/dist/main.css";
  const colorMode = useColorMode();
  onMounted(() => {
    const runtimeConfig = useRuntimeConfig();
    console.log(
      "App major version",
      runtimeConfig.public.NUXT_APP_MAJOR_VERSION,
    );
    console.log("App version", runtimeConfig.public.NUXT_APP_VERSION);
    // colorMode.value = "light";

    colorMode.preference = localStorage.getItem("colorMode") || "light";
  });
</script>
