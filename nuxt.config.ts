import svgLoader from "vite-svg-loader";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  ssr: false,
  devtools: { enabled: true },
  // extends: ['@nuxt/ui-pro'],
  modules: [
    "@nuxt/ui",
    "@pinia/nuxt",
    "@pinia-plugin-persistedstate/nuxt",
    "@nuxtjs/eslint-module",
    "@vueuse/nuxt",
    "@nuxtjs/i18n",
    "nuxt-permissions",
    '@nuxt/test-utils/module'
  ],
  build: { transpile: ["vue-number-animation"] },
  pinia: {
    storesDirs: ["./stores/**", "./stores/customs/**"],
  },
  vite: {
    plugins: [svgLoader()],
  },
  i18n: {
    vueI18n: "./i18n.config.ts",
  },
  components: [
    {
      path: "~/components",
      pathPrefix: false,
    },
  ],
  css: ["@/assets/styles/global.scss"],
  runtimeConfig: {
    public: {
      NUXT_USE_MOCK_API: process.env.NUXT_USE_MOCK_API === "true",
      NUXT_API_AUTHENTICATE_BASE_URL:
        process.env.NUXT_API_AUTHENTICATE_BASE_URL,
      NUXT_API_ADMIN_BASE_URL: process.env.NUXT_API_ADMIN_BASE_URL,
      NUXT_API_APP_BASE_URL: process.env.NUXT_API_APP_BASE_URL,
      NUXT_SOCKET_BASE_URL: process.env.NUXT_SOCKET_BASE_URL,
      NUXT_APP_MAJOR_VERSION: process.env.NUXT_APP_MAJOR_VERSION,
      NUXT_LINE_CHAT_LIFF_ID: process.env.NUXT_LINE_CHAT_LIFF_ID,
      NUXT_LINE_SURVEY_LIFF_ID: process.env.NUXT_LINE_SURVEY_LIFF_ID,
      NUXT_LINE_LIFF_MOCK: process.env.NUXT_LINE_LIFF_MOCK === "true",
      NUXT_LINE_ID_TOKEN: process.env.NUXT_LINE_ID_TOKEN,
      NUXT_LINE_LOGIN_REDIRECT_URL: process.env.NUXT_LINE_LOGIN_REDIRECT_URL,
      NUXT_APP_VERSION: process.env.NUXT_APP_VERSION,
      NUXT_APP_BUILD_DATE: process.env.NUXT_APP_BUILD_DATE,
      autoRefreshs: {
        cases: 60000 || process.env.NUXT_APP_AUTO_REFRESH_CASES, // 60秒毎
        counselors: 60000 || process.env.NUXT_APP_AUTO_REFRESH_COUNSELORS, // 60秒毎
        statistics: 60000 || process.env.NUXT_APP_AUTO_REFRESH_STATISTICS, // 60秒毎
        customers: 60000 || process.env.NUXT_APP_AUTO_REFRESH_CUSTOMERS, // 60秒毎
        chats: 60000 || process.env.NUXT_APP_AUTO_REFRESH_CHATS, // 60秒毎
        counseleeChat:
          60000 || process.env.NUXT_APP_AUTO_REFRESH_COUNSELEE_CHAT, // 60秒毎
      },
      hamamatsuChatAvailable: {
        // 検証のため、強制的に有効にする
        hardAvailable:
          process.env.NUXT_HAMAMATSU_CHAT_AVAILABLE === "true" || false,
          
        // 本番環境
        // 0 (Sunday) to 6 (Saturday).
        // 毎週水曜日・日曜日（令和9年3月末まで）
        days: [0, 3],
        // 期限：2026年3月31日まで
        // 時間帯：18:00～22:00
        times: [18, 19, 20, 21, 22],
        // 連続期間
        // 令和8年(2026年)
        // 1/5~1/21
        // 5/5~5/12
        // 6/5~6/18
        // 8/19~9/6
        // 令和9年(2027年)
        // 1/5~1/21
        dates: [
          ["2026-01-05", "2026-01-21"],
          ["2026-05-05", "2026-05-12"],
          ["2026-06-05", "2026-06-18"],
          ["2026-08-19", "2026-09-06"],
          ["2027-01-05", "2027-01-21"],
        ],
      },
    },
  },
  colorMode: {
    preference: "light",
    fallback: "light",
  },
  ui: {
    icons: "all",
  },
  app: {
    head: {
      viewport:
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
    },
  },
});
