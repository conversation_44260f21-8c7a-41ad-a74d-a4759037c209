No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,UI/UX,シナリオ一覧,テーブルレイアウト,テーブルが適切なレイアウトで表示されること,"1. シナリオ一覧画面を表示
2. テーブルヘッダーが適切に配置されていることを確認
3. 各行のデータが整列していることを確認",,
2,UI/UX,シナリオ一覧,アイコン表示,適切なアイコンが表示されること,"1. シナリオ一覧でチャットバブルアイコンが表示されることを確認
2. アクションボタンに適切なアイコンが表示されることを確認",,
3,UI/UX,シナリオ一覧,ホバーエフェクト,ボタンホバー時のエフェクトが適切に動作すること,"1. 各ボタンにマウスをホバー
2. ホバーエフェクトが表示されることを確認
3. ホバー解除時に元の状態に戻ることを確認",,
4,UI/UX,シナリオ一覧,バッジ表示,ステータスバッジが適切な色で表示されること,"1. 有効なシナリオのバッジが緑色で表示されることを確認
2. 無効なシナリオのバッジがグレーで表示されることを確認",,
5,UI/UX,シナリオ一覧,テキスト省略,長いテキストが適切に省略されること,"1. 長い説明文を持つシナリオを表示
2. テキストが「...」で省略されることを確認
3. 適切な幅で切り詰められることを確認",,
6,UI/UX,モーダル,モーダル表示アニメーション,モーダルの表示・非表示アニメーションが滑らかであること,"1. 新規作成ボタンをクリック
2. モーダルが滑らかにフェードイン表示されることを確認
3. 閉じる時も滑らかにフェードアウトすることを確認",,
7,UI/UX,モーダル,背景オーバーレイ,モーダル背景のオーバーレイが適切に表示されること,"1. モーダルを開く
2. 背景が半透明のオーバーレイで覆われることを確認
3. 背景クリックでモーダルが閉じることを確認",,
8,UI/UX,モーダル,モーダル幅調整,質問設定モーダルの幅が適切に調整されること,"1. 質問設定モーダルを開く
2. モーダル幅が内容に対して適切であることを確認
3. 画面幅に応じて調整されることを確認",,
9,UI/UX,フォーム,フォーカス状態,入力フィールドのフォーカス状態が明確であること,"1. 各入力フィールドをクリック
2. フォーカス状態が視覚的に分かりやすいことを確認
3. フォーカス外れ時の状態も確認",,
10,UI/UX,フォーム,プレースホルダー,適切なプレースホルダーテキストが表示されること,"1. 各入力フィールドのプレースホルダーを確認
2. 内容が分かりやすいテキストであることを確認",,
11,UI/UX,フォーム,エラー表示,バリデーションエラーが分かりやすく表示されること,"1. 必須項目を空白で保存を試行
2. エラーメッセージが赤色で表示されることを確認
3. エラー箇所が明確に示されることを確認",,
12,UI/UX,フォーム,成功メッセージ,操作成功時のメッセージが適切に表示されること,"1. シナリオを正常に作成
2. 成功メッセージがトースト形式で表示されることを確認
3. 適切な色（緑色）で表示されることを確認",,
13,UI/UX,ボタン,ボタンサイズ統一,各画面のボタンサイズが統一されていること,"1. 各画面のボタンサイズを確認
2. 同じレベルのボタンが同じサイズであることを確認",,
14,UI/UX,ボタン,ボタン色分け,ボタンの色が用途に応じて適切に分けられていること,"1. 作成・保存ボタンが青系色であることを確認
2. 削除ボタンが赤系色であることを確認
3. キャンセルボタンがグレー系色であることを確認",,
15,UI/UX,ボタン,無効化状態,ボタンの無効化状態が視覚的に分かること,"1. 選択肢が最大数に達した時の追加ボタンを確認
2. ボタンが無効化されて見えることを確認
3. クリックできない状態であることを確認",,
16,UI/UX,レスポンシブ,タブレット表示,タブレット画面で適切に表示されること,"1. 画面幅をタブレットサイズに調整
2. テーブルとモーダルが適切に表示されることを確認
3. タッチ操作に適したボタンサイズであることを確認",,
17,UI/UX,レスポンシブ,スマートフォン表示,スマートフォン画面で適切に表示されること,"1. 画面幅をスマートフォンサイズに調整
2. テーブルが横スクロール可能であることを確認
3. モーダルが画面に収まることを確認",,
18,UI/UX,アクセシビリティ,キーボード操作,キーボードのみで操作できること,"1. Tabキーでフォーカス移動できることを確認
2. Enterキーでボタンを押下できることを確認
3. Escapeキーでモーダルを閉じられることを確認",,
19,UI/UX,アクセシビリティ,ARIA属性,適切なARIA属性が設定されていること,"1. ボタンに適切なaria-labelが設定されていることを確認
2. フォームフィールドに適切なaria-describedbyが設定されていることを確認",開発者ツールで確認,
20,UI/UX,パフォーマンス,画面読み込み速度,画面の読み込み速度が適切であること,"1. シナリオ一覧画面の読み込み時間を測定
2. 2秒以内に表示されることを確認",パフォーマンス要件に依存,
21,UI/UX,パフォーマンス,モーダル表示速度,モーダルの表示速度が適切であること,"1. モーダル表示のレスポンス時間を確認
2. 即座に表示されることを確認",,
22,UI/UX,文字数カウンター,カウンター表示,文字数カウンターが見やすく表示されること,"1. 質問入力フィールドに文字を入力
2. 右下に文字数が表示されることを確認
3. 制限に近づくと色が変わることを確認（実装されている場合）",,
23,UI/UX,文字数カウンター,リアルタイム更新,文字数がリアルタイムで更新されること,"1. 文字を入力・削除
2. 文字数が即座に更新されることを確認",,
24,UI/UX,選択肢管理,選択肢追加UI,選択肢追加ボタンが分かりやすく配置されていること,"1. 選択肢セクションで追加ボタンの配置を確認
2. 他のボタンと区別しやすいことを確認",,
25,UI/UX,選択肢管理,選択肢削除UI,選択肢削除ボタンが適切に配置されていること,"1. 各選択肢の削除ボタンの配置を確認
2. 誤操作しにくい位置にあることを確認",,
26,UI/UX,選択肢管理,選択肢境界線,各選択肢の境界が明確であること,"1. 複数の選択肢がある状態を確認
2. 各選択肢が境界線で区切られていることを確認",,
27,UI/UX,ドロップダウン,選択肢動作ドロップダウン,動作選択ドロップダウンが使いやすいこと,"1. 動作選択ドロップダウンをクリック
2. 選択肢が明確に表示されることを確認
3. 選択後にドロップダウンが閉じることを確認",,
28,UI/UX,ローディング,ローディングアニメーション,ローディング中のアニメーションが適切であること,"1. リロードボタンをクリック
2. ローディングスピナーが表示されることを確認
3. アニメーションが滑らかであることを確認",,
29,UI/UX,ツールチップ,ボタンツールチップ,ボタンにツールチップが表示されること,"1. 各アクションボタンにホバー
2. ボタンの機能説明ツールチップが表示されることを確認（実装されている場合）",ツールチップが実装されている場合,
30,UI/UX,スペーシング,要素間の余白,各UI要素間の余白が適切であること,"1. 各セクション間の余白を確認
2. 窮屈すぎず、離れすぎていないことを確認",,
31,UI/UX,タイポグラフィ,フォントサイズ,フォントサイズが読みやすいこと,"1. 各テキストのフォントサイズを確認
2. 階層に応じて適切なサイズであることを確認",,
32,UI/UX,タイポグラフィ,フォント色,フォント色のコントラストが適切であること,"1. テキストと背景のコントラストを確認
2. WCAG基準を満たしていることを確認",アクセシビリティ要件,
33,UI/UX,エラーハンドリング,ネットワークエラーUI,ネットワークエラー時のUI表示が適切であること,"1. ネットワークエラーを発生させる
2. エラー状態が分かりやすく表示されることを確認
3. 再試行ボタンなどが提供されることを確認",,
34,UI/UX,エラーハンドリング,404エラーUI,存在しないシナリオアクセス時のUI表示が適切であること,"1. 存在しないシナリオIDでアクセス
2. 404エラーページまたはメッセージが表示されることを確認",,
35,UI/UX,空の状態,空のメッセージデザイン,空の状態メッセージのデザインが適切であること,"1. シナリオが0件の状態を確認
2. アイコンとメッセージが中央に配置されていることを確認
3. 視覚的に分かりやすいことを確認",,