# SNS相談システム 総合テスト仕様書

## 概要

このディレクトリには、SNS相談システムフロントエンドの総合テスト（Integration Test）に関する仕様書が含まれています。

## システム概要

SNS相談システムは、LINE、LIFF、Webアプリケーションを通じて相談者とカウンセラーをつなぐ統合プラットフォームです。

### 主要機能
- **認証システム**: 多段階の権限管理（管理者、スーパーバイザー、一般、閲覧者）
- **チャット機能**: リアルタイムメッセージング、ケース管理、状態管理
- **アンケート機能**: 動的フォーム作成、条件分岐、結果集計
- **ウィザード機能**: ステップ式ガイダンス、条件分岐、結果管理
- **チャットボット機能**: 自動応答、シナリオ管理、キーワード応答
- **管理機能**: カスタマーアカウント管理、カウンセラー管理、権限管理
- **レポート機能**: データエクスポート、統計情報、ログ管理
- **外部連携**: LINE API、LIFF、メール送信、ファイル管理

## テストファイル構成

### 1. integration_test_scenarios.csv
**総合テストシナリオ（70項目）**

システム全体の主要機能を網羅した統合テストシナリオです。

#### 主要テスト項目:
- 認証システム（ログイン、パスワードリセット、セッション管理）
- 管理機能（カスタマー・カウンセラーアカウント管理、権限管理）
- チャット機能（メッセージ送受信、ケース管理、状態管理）
- アンケート機能（作成、管理、回答収集、結果表示）
- ウィザード機能（作成、実行、結果管理）
- チャットボット機能（設定、シナリオ管理、自動応答）
- レポート機能（データエクスポート、統計情報）
- 外部連携（LINE、LIFF、メール）
- セキュリティ（認証・認可、データ保護、セッション管理）
- パフォーマンス（ページ読み込み、大量データ処理、同時接続）
- ユーザビリティ（レスポンシブ対応、ナビゲーション、エラーハンドリング）
- 国際化（多言語対応、文字エンコーディング）
- アクセシビリティ（キーボード操作、スクリーンリーダー対応）
- データ整合性（リアルタイム同期、バックアップ・復旧）
- エンドツーエンドテスト（相談フロー、アンケート実施、ウィザード実行）

### 2. detailed_test_scenarios.csv
**詳細テストシナリオ（80項目）**

各機能の詳細な動作を検証するテストシナリオです。

#### 主要テスト項目:
- 認証システムの詳細（権限別ログイン、エラーハンドリング、セッション管理）
- 管理機能の詳細（アカウント作成・編集、機能設定、権限マトリックス）
- チャット機能の詳細（ケース検索・フィルタ、メッセージ送受信、ファイル送信）
- アンケート機能の詳細（フォーム作成、質問項目設定、結果管理）
- ウィザード機能の詳細（フロー作成、ステップ設定、実行・結果管理）
- チャットボット機能の詳細（基本設定、シナリオ管理、自動応答設定）

### 3. ui_ux_integration_test_scenarios.csv
**UI/UX・統合テストシナリオ（50項目）**

ユーザーインターフェース、ユーザーエクスペリエンス、および外部システム統合に関するテストシナリオです。

#### 主要テスト項目:
- UI/UX（レスポンシブデザイン、ナビゲーション、フォーム、モーダル、テーブル）
- アクセシビリティ（キーボード操作、フォーカス表示、色覚対応、スクリーンリーダー）
- 多言語対応（言語切り替え、文字表示）
- テーマ・スタイル（ダークモード、カスタムテーマ）
- 統合機能（LINE連携、メール連携、外部API、ファイル管理）
- パフォーマンス（ページ読み込み、データ処理、メモリ使用量）
- セキュリティ（権限チェック）

### 4. scenario_creation_test_scenarios.csv
**シナリオ作成機能テストシナリオ（50項目）**

チャットボットのシナリオ作成・管理機能に関する詳細なテストシナリオです。

#### 主要テスト項目:
- シナリオ管理（一覧表示、作成、編集、削除、有効化/無効化）
- 質問設定（質問入力、文字数制限、バリデーション）
- 選択肢管理（追加、削除、最大数制限、動作設定）
- 選択肢動作（終了アンケート、トーク開設、次の設問、終了）
- UI/UX（フォーム操作、モーダル、テーブル、バリデーション表示）
- データ管理（統計更新、日時表示、空の状態表示）

### 5. scenario_ui_ux_test_scenarios.csv
**シナリオ管理UI/UXテストシナリオ（35項目）**

シナリオ管理画面のユーザーインターフェース・ユーザーエクスペリエンスに特化したテストシナリオです。

#### 主要テスト項目:
- レスポンシブデザイン（タブレット、スマートフォン表示）
- アクセシビリティ（キーボード操作、ARIA属性、スクリーンリーダー対応）
- ビジュアルデザイン（アイコン、色彩、レイアウト、アニメーション）
- フォーム操作性（フォーカス、バリデーション、エラー表示、文字数カウンター）
- パフォーマンス（読み込み速度、表示速度、操作レスポンス）

### 6. scenario_integration_test_scenarios.csv
**シナリオ統合テストシナリオ（40項目）**

シナリオ機能と他システムとの連携・統合に関するテストシナリオです。

#### 主要テスト項目:
- チャットボット連携（シナリオ選択、依存関係チェック、実行動作）
- 権限管理（アクセス制御、操作権限、閲覧専用権限）
- データ整合性（リアルタイム更新、統計更新、バックアップ・復旧）
- 外部連携（LINE、LIFF、API連携、エクスポート・インポート）
- パフォーマンス・セキュリティ（大量データ、同時接続、認証・セッション管理）
- エンドツーエンド（シナリオ作成から実行までの完全フロー）

## CSV ファイル形式

各CSVファイルは以下の列構成になっています：

| 列名 | 説明 |
|------|------|
| No | テストケース番号 |
| 大項目 | 大分類（例：認証システム、チャット機能） |
| 中項目 | 中分類（例：ログイン機能、メッセージ送受信） |
| 小項目 | 小分類（例：正常ログイン、エラーハンドリング） |
| 期待動作 | テストで期待される結果 |
| 手順 | 具体的なテスト手順（番号付きリスト） |
| 備考 | 追加情報、注意事項、関連する技術仕様 |
| 結果 | テスト結果記録欄（Pass, Fail, Retest, Skip, Blocked） |

## テスト実行ガイドライン

### 前提条件
1. **環境準備**
   - Node.js v20.x
   - pnpm v8.9.2 または npm v10.1.0
   - 開発環境: `NUXT_USE_MOCK_API=true`

2. **権限設定**
   - 管理者権限アカウント
   - 各権限レベルのテストアカウント（スーパーバイザー、一般、閲覧者）

3. **外部連携設定**
   - LINE API設定（チャンネルID、シークレット、アクセストークン）
   - LIFF設定（LIFF ID）
   - メール送信設定

### テスト実行順序

1. **基本機能テスト**
   - 認証システム
   - 管理機能
   - 基本的なCRUD操作

2. **コア機能テスト**
   - チャット機能
   - アンケート機能
   - ウィザード機能
   - チャットボット機能

3. **統合テスト**
   - 外部システム連携
   - データ同期
   - エンドツーエンドフロー

4. **非機能テスト**
   - パフォーマンス
   - セキュリティ
   - アクセシビリティ
   - ユーザビリティ

### テスト結果の記録

結果列に以下の値を記録してください：

- **Pass**: テストが成功
- **Fail**: テストが失敗（詳細な失敗理由を備考欄に記録）
- **Retest**: 再テストが必要
- **Skip**: テストをスキップ（理由を備考欄に記録）
- **Blocked**: テスト実行がブロックされている（依存関係等）

## 技術仕様参照

テスト実行時は以下の技術仕様を参照してください：

### 主要な型定義
- `CounselorRole`: 権限レベル（ADMIN, SUPERVISOR, GENERAL, VIEWER）
- `CaseStatus`: ケース状態（BEFORE_START, OPEN, IN_PROGRESS, WAITING, RESOLVED, CANCELLED）
- `CustomerFeature`: 利用可能機能（Chatbot, Survey, Wizard, Case, Tag等）
- `SurveyStatus`: アンケート状態（DRAFT, ACTIVE, INACTIVE）

### 主要なインターフェース
- `Customer`: カスタマーアカウント情報
- `Case`: 相談ケース情報
- `CaseChatMessage`: チャットメッセージ情報
- `ChatbotConfig`: チャットボット設定情報

## 注意事項

1. **データの整合性**: テスト実行前後でデータの整合性を確認してください
2. **権限チェック**: 各機能で適切な権限チェックが動作することを確認してください
3. **エラーハンドリング**: 正常系だけでなく異常系のテストも必ず実行してください
4. **パフォーマンス**: 大量データでの動作確認を行ってください
5. **セキュリティ**: 認証・認可、データ保護の観点でテストを実行してください

## 更新履歴

- 2024-XX-XX: 初版作成
- 2024-09-25: シナリオ作成機能テストシナリオ追加
- 総合テストシナリオ: 70項目
- 詳細テストシナリオ: 80項目  
- UI/UX・統合テストシナリオ: 50項目
- シナリオ作成機能テストシナリオ: 50項目
- シナリオ管理UI/UXテストシナリオ: 35項目
- シナリオ統合テストシナリオ: 40項目

## 関連ドキュメント

- `FEATURE_UPDATE_README.md`: 機能更新履歴
- `README.md`: プロジェクト概要
- `nuxt.config.ts`: Nuxt設定
- `types/`: 型定義ファイル
