No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,認証システム,ログイン機能,正常ログイン（管理者）,管理者権限でログインが成功すること,"1. /loginページにアクセス
2. 管理者のメールアドレスを入力
3. パスワードを入力
4. ログインボタンをクリック
5. /adminページにリダイレクトされることを確認",CounselorRole.ADMIN権限,
2,認証システム,ログイン機能,正常ログイン（スーパーバイザー）,スーパーバイザー権限でログインが成功すること,"1. /loginページにアクセス
2. スーパーバイザーのメールアドレスを入力
3. パスワードを入力
4. ログインボタンをクリック
5. /appページにリダイレクトされることを確認",CounselorRole.SUPERVISOR権限,
3,認証システム,ログイン機能,正常ログイン（一般カウンセラー）,一般カウンセラー権限でログインが成功すること,"1. /loginページにアクセス
2. 一般カウンセラーのメールアドレスを入力
3. パスワードを入力
4. ログインボタンをクリック
5. /appページにリダイレクトされることを確認",CounselorRole.GENERAL権限,
4,認証システム,ログイン機能,正常ログイン（閲覧者）,閲覧者権限でログインが成功すること,"1. /loginページにアクセス
2. 閲覧者のメールアドレスを入力
3. パスワードを入力
4. ログインボタンをクリック
5. /appページにリダイレクトされることを確認",CounselorRole.VIEWER権限,
5,認証システム,ログイン機能,無効なメールアドレス,存在しないメールアドレスでログインが拒否されること,"1. /loginページにアクセス
2. 存在しないメールアドレスを入力
3. 任意のパスワードを入力
4. ログインボタンをクリック
5. エラーメッセージが表示されることを確認",認証エラーハンドリング,
6,認証システム,ログイン機能,無効なパスワード,間違ったパスワードでログインが拒否されること,"1. /loginページにアクセス
2. 正しいメールアドレスを入力
3. 間違ったパスワードを入力
4. ログインボタンをクリック
5. エラーメッセージが表示されることを確認",認証エラーハンドリング,
7,認証システム,ログイン機能,空の入力フィールド,必須フィールドが空の場合にバリデーションエラーが表示されること,"1. /loginページにアクセス
2. メールアドレスフィールドを空のまま
3. パスワードフィールドを空のまま
4. ログインボタンをクリック
5. バリデーションエラーが表示されることを確認",フロントエンドバリデーション,
8,認証システム,パスワードリセット,有効なメールアドレス,登録済みメールアドレスでパスワードリセットメールが送信されること,"1. /forgot-passwordページにアクセス
2. 登録済みメールアドレスを入力
3. 送信ボタンをクリック
4. 成功メッセージが表示されることを確認
5. メールが送信されることを確認",メール送信機能,
9,認証システム,パスワードリセット,無効なメールアドレス,未登録メールアドレスでエラーメッセージが表示されること,"1. /forgot-passwordページにアクセス
2. 未登録メールアドレスを入力
3. 送信ボタンをクリック
4. エラーメッセージが表示されることを確認",エラーハンドリング,
10,認証システム,パスワード設定,新規パスワード設定,有効なトークンで新しいパスワードが設定できること,"1. パスワードリセットメールのリンクをクリック
2. /set-passwordページにアクセス
3. 新しいパスワードを入力
4. パスワード確認を入力
5. 設定ボタンをクリック
6. 成功メッセージが表示されることを確認",パスワード強度チェック,
11,認証システム,パスワード設定,パスワード不一致,パスワードと確認パスワードが一致しない場合にエラーが表示されること,"1. /set-passwordページにアクセス
2. 新しいパスワードを入力
3. 異なるパスワード確認を入力
4. 設定ボタンをクリック
5. エラーメッセージが表示されることを確認",バリデーション機能,
12,認証システム,パスワード設定,弱いパスワード,パスワード強度が不十分な場合にエラーが表示されること,"1. /set-passwordページにアクセス
2. 弱いパスワード（例：123456）を入力
3. 同じパスワードを確認欄に入力
4. 設定ボタンをクリック
5. パスワード強度エラーが表示されることを確認",パスワードポリシー,
13,認証システム,セッション管理,セッション有効期限,セッション有効期限切れで自動ログアウトされること,"1. ログイン後、一定時間（設定値）待機
2. 何らかの操作を実行
3. セッション期限切れメッセージが表示されることを確認
4. ログインページにリダイレクトされることを確認",セッション管理,
14,認証システム,セッション管理,ログアウト機能,ログアウトボタンでセッションが無効化されること,"1. ログイン状態でログアウトボタンをクリック
2. ログインページにリダイレクトされることを確認
3. ブラウザの戻るボタンで保護されたページにアクセス
4. ログインページにリダイレクトされることを確認",セッション無効化,
15,管理機能,カスタマーアカウント管理,アカウント一覧表示,カスタマーアカウント一覧が正しく表示されること,"1. 管理者でログイン
2. /adminページにアクセス
3. カスタマーアカウント一覧が表示されることを確認
4. 各アカウントの基本情報が表示されることを確認
5. ページネーションが動作することを確認",管理者権限必須,
16,管理機能,カスタマーアカウント管理,アカウント検索,カスタマーアカウントの検索機能が正常に動作すること,"1. アカウント一覧ページで検索フィールドに顧客名を入力
2. 検索ボタンをクリック
3. 該当するアカウントのみが表示されることを確認
4. 検索条件をクリアして全件表示に戻ることを確認",検索機能,
17,管理機能,カスタマーアカウント管理,アカウント作成,新規カスタマーアカウントが正常に作成できること,"1. アカウント作成ボタンをクリック
2. CustomerAccountModalが開くことを確認
3. 必須項目を入力（customerName、slug、startDate、endDate等）
4. 利用可能機能を選択
5. 保存ボタンをクリック
6. アカウントが作成されることを確認",必須項目バリデーション,
18,管理機能,カスタマーアカウント管理,アカウント編集,既存カスタマーアカウントが正常に編集できること,"1. アカウント一覧から編集対象を選択
2. 編集ボタンをクリック
3. CustomerAccountModalが開くことを確認
4. 情報を変更
5. 保存ボタンをクリック
6. 変更が反映されることを確認",編集権限チェック,
19,管理機能,カスタマーアカウント管理,機能選択,カスタマーの利用可能機能が正しく設定できること,"1. アカウント作成/編集画面で機能選択セクションにアクセス
2. チャットボット機能を選択
3. チャットボット設定セクションが表示されることを確認
4. 基本設定を入力
5. 保存して設定が反映されることを確認",CustomerFeature enum,
20,管理機能,カスタマーアカウント管理,チャットボット設定,チャットボット機能の詳細設定ができること,"1. チャットボット機能を選択
2. チャットボット名を入力
3. ウェルカムメッセージを入力
4. 自動応答設定を有効化
5. 応答待機時間を設定（1-300秒）
6. 営業時間外メッセージを設定
7. キーワード応答を追加
8. 設定が保存されることを確認",ChatbotConfig interface,
21,管理機能,カスタマーアカウント管理,LINE設定,LINE連携設定が正常に設定できること,"1. LINE設定セクションにアクセス
2. チャンネルIDを入力
3. チャンネルシークレットを入力
4. アクセストークンを入力
5. LIFF IDを入力
6. 設定を保存
7. 接続テストが成功することを確認",LINE API設定,
22,管理機能,カスタマーアカウント管理,アプリケーション設定,アプリケーション設定が正常に設定できること,"1. アプリケーション設定セクションにアクセス
2. アクセスコードを設定
3. ブラウザアクセスコードを設定
4. ブラウザアクセスコード表示設定を変更
5. 設定を保存
6. 設定が反映されることを確認",アクセス制御設定,
23,管理機能,カスタマーアカウント管理,契約期間設定,契約期間が正常に設定できること,"1. 契約開始日を設定
2. 契約終了日を設定
3. 契約ライン数を設定
4. 相談者制限数を設定
5. 設定を保存
6. 設定が反映されることを確認",契約管理,
24,管理機能,カスタマーアカウント管理,IPホワイトリスト,IPホワイトリストが正常に設定できること,"1. IPホワイトリスト設定セクションにアクセス
2. 許可するIPアドレスを追加
3. IPアドレス形式のバリデーションを確認
4. 複数のIPアドレスを設定
5. 設定を保存
6. アクセス制御が動作することを確認",セキュリティ設定,
25,管理機能,カウンセラーアカウント管理,カウンセラー一覧,カウンセラーアカウント一覧が正しく表示されること,"1. /admin/counserlor-accountページにアクセス
2. カウンセラー一覧が表示されることを確認
3. 各カウンセラーの基本情報が表示されることを確認
4. 権限レベルが表示されることを確認
5. 担当顧客数が表示されることを確認",カウンセラー管理,
26,管理機能,カウンセラーアカウント管理,カウンセラー作成,新規カウンセラーアカウントが正常に作成できること,"1. カウンセラー作成ボタンをクリック
2. 必須情報を入力（名前、メール、権限等）
3. 担当顧客を選択
4. パスワード設定方法を選択
5. 保存ボタンをクリック
6. アカウントが作成されることを確認",権限設定,
27,管理機能,カウンセラーアカウント管理,カウンセラー編集,既存カウンセラーアカウントが正常に編集できること,"1. カウンセラー一覧から編集対象を選択
2. 編集ボタンをクリック
3. 情報を変更
4. 権限レベルを変更
5. 担当顧客を変更
6. 保存ボタンをクリック
7. 変更が反映されることを確認",権限変更の影響確認,
28,管理機能,カウンセラーアカウント管理,権限設定,カウンセラーの権限が正しく設定できること,"1. カウンセラー編集画面で権限設定セクションにアクセス
2. 管理者、スーパーバイザー、一般、閲覧者から選択
3. 各権限レベルの説明が表示されることを確認
4. 権限を変更して保存
5. 実際のアクセス権限が変更されることを確認",CounselorRole enum,
29,管理機能,権限管理,権限マトリックス表示,権限マトリックスが正しく表示されること,"1. /admin/permissionページにアクセス
2. 権限マトリックステーブルが表示されることを確認
3. 各権限（管理者、スーパーバイザー、一般、閲覧者）の列が表示されることを確認
4. 各機能の行が表示されることを確認
5. 権限の有無がトグルで表示されることを確認",権限可視化,
30,管理機能,権限管理,権限変更,権限設定が正常に変更できること,"1. 権限マトリックスで特定の権限トグルをクリック
2. 権限の有効/無効が切り替わることを確認
3. 変更を保存
4. 実際のアクセス権限が変更されることを確認
5. 該当ユーザーでログインして確認",権限変更の即時反映,
31,チャット機能,ケース管理,ケース一覧表示,対応中のケース一覧が正しく表示されること,"1. /app/chatsページにアクセス
2. 対応中のケース一覧が表示されることを確認
3. ケース情報（ID、相談者名、状態、最終投稿時間等）が表示されることを確認
4. ケース状態別の色分けが正しく表示されることを確認",CaseStatus enum,
32,チャット機能,ケース管理,ケース検索,ケース検索機能が正常に動作すること,"1. ケース検索フォームにケースIDを入力
2. 検索ボタンをクリック
3. 該当するケースのみが表示されることを確認
4. 相談者名での検索を実行
5. 該当するケースが表示されることを確認
6. 検索条件をクリアして全件表示に戻ることを確認",CaseSearchConditions interface,
33,チャット機能,ケース管理,ケースフィルタ,ケース状態別フィルタが正常に動作すること,"1. ケース状態フィルタで「対応中」を選択
2. 対応中のケースのみが表示されることを確認
3. 「待機中」を選択
4. 待機中のケースのみが表示されることを確認
5. 「すべて」を選択
6. 全ケースが表示されることを確認",状態別フィルタリング,
34,チャット機能,ケース管理,ケース詳細表示,ケース詳細情報が正しく表示されること,"1. ケース一覧からケースを選択
2. ケース詳細画面が表示されることを確認
3. 相談者情報が表示されることを確認
4. チャット履歴が表示されることを確認
5. ケース状態が表示されることを確認
6. 担当者情報が表示されることを確認",Case interface,
35,チャット機能,メッセージ送受信,メッセージ送信,カウンセラーからのメッセージ送信が正常に動作すること,"1. ケース詳細画面でメッセージ入力欄にテキストを入力
2. 送信ボタンをクリック
3. メッセージが送信されることを確認
4. チャット履歴に追加されることを確認
5. 送信状態が正しく表示されることを確認",CaseChatMessage interface,
36,チャット機能,メッセージ送受信,メッセージ受信,相談者からのメッセージ受信が正常に動作すること,"1. 相談者側からメッセージを送信（外部システムから）
2. カウンセラー側でメッセージを受信
3. 未読通知が表示されることを確認
4. メッセージ内容が正しく表示されることを確認
5. 受信時刻が正しく表示されることを確認",リアルタイム通信,
37,チャット機能,メッセージ送受信,ファイル送信,ファイル添付機能が正常に動作すること,"1. メッセージ入力欄でファイル添付ボタンをクリック
2. ファイルを選択
3. ファイルがプレビュー表示されることを確認
4. 送信ボタンをクリック
5. ファイルが正常に送信されることを確認
6. 相談者側でファイルを受信できることを確認",ファイルアップロード機能,
38,チャット機能,メッセージ送受信,絵文字・特殊文字,絵文字や特殊文字が正しく送受信できること,"1. メッセージ入力欄に絵文字を入力
2. 特殊文字（記号等）を入力
3. 送信ボタンをクリック
4. 絵文字・特殊文字が正しく表示されることを確認
5. 相談者側でも正しく表示されることを確認",文字エンコーディング,
39,チャット機能,ケース状態管理,状態変更,ケースの状態変更が正常に動作すること,"1. ケース詳細画面で状態変更ボタンをクリック
2. 新しい状態を選択（例：対応中→待機中）
3. 変更理由を入力（任意）
4. 確定ボタンをクリック
5. 状態が変更されることを確認
6. 履歴に記録されることを確認",CaseStatus enum,
40,チャット機能,ケース状態管理,担当者変更,ケースの担当者変更が正常に動作すること,"1. ケース詳細画面で担当者変更ボタンをクリック
2. 新しい担当者を選択
3. 引き継ぎメモを入力
4. 確定ボタンをクリック
5. 担当者が変更されることを確認
6. 新担当者に通知が送信されることを確認",担当者管理,
41,チャット機能,ケース状態管理,ケース完了,ケースの完了処理が正常に動作すること,"1. ケース詳細画面で完了ボタンをクリック
2. 完了理由を選択
3. 完了メモを入力
4. 確定ボタンをクリック
5. ケース状態が「対応済」に変更されることを確認
6. 完了時刻が記録されることを確認",ケース完了処理,
42,チャット機能,メモ・タグ機能,メモ追加,ケースにメモを追加できること,"1. ケース詳細画面でメモ追加ボタンをクリック
2. メモ内容を入力
3. 保存ボタンをクリック
4. メモが保存されることを確認
5. 他のカウンセラーからも閲覧できることを確認",共有メモ機能,
43,チャット機能,メモ・タグ機能,メモ編集,既存のメモを編集できること,"1. 既存のメモを選択
2. 編集ボタンをクリック
3. メモ内容を変更
4. 保存ボタンをクリック
5. 変更が反映されることを確認
6. 編集履歴が記録されることを確認",メモ編集機能,
44,チャット機能,メモ・タグ機能,タグ設定,ケースにタグを設定できること,"1. ケース詳細画面でタグ設定ボタンをクリック
2. 既存タグから選択または新規タグを作成
3. タグを設定
4. 保存ボタンをクリック
5. タグが設定されることを確認
6. タグでの検索・フィルタが動作することを確認",タグ管理機能,
45,チャット機能,メモ・タグ機能,タグ管理,タグの作成・編集・削除ができること,"1. タグ管理画面にアクセス
2. 新規タグ作成ボタンをクリック
3. タグ名、色、説明を入力
4. 保存ボタンをクリック
5. タグが作成されることを確認
6. 編集・削除機能が動作することを確認",TagFormType enum,
46,アンケート機能,アンケート作成,フォーム作成,アンケートフォームが正常に作成できること,"1. /app/surveysページにアクセス
2. 新規作成ボタンをクリック
3. アンケート名を入力
4. フォームビルダーで質問項目を追加
5. 保存ボタンをクリック
6. アンケートが作成されることを確認",Survey interface,
47,アンケート機能,アンケート作成,質問項目追加,様々なタイプの質問項目が追加できること,"1. フォームビルダーで質問追加ボタンをクリック
2. テキスト入力タイプを選択
3. 質問文を入力
4. 必須設定を行う
5. 単一選択タイプの質問を追加
6. 選択肢を設定
7. 複数選択タイプの質問を追加
8. 各質問タイプが正しく設定されることを確認",質問タイプ管理,
48,アンケート機能,アンケート作成,条件分岐設定,アンケートに条件分岐が設定できること,"1. フォームビルダーで条件分岐を追加
2. 分岐条件を設定（例：前の質問の回答が「はい」の場合）
3. 分岐先の質問を設定
4. プレビュー機能で分岐動作を確認
5. 保存して設定が反映されることを確認",条件分岐ロジック,
49,アンケート機能,アンケート作成,プレビュー機能,アンケートのプレビューが正常に表示されること,"1. アンケート作成画面でプレビューボタンをクリック
2. プレビュー画面が表示されることを確認
3. 各質問項目が正しく表示されることを確認
4. 条件分岐が正しく動作することを確認
5. 回答入力が正常に動作することを確認",プレビュー機能,
50,アンケート機能,アンケート管理,一覧表示,作成済みアンケート一覧が正しく表示されること,"1. アンケート一覧ページにアクセス
2. 作成済みアンケートが一覧表示されることを確認
3. アンケート名、作成日、ステータスが表示されることを確認
4. 検索・フィルタ機能が動作することを確認
5. ページネーションが動作することを確認",SurveyStatus enum,
51,アンケート機能,アンケート管理,公開設定,アンケートの公開・非公開が正常に設定できること,"1. アンケート一覧から対象を選択
2. 公開設定ボタンをクリック
3. 公開設定を有効化
4. 公開URLが生成されることを確認
5. 非公開設定に変更
6. アクセスできなくなることを確認",公開制御機能,
52,アンケート機能,アンケート管理,編集機能,既存アンケートが正常に編集できること,"1. アンケート一覧から編集対象を選択
2. 編集ボタンをクリック
3. アンケート内容を変更
4. 質問項目を追加・削除
5. 保存ボタンをクリック
6. 変更が反映されることを確認",編集機能,
53,アンケート機能,アンケート管理,複製機能,アンケートの複製が正常に動作すること,"1. アンケート一覧から複製対象を選択
2. 複製ボタンをクリック
3. 複製されたアンケートが作成されることを確認
4. 元のアンケートと同じ内容であることを確認
5. 複製後に編集可能であることを確認",複製機能,
54,アンケート機能,アンケート管理,削除機能,アンケートの削除が正常に動作すること,"1. アンケート一覧から削除対象を選択
2. 削除ボタンをクリック
3. 確認ダイアログが表示されることを確認
4. 削除を確定
5. アンケートが削除されることを確認
6. 関連データも削除されることを確認",削除機能,
55,アンケート機能,回答収集,回答送信,相談者がアンケートに正常に回答できること,"1. 公開されたアンケートURLにアクセス
2. 各質問項目に回答を入力
3. 必須項目のバリデーションを確認
4. 送信ボタンをクリック
5. 回答が正常に送信されることを確認
6. 完了画面が表示されることを確認",回答データ保存,
56,アンケート機能,回答収集,バリデーション,回答入力時のバリデーションが正常に動作すること,"1. アンケート回答画面で必須項目を空のまま送信
2. バリデーションエラーが表示されることを確認
3. 数値項目に文字列を入力
4. 形式エラーが表示されることを確認
5. 正しい形式で入力し直して送信成功することを確認",入力バリデーション,
57,アンケート機能,回答収集,条件分岐,回答に応じた条件分岐が正常に動作すること,"1. 条件分岐が設定されたアンケートに回答
2. 分岐条件に該当する回答を選択
3. 次の質問が条件に応じて表示されることを確認
4. 異なる回答を選択
5. 別の分岐先が表示されることを確認",分岐ロジック,
58,アンケート機能,結果管理,結果一覧,アンケート回答結果一覧が正しく表示されること,"1. アンケート管理画面で結果表示を選択
2. 回答結果一覧が表示されることを確認
3. 回答者情報、回答日時が表示されることを確認
4. 検索・フィルタ機能が動作することを確認
5. ページネーションが動作することを確認",結果管理,
59,アンケート機能,結果管理,結果詳細,個別回答結果の詳細が正しく表示されること,"1. 回答結果一覧から詳細表示を選択
2. 個別回答の詳細が表示されることを確認
3. 各質問の回答内容が正しく表示されることを確認
4. 回答時刻、所要時間が表示されることを確認",詳細表示,
60,アンケート機能,結果管理,統計情報,アンケート結果の統計情報が正しく表示されること,"1. アンケート結果画面で統計タブを選択
2. 回答数、回答率が表示されることを確認
3. 選択肢別の集計結果が表示されることを確認
4. グラフ・チャートが正しく表示されることを確認
5. 統計データのエクスポートが可能なことを確認",統計機能,
61,アンケート機能,結果管理,エクスポート,アンケート結果のエクスポートが正常に動作すること,"1. アンケート結果画面でエクスポートボタンをクリック
2. エクスポート形式を選択（CSV、Excel等）
3. エクスポート条件を設定
4. エクスポートを実行
5. ファイルがダウンロードされることを確認
6. ファイル内容が正しいことを確認",データエクスポート,
62,ウィザード機能,ウィザード作成,フロー作成,ウィザードフローが正常に作成できること,"1. /app/wizardsページにアクセス
2. 新規作成ボタンをクリック
3. ウィザード名を入力
4. ステップを追加してフローを構築
5. 各ステップの設定を行う
6. 保存ボタンをクリック
7. ウィザードが作成されることを確認",ウィザード作成,
63,ウィザード機能,ウィザード作成,ステップ設定,ウィザードステップが正常に設定できること,"1. ウィザード作成画面でステップ追加ボタンをクリック
2. ステップタイプを選択（質問、情報表示等）
3. ステップ内容を設定
4. 次のステップとの接続を設定
5. ステップ設定が保存されることを確認",ステップ管理,
64,ウィザード機能,ウィザード作成,条件分岐,ウィザードに条件分岐が正常に設定できること,"1. ウィザード作成画面で分岐ステップを追加
2. 分岐条件を設定（例：前のステップの回答が特定値の場合）
3. 条件に応じた次のステップを設定
4. 複数の分岐パターンを設定
5. プレビュー機能で分岐動作を確認",条件分岐設定,
65,ウィザード機能,ウィザード作成,プレビュー,ウィザードのプレビューが正常に表示されること,"1. ウィザード作成画面でプレビューボタンをクリック
2. プレビュー画面が表示されることを確認
3. 各ステップが正しく表示されることを確認
4. ステップ間の遷移が正しく動作することを確認
5. 条件分岐が正しく動作することを確認",プレビュー機能,
66,ウィザード機能,ウィザード管理,一覧表示,作成済みウィザード一覧が正しく表示されること,"1. ウィザード一覧ページにアクセス
2. 作成済みウィザードが一覧表示されることを確認
3. ウィザード名、作成日、ステータスが表示されることを確認
4. 検索・フィルタ機能が動作することを確認
5. ページネーションが動作することを確認",ウィザード管理,
67,ウィザード機能,ウィザード管理,公開設定,ウィザードの公開・非公開が正常に設定できること,"1. ウィザード一覧から対象を選択
2. 公開設定ボタンをクリック
3. 公開設定を有効化
4. 公開URLが生成されることを確認
5. 非公開設定に変更
6. アクセスできなくなることを確認",公開制御,
68,ウィザード機能,ウィザード実行,ステップ実行,ウィザードが正常に実行されること,"1. 公開されたウィザードにアクセス
2. 開始ボタンをクリック
3. 各ステップを順次実行
4. 入力内容に応じて次のステップに進むことを確認
5. 最終ステップまで完了できることを確認",実行フロー,
69,ウィザード機能,ウィザード実行,データ保存,ウィザード実行中のデータが正常に保存されること,"1. ウィザード実行中に各ステップで入力
2. 途中でブラウザを閉じる
3. 再度アクセスして続きから実行できることを確認
4. 入力データが保持されていることを確認",データ永続化,
70,ウィザード機能,ウィザード実行,完了処理,ウィザード完了処理が正常に動作すること,"1. ウィザードを最終ステップまで実行
2. 完了ボタンをクリック
3. 完了画面が表示されることを確認
4. 実行結果が保存されることを確認
5. 完了通知が送信されることを確認",完了処理,
71,ウィザード機能,結果管理,結果一覧,ウィザード実行結果一覧が正しく表示されること,"1. ウィザード管理画面で結果表示を選択
2. 実行結果一覧が表示されることを確認
3. 実行者情報、実行日時が表示されることを確認
4. 検索・フィルタ機能が動作することを確認
5. ページネーションが動作することを確認",結果一覧,
72,ウィザード機能,結果管理,結果詳細,個別実行結果の詳細が正しく表示されること,"1. 実行結果一覧から詳細表示を選択
2. 個別実行結果の詳細が表示されることを確認
3. 各ステップの実行内容が正しく表示されることを確認
4. 実行時刻、所要時間が表示されることを確認",結果詳細,
73,ウィザード機能,結果管理,エクスポート,ウィザード結果のエクスポートが正常に動作すること,"1. ウィザード結果画面でエクスポートボタンをクリック
2. エクスポート形式を選択
3. エクスポート条件を設定
4. エクスポートを実行
5. ファイルがダウンロードされることを確認
6. ファイル内容が正しいことを確認",結果エクスポート,
74,チャットボット機能,ボット設定,基本設定,チャットボットの基本設定が正常にできること,"1. /app/chatbot/chatbotページにアクセス
2. チャットボット名を入力
3. ウェルカムメッセージを設定
4. 有効化設定を行う
5. 保存ボタンをクリック
6. 設定が保存されることを確認",基本設定,
75,チャットボット機能,ボット設定,自動応答設定,自動応答機能が正常に設定できること,"1. 自動応答設定セクションにアクセス
2. 自動応答を有効化
3. 応答待機時間を設定（1-300秒）
4. 営業時間外メッセージを設定
5. 保存ボタンをクリック
6. 設定が保存されることを確認",自動応答設定,
76,チャットボット機能,ボット設定,キーワード応答,キーワード自動応答が正常に設定できること,"1. キーワード応答設定セクションにアクセス
2. 新規キーワード応答を追加
3. トリガーキーワードを設定
4. 応答メッセージを設定
5. 保存ボタンをクリック
6. 設定が保存されることを確認",キーワード応答,
77,チャットボット機能,シナリオ管理,シナリオ作成,チャットボットシナリオが正常に作成できること,"1. /app/chatbot/scenarioページにアクセス
2. 新規シナリオ作成ボタンをクリック
3. シナリオ名を入力
4. トリガーキーワードを設定
5. 応答メッセージを設定
6. 保存ボタンをクリック
7. シナリオが作成されることを確認",シナリオ作成,
78,チャットボット機能,シナリオ管理,シナリオ編集,既存シナリオが正常に編集できること,"1. シナリオ一覧から編集対象を選択
2. 編集ボタンをクリック
3. シナリオ内容を変更
4. 保存ボタンをクリック
5. 変更が反映されることを確認",シナリオ編集,
79,チャットボット機能,シナリオ管理,シナリオ有効化,シナリオの有効化・無効化が正常に動作すること,"1. シナリオ一覧で有効化トグルをクリック
2. シナリオが有効化されることを確認
3. 無効化トグルをクリック
4. シナリオが無効化されることを確認
5. 実際の動作に反映されることを確認",シナリオ制御,
80,チャットボット機能,シナリオ管理,シナリオ複製,シナリオの複製が正常に動作すること,"1. シナリオ一覧から複製対象を選択
2. 複製ボタンをクリック
3. 複製されたシナリオが作成されることを確認
4. 元のシナリオと同じ内容であることを確認
5. 複製後に編集可能であることを確認",シナリオ複製,
