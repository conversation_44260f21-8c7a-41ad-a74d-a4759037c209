No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,統合テスト,シナリオ-チャットボット連携,チャットボット作成時のシナリオ参照,チャットボット作成時に作成済みシナリオを選択できること,"1. シナリオを複数作成
2. チャットボット作成画面でシナリオ選択フィールドを確認
3. 作成済みシナリオが選択肢として表示されることを確認",チャットボットとシナリオの連携,
2,統合テスト,シナリオ-チャットボット連携,有効なシナリオのみ選択可能,チャットボット作成時に有効なシナリオのみ選択できること,"1. 有効・無効のシナリオを作成
2. チャットボット作成画面でシナリオ選択
3. 有効なシナリオのみが選択肢に表示されることを確認",,
3,統合テスト,シナリオ-チャットボット連携,シナリオ削除時の影響確認,使用中のシナリオ削除時に警告が表示されること,"1. チャットボットで使用中のシナリオを削除しようとする
2. 「このシナリオは使用中です」等の警告が表示されることを確認
3. 削除が阻止されることを確認",依存関係チェック,
4,統合テスト,シナリオ-チャットボット連携,シナリオ無効化時の影響確認,使用中のシナリオ無効化時に警告が表示されること,"1. チャットボットで使用中のシナリオを無効化しようとする
2. 影響範囲に関する警告が表示されることを確認",依存関係チェック,
5,統合テスト,シナリオ実行,チャットボットでのシナリオ実行,作成したシナリオがチャットボットで正常に実行されること,"1. 質問と選択肢を設定したシナリオを作成
2. そのシナリオを使用するチャットボットを作成
3. チャットボットを実行してシナリオが動作することを確認",E2Eテスト,
6,統合テスト,シナリオ実行,質問の表示確認,シナリオの質問がチャットボットで正しく表示されること,"1. 「どのようなサポートが必要ですか？」という質問を持つシナリオを作成
2. チャットボット実行時に質問が正確に表示されることを確認",,
7,統合テスト,シナリオ実行,選択肢の表示確認,シナリオの選択肢がチャットボットで正しく表示されること,"1. 複数の選択肢を持つシナリオを作成
2. チャットボット実行時に選択肢が正確に表示されることを確認
3. 選択肢の順序が正しいことを確認",,
8,統合テスト,シナリオ実行,選択時メッセージ表示,選択肢選択時にメッセージが表示されること,"1. 選択時メッセージを設定した選択肢を持つシナリオを作成
2. チャットボットで該当選択肢を選択
3. 設定したメッセージが表示されることを確認",,
9,統合テスト,シナリオ実行,次の設問への遷移,「次の設問に進む」選択時に次の質問が表示されること,"1. 「次の設問に進む」を設定した選択肢を持つシナリオを作成
2. チャットボットで該当選択肢を選択
3. 次の質問に遷移することを確認",複数階層のシナリオテスト,
10,統合テスト,シナリオ実行,トーク開設機能,「トークを開設する」選択時にトークが開始されること,"1. 「トークを開設する」を設定した選択肢を持つシナリオを作成
2. チャットボットで該当選択肢を選択
3. 1:1トークが開始されることを確認",トーク機能との連携,
11,統合テスト,シナリオ実行,既存トーク再開,中断したトークがある状態でトーク開設選択肢を選んだ時の動作確認,"1. 中断したトークがある利用者でシナリオを実行
2. 「トークを開設する」選択肢を選択
3. 中断したトークが再開されることを確認
4. 選択時メッセージが表示されないことを確認",仕様通りの動作確認,
12,統合テスト,シナリオ実行,終了アンケート表示,「終了アンケートを表示する」選択時にアンケートが表示されること,"1. 「終了アンケートを表示する」を設定した選択肢を持つシナリオを作成
2. チャットボットで該当選択肢を選択
3. 終了アンケートが表示されることを確認",アンケート機能との連携,
13,統合テスト,シナリオ実行,シナリオ終了,「終了する」選択時にシナリオが終了すること,"1. 2階層目以降で「終了する」を設定した選択肢を持つシナリオを作成
2. チャットボットで該当選択肢を選択
3. シナリオが終了することを確認",,
14,統合テスト,権限管理,シナリオ管理権限,適切な権限を持つユーザーのみシナリオを管理できること,"1. 各権限レベルのユーザーでログイン
2. シナリオ管理画面へのアクセス可否を確認
3. 作成・編集・削除権限を確認",権限システムとの連携,
15,統合テスト,権限管理,閲覧専用権限,閲覧専用権限ユーザーは参照のみ可能であること,"1. 閲覧専用権限でログイン
2. シナリオ一覧を参照できることを確認
3. 作成・編集・削除ボタンが非表示または無効化されていることを確認",,
16,統合テスト,データ整合性,シナリオ統計更新,質問・選択肢追加時に統計が正しく更新されること,"1. シナリオを作成
2. 質問と選択肢を追加
3. シナリオ一覧の統計（質問数・選択肢数）が正しく更新されることを確認",,
17,統合テスト,データ整合性,リアルタイム更新,他のユーザーがシナリオを更新した時のリアルタイム反映,"1. 2つのブラウザで同じシナリオ一覧を開く
2. 片方でシナリオを作成・更新
3. もう片方でリアルタイムに反映されることを確認",WebSocketやポーリングが実装されている場合,
18,統合テスト,パフォーマンス,大量シナリオ表示,大量のシナリオがある場合でもパフォーマンスが維持されること,"1. 100個以上のシナリオを作成
2. シナリオ一覧の表示速度を確認
3. ページネーションが正常に動作することを確認",パフォーマンステスト,
19,統合テスト,パフォーマンス,複雑なシナリオ処理,複雑な階層構造を持つシナリオでもパフォーマンスが維持されること,"1. 多階層の複雑なシナリオを作成
2. シナリオ実行時のレスポンス時間を測定
3. 許容範囲内であることを確認",,
20,統合テスト,エラーハンドリング,サーバーエラー時の対応,サーバーエラー発生時に適切なエラーハンドリングがされること,"1. サーバーエラーを発生させる
2. ユーザーフレンドリーなエラーメッセージが表示されることを確認
3. 適切な復旧手段が提示されることを確認",,
21,統合テスト,エラーハンドリング,ネットワーク切断時の対応,ネットワーク切断時に適切な処理がされること,"1. ネットワークを切断した状態で操作を実行
2. オフライン状態であることが通知されることを確認
3. ネットワーク復旧時に自動で同期されることを確認",オフライン対応が実装されている場合,
22,統合テスト,セキュリティ,認証要求,未認証ユーザーのシナリオ管理画面アクセス時の処理,"1. ログアウト状態でシナリオ管理画面にアクセス
2. ログイン画面にリダイレクトされることを確認",,
23,統合テスト,セキュリティ,セッション切れ対応,セッション切れ時の適切な処理,"1. 長時間操作せずセッションを切らす
2. 操作実行時にセッション切れが検知されることを確認
3. 適切にログイン画面にリダイレクトされることを確認",,
24,統合テスト,バックアップ・復旧,データバックアップ,シナリオデータが適切にバックアップされること,"1. シナリオを作成・更新
2. バックアップ処理が実行されることを確認",バックアップシステムが存在する場合,
25,統合テスト,バックアップ・復旧,データ復旧,バックアップからのデータ復旧が正常に動作すること,"1. データを破損させる
2. バックアップからの復旧を実行
3. シナリオが正常に復旧されることを確認",バックアップシステムが存在する場合,
26,統合テスト,多言語対応,日本語表示,日本語環境でのシナリオ管理が正常に動作すること,"1. ブラウザ言語を日本語に設定
2. シナリオ管理画面を表示
3. 全ての表示が日本語であることを確認",,
27,統合テスト,多言語対応,英語表示,英語環境でのシナリオ管理が正常に動作すること,"1. ブラウザ言語を英語に設定
2. シナリオ管理画面を表示
3. 全ての表示が英語であることを確認",多言語対応が実装されている場合,
28,統合テスト,ログ記録,操作ログ記録,シナリオ操作がログに記録されること,"1. シナリオの作成・編集・削除を実行
2. 操作ログが適切に記録されることを確認",ログシステムが存在する場合,
29,統合テスト,ログ記録,監査ログ,重要な操作が監査ログに記録されること,"1. シナリオ削除を実行
2. 監査ログに削除操作が記録されることを確認
3. 実行者・実行時間が記録されることを確認",,
30,統合テスト,外部連携,LINE連携,作成したシナリオがLINEチャットボットで動作すること,"1. シナリオを作成
2. LINEチャットボットに設定
3. LINE上でシナリオが正常に動作することを確認",LINE API連携,
31,統合テスト,外部連携,LIFF連携,LIFFアプリ内でのシナリオ動作確認,"1. シナリオを作成
2. LIFFアプリでシナリオを実行
3. LIFF環境で正常に動作することを確認",LIFF連携,
32,統合テスト,エクスポート・インポート,シナリオエクスポート,作成したシナリオをエクスポートできること,"1. シナリオを作成
2. エクスポート機能を実行
3. シナリオデータが適切な形式でエクスポートされることを確認",エクスポート機能が存在する場合,
33,統合テスト,エクスポート・インポート,シナリオインポート,エクスポートしたシナリオをインポートできること,"1. エクスポートしたシナリオファイルを用意
2. インポート機能を実行
3. シナリオが正常に復元されることを確認",インポート機能が存在する場合,
34,統合テスト,テンプレート連携,終了テンプレート選択,シナリオから終了テンプレートが選択できること,"1. 終了テンプレートを事前に作成
2. シナリオの選択肢で「終了テンプレート選択」を設定
3. 作成済みテンプレートが選択肢として表示されることを確認",終了テンプレート機能との連携,
35,統合テスト,検索・フィルター,シナリオ検索,シナリオ名での検索機能が正常に動作すること,"1. 複数のシナリオを作成
2. 検索フィールドでシナリオ名を検索
3. 該当するシナリオのみが表示されることを確認",検索機能が実装されている場合,
36,統合テスト,検索・フィルター,ステータスフィルター,ステータスでのフィルター機能が正常に動作すること,"1. 有効・無効のシナリオを作成
2. ステータスフィルターを適用
3. 該当するステータスのシナリオのみが表示されることを確認",フィルター機能が実装されている場合,
37,統合テスト,通知システム,シナリオ更新通知,シナリオ更新時に関係者に通知が送信されること,"1. シナリオを更新
2. 関係者に更新通知が送信されることを確認",通知システムが存在する場合,
38,統合テスト,APIテスト,REST API動作確認,シナリオ管理のREST APIが正常に動作すること,"1. API経由でシナリオを作成
2. 作成されたシナリオが画面に反映されることを確認
3. API経由での更新・削除も正常に動作することを確認",API仕様書との照合,
39,統合テスト,同時接続,複数ユーザー同時操作,複数ユーザーが同時にシナリオ操作した時の動作確認,"1. 複数のブラウザで同時にシナリオを操作
2. データの整合性が保たれることを確認
3. 競合状態が適切に処理されることを確認",同時接続テスト,
40,統合テスト,E2E,エンドツーエンドシナリオ,シナリオ作成から実行までの一連の流れが正常に動作すること,"1. 新規でシナリオを作成
2. 質問と選択肢を設定
3. チャットボットに適用
4. 実際のユーザーがシナリオを実行
5. 期待通りの結果が得られることを確認",完全なE2Eテストシナリオ,