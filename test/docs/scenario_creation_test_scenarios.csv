No,大項目,中項目,小項目,期待動作,手順,備考,結果
1,シナリオ管理,シナリオ一覧,画面表示,シナリオ一覧画面が正常に表示されること,"1. /app/chatbot/scenarioページにアクセス
2. シナリオ一覧のテーブルが表示されることを確認
3. ヘッダー（シナリオ名、説明、ステータス、統計、作成日時、更新日時、アクション）が表示されることを確認",権限必要,
2,シナリオ管理,シナリオ一覧,新規作成ボタン,新規作成ボタンが表示され、クリック可能であること,"1. シナリオ一覧画面で新規作成ボタンを確認
2. ボタンをクリック
3. シナリオ作成モーダルが開くことを確認",,
3,シナリオ管理,シナリオ一覧,ページネーション,ページネーションが正常に動作すること,"1. シナリオ数が10個以上の状態で確認
2. ページネーションボタンをクリック
3. 次のページのデータが表示されることを確認",,
4,シナリオ管理,シナリオ一覧,ソート機能,列のソートが正常に動作すること,"1. シナリオ名の列ヘッダーをクリック
2. 昇順でソートされることを確認
3. 再度クリックして降順でソートされることを確認",,
5,シナリオ管理,シナリオ一覧,リロード機能,リロードボタンでデータが更新されること,"1. リロードボタンをクリック
2. ローディング状態が表示されることを確認
3. データが再読み込みされることを確認",,
6,シナリオ管理,シナリオ作成,モーダル表示,シナリオ作成モーダルが正常に表示されること,"1. 新規作成ボタンをクリック
2. モーダルが開くことを確認
3. シナリオ名、説明、ステータスの入力フィールドが表示されることを確認",,
7,シナリオ管理,シナリオ作成,シナリオ名入力,シナリオ名が正常に入力できること,"1. シナリオ作成モーダルでシナリオ名フィールドをクリック
2. 任意のシナリオ名を入力
3. 入力内容が正常に反映されることを確認",,
8,シナリオ管理,シナリオ作成,説明入力,説明が正常に入力できること,"1. シナリオ作成モーダルで説明フィールドをクリック
2. 任意の説明を入力
3. 入力内容が正常に反映されることを確認",,
9,シナリオ管理,シナリオ作成,ステータス切り替え,ステータスのトグルが正常に動作すること,"1. シナリオ作成モーダルでステータストグルをクリック
2. 有効/無効が切り替わることを確認
3. 表示テキストが変更されることを確認",,
10,シナリオ管理,シナリオ作成,正常作成,シナリオが正常に作成されること,"1. シナリオ名に「テストシナリオ」と入力
2. 説明に「テスト用のシナリオです」と入力
3. 作成ボタンをクリック
4. 成功メッセージが表示されることを確認
5. シナリオ一覧に新しいシナリオが追加されることを確認",,
11,シナリオ管理,シナリオ作成,必須項目バリデーション,シナリオ名が未入力の場合エラーになること,"1. シナリオ名を空白のまま作成ボタンをクリック
2. バリデーションエラーが表示されることを確認",シナリオ名は必須項目,
12,シナリオ管理,シナリオ作成,キャンセル機能,キャンセルボタンでモーダルが閉じること,"1. シナリオ作成モーダルでキャンセルボタンをクリック
2. モーダルが閉じることを確認
3. 入力内容がリセットされることを確認",,
13,シナリオ管理,シナリオ編集,編集モーダル表示,編集モーダルが正常に表示されること,"1. シナリオ一覧で編集ボタンをクリック
2. 編集モーダルが開くことを確認
3. 既存のデータが入力フィールドに反映されることを確認",,
14,シナリオ管理,シナリオ編集,データ更新,シナリオデータが正常に更新されること,"1. 編集モーダルでシナリオ名を「更新されたシナリオ」に変更
2. 更新ボタンをクリック
3. 成功メッセージが表示されることを確認
4. シナリオ一覧で変更が反映されることを確認",,
15,シナリオ管理,シナリオ削除,削除確認,削除ボタンで確認ダイアログが表示されること,"1. シナリオ一覧で削除ボタンをクリック
2. 確認ダイアログが表示されることを確認",実装時は適切な確認ダイアログコンポーネントを使用,
16,シナリオ管理,シナリオ削除,削除実行,シナリオが正常に削除されること,"1. 削除ボタンをクリック
2. 確認ダイアログでOKをクリック
3. 成功メッセージが表示されることを確認
4. シナリオ一覧から削除されることを確認",,
17,シナリオ管理,ステータス切り替え,有効化,無効なシナリオを有効化できること,"1. 無効状態のシナリオの有効化ボタンをクリック
2. ステータスが有効に変更されることを確認
3. 成功メッセージが表示されることを確認",,
18,シナリオ管理,ステータス切り替え,無効化,有効なシナリオを無効化できること,"1. 有効状態のシナリオの無効化ボタンをクリック
2. ステータスが無効に変更されることを確認
3. 成功メッセージが表示されることを確認",,
19,質問設定,設定モーダル表示,モーダル表示,質問設定モーダルが正常に表示されること,"1. シナリオ一覧で設定ボタンをクリック
2. 質問設定モーダルが開くことを確認
3. 質問入力フィールドと選択肢セクションが表示されることを確認",,
20,質問設定,質問入力,正常入力,質問が正常に入力できること,"1. 質問設定モーダルで質問フィールドをクリック
2. 「どのようなサポートが必要ですか？」と入力
3. 入力内容が正常に反映されることを確認",,
21,質問設定,質問入力,文字数制限,質問の文字数制限（100文字）が正常に動作すること,"1. 質問フィールドに101文字以上を入力しようとする
2. 100文字で入力が制限されることを確認
3. 文字数カウンターが表示されることを確認",仕様: 100文字以内,
22,質問設定,質問入力,文字数表示,質問の文字数カウンターが正常に表示されること,"1. 質問フィールドに文字を入力
2. 「○○/100文字」の形式でカウンターが表示されることを確認
3. 文字数に応じてカウンターが更新されることを確認",,
23,質問設定,選択肢管理,初期状態,初期状態で1つの選択肢が表示されること,"1. 質問設定モーダルを開く
2. 選択肢セクションに1つの選択肢フォームが表示されることを確認",,
24,質問設定,選択肢管理,選択肢追加,選択肢を追加できること,"1. 「選択肢を追加」ボタンをクリック
2. 新しい選択肢フォームが追加されることを確認
3. 最大4つまで追加できることを確認",仕様: 最大4つの選択肢,
25,質問設定,選択肢管理,選択肢削除,選択肢を削除できること,"1. 複数の選択肢がある状態で削除ボタンをクリック
2. 該当する選択肢が削除されることを確認
3. 最後の1つは削除できないことを確認",,
26,質問設定,選択肢管理,最大数制限,選択肢の最大数制限が正常に動作すること,"1. 選択肢を4つまで追加
2. 「選択肢を追加」ボタンが無効になることを確認",仕様: 最大4つの選択肢,
27,質問設定,選択肢入力,選択肢名入力,選択肢名が正常に入力できること,"1. 選択肢名フィールドに「技術的な問題」と入力
2. 入力内容が正常に反映されることを確認",,
28,質問設定,選択肢入力,選択肢名文字数制限,選択肢名の文字数制限（150文字）が正常に動作すること,"1. 選択肢名フィールドに151文字以上を入力しようとする
2. 150文字で入力が制限されることを確認
3. 文字数カウンターが表示されることを確認",仕様: 全角150文字以内,
29,質問設定,選択肢入力,選択時メッセージ入力,選択時メッセージが正常に入力できること,"1. 選択時メッセージフィールドに「技術的な問題についてお聞かせください」と入力
2. 入力内容が正常に反映されることを確認",,
30,質問設定,選択肢動作設定,動作選択,選択後の動作が選択できること,"1. 動作選択ドロップダウンをクリック
2. 4つの選択肢（終了アンケート表示、トーク開設、次の設問、終了）が表示されることを確認
3. いずれかを選択できることを確認",仕様の4つの動作から選択,
31,質問設定,選択肢動作設定,終了アンケート設定,「終了アンケートを表示する」が選択できること,"1. 動作選択で「終了アンケートを表示する」を選択
2. 選択内容が正常に反映されることを確認",,
32,質問設定,選択肢動作設定,トーク開設設定,「トークを開設する」が選択できること,"1. 動作選択で「トークを開設する」を選択
2. 選択内容が正常に反映されることを確認",,
33,質問設定,選択肢動作設定,次の設問設定,「次の設問に進む」が選択できること,"1. 動作選択で「次の設問に進む」を選択
2. 選択内容が正常に反映されることを確認",,
34,質問設定,選択肢動作設定,終了設定,「終了する」が選択できること,"1. 動作選択で「終了する」を選択
2. 選択内容が正常に反映されることを確認",2階層目以降で設定可能,
35,質問設定,保存機能,正常保存,質問と選択肢が正常に保存されること,"1. 質問に「どのようなサポートが必要ですか？」と入力
2. 選択肢1に「技術的な問題」、動作「次の設問に進む」を設定
3. 選択肢2に「一般的な質問」、動作「トークを開設する」を設定
4. 保存ボタンをクリック
5. 成功メッセージが表示されることを確認",,
36,質問設定,バリデーション,質問必須チェック,質問が未入力の場合エラーになること,"1. 質問フィールドを空白のまま保存ボタンをクリック
2. バリデーションエラーが表示されることを確認",,
37,質問設定,バリデーション,選択肢必須チェック,選択肢名が未入力の場合エラーになること,"1. 選択肢名を空白のまま保存ボタンをクリック
2. バリデーションエラーが表示されることを確認",,
38,質問設定,バリデーション,動作選択必須チェック,選択後の動作が未選択の場合エラーになること,"1. 選択後の動作を未選択のまま保存ボタンをクリック
2. バリデーションエラーが表示されることを確認",,
39,質問設定,キャンセル機能,キャンセル,キャンセルボタンでモーダルが閉じること,"1. 質問設定モーダルでキャンセルボタンをクリック
2. モーダルが閉じることを確認
3. 入力内容がリセットされることを確認",,
40,シナリオ管理,空の状態,空のメッセージ表示,シナリオが存在しない場合、適切なメッセージが表示されること,"1. シナリオを全て削除した状態でページを表示
2. 「シナリオがありません」のメッセージが表示されることを確認
3. アイコンと共に表示されることを確認",,
41,シナリオ管理,ローディング状態,ローディング表示,データ読み込み中にローディング状態が表示されること,"1. ページを表示
2. データ読み込み中にローディングスピナーが表示されることを確認
3. データ読み込み完了後にテーブルが表示されることを確認",,
42,シナリオ管理,エラーハンドリング,ネットワークエラー,ネットワークエラー時に適切なメッセージが表示されること,"1. ネットワークを切断した状態で操作を実行
2. エラーメッセージが表示されることを確認",エラーハンドリングの実装が必要,
43,シナリオ管理,統計表示,質問数表示,シナリオの質問数が正確に表示されること,"1. 質問を設定したシナリオの統計を確認
2. 設定した質問数が正確に表示されることを確認",,
44,シナリオ管理,統計表示,選択肢数表示,シナリオの選択肢数が正確に表示されること,"1. 選択肢を設定したシナリオの統計を確認
2. 設定した選択肢数が正確に表示されることを確認",,
45,シナリオ管理,日時表示,作成日時表示,シナリオの作成日時が正しい形式で表示されること,"1. シナリオの作成日時を確認
2. 「YYYY/MM/DD HH:MM」の形式で表示されることを確認",,
46,シナリオ管理,日時表示,更新日時表示,シナリオの更新日時が正しい形式で表示されること,"1. シナリオを更新
2. 更新日時が「YYYY/MM/DD HH:MM」の形式で表示されることを確認
3. 実際の更新時刻と一致することを確認",,
47,シナリオ管理,レスポンシブ,モバイル表示,モバイル画面でも適切に表示されること,"1. ブラウザの画面幅を狭くしてモバイル表示にする
2. テーブルとボタンが適切に表示されることを確認
3. モーダルが画面に収まることを確認",レスポンシブデザインの確認,
48,質問設定,複数質問管理,質問の階層構造,「次の設問に進む」選択時の階層管理ができること,"1. 最初の質問を作成し、選択肢で「次の設問に進む」を選択
2. 次の質問が作成できることを確認
3. 質問の階層構造が管理されることを確認",シナリオの階層構造管理,
49,質問設定,シナリオフロー,フロー可視化,作成したシナリオのフローが可視化されること,"1. 複数の質問と選択肢を持つシナリオを作成
2. シナリオのフロー図が表示されることを確認",フロー可視化機能があれば,
50,質問設定,プレビュー機能,シナリオプレビュー,作成したシナリオをプレビューできること,"1. シナリオを作成
2. プレビュー機能でシナリオの動作を確認
3. 実際のチャットボットでの表示を確認",プレビュー機能があれば,