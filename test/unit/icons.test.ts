import { describe, it, expect, vi } from 'vitest'
import { SNSChannel, TagFormType } from '~/types/enums.d.ts'
import {
  getSNSIconComponent,
  getFormTempalteIcon
} from '~/utils/icons'

// Mock SVG imports
vi.mock('~/assets/images/facebook.svg', () => ({
  default: 'FacebookSvg'
}))

vi.mock('~/assets/images/line.svg', () => ({
  default: 'LineSvg'
}))

vi.mock('~/assets/images/application.svg', () => ({
  default: 'ApplicationSvg'
}))

describe('icons utils', () => {
  describe('getSNSIconComponent', () => {
    it('should return FacebookSvg for FACEBOOK channel', () => {
      const result = getSNSIconComponent(SNSChannel.FACEBOOK)
      expect(result).toBe('FacebookSvg')
    })

    it('should return LineSvg for LINE channel', () => {
      const result = getSNSIconComponent(SNSChannel.LINE)
      expect(result).toBe('LineSvg')
    })

    it('should return ApplicationSvg for APPLICATION channel', () => {
      const result = getSNSIconComponent(SNSChannel.APPLICATION)
      expect(result).toBe('ApplicationSvg')
    })

    it('should return ApplicationSvg for unknown channel', () => {
      const result = getSNSIconComponent('unknown')
      expect(result).toBe('ApplicationSvg')
    })

    it('should return ApplicationSvg for undefined channel', () => {
      const result = getSNSIconComponent(undefined)
      expect(result).toBe('ApplicationSvg')
    })
  })

  describe('getFormTempalteIcon', () => {
    it('should return correct icon for TEXT form type', () => {
      const result = getFormTempalteIcon(TagFormType.TEXT)
      expect(result).toBe('i-ph-textbox')
    })

    it('should return correct icon for CHECKBOX form type', () => {
      const result = getFormTempalteIcon(TagFormType.CHECKBOX)
      expect(result).toBe('i-mdi-checkbox-outline')
    })

    it('should return correct icon for PULLDOWN form type', () => {
      const result = getFormTempalteIcon(TagFormType.PULLDOWN)
      expect(result).toBe('i-tabler-select')
    })

    it('should return correct icon for RADIO form type', () => {
      const result = getFormTempalteIcon(TagFormType.RADIO)
      expect(result).toBe('i-formkit-radio')
    })

    it('should return correct icon for TEXT_AND_PULLDOWN form type', () => {
      const result = getFormTempalteIcon(TagFormType.TEXT_AND_PULLDOWN)
      expect(result).toBe('i-gala-select')
    })

    it('should return default icon for unknown form type', () => {
      const result = getFormTempalteIcon('unknown')
      expect(result).toBe('i-heroicons-question-mark-circle')
    })

    it('should return default icon for undefined form type', () => {
      const result = getFormTempalteIcon(undefined)
      expect(result).toBe('i-heroicons-question-mark-circle')
    })
  })
})
