import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import dayjs from 'dayjs'
import {
  fromNowByTimestamp,
  fromNow,
  fromNowByHour,
  formatDate,
  formatDateForMessage,
  fromNowByDatetime,
  parseTimeToObject,
  parseTimeToString,
  parseYearMonthToString,
  isValidDate,
  nowDate,
  isChatAvailableTime
} from '~/utils/dateTimes'

// Mock useRuntimeConfig
const mockRuntimeConfig = {
  public: {
    hamamatsuChatAvailable: {
      days: [1, 2, 3, 4, 5], // Monday to Friday
      times: [9, 10, 11, 12, 13, 14, 15, 16, 17], // 9 AM to 5 PM
      dates: [
        ['2024-01-01', '2024-01-31'],
        ['2024-06-01', '2024-06-30']
      ],
      hardAvailable: false
    }
  }
}

vi.mock('#app', () => ({
  useRuntimeConfig: () => mockRuntimeConfig
}))

// Don't mock dayjs at the module level to avoid conflicts with extend() calls

describe('dateTimes utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('fromNowByTimestamp', () => {
    it('should return relative time for valid timestamp', () => {
      const timestamp = dayjs().subtract(1, 'hour').unix()
      const result = fromNowByTimestamp(timestamp)
      expect(result).toContain('時間前')
    })

    it('should return empty string for falsy timestamp', () => {
      expect(fromNowByTimestamp(0)).toBe('')
      expect(fromNowByTimestamp(null as any)).toBe('')
      expect(fromNowByTimestamp(undefined as any)).toBe('')
    })
  })

  describe('fromNow', () => {
    it('should return relative time for valid date', () => {
      const date = new Date(Date.now() - 3600000) // 1 hour ago
      const result = fromNow(date)
      expect(result).toContain('時間前')
    })

    it('should return empty string for falsy date', () => {
      expect(fromNow(null as any)).toBe('')
      expect(fromNow(undefined as any)).toBe('')
    })
  })

  describe('fromNowByHour', () => {
    it('should return relative time for recent date (within 24 hours)', () => {
      const date = new Date(Date.now() - 3600000) // 1 hour ago
      const result = fromNowByHour(date)
      expect(result).toContain('時間前')
    })

    it('should return formatted date for old date (over 24 hours)', () => {
      const date = new Date(Date.now() - 86400000 * 2) // 2 days ago
      const result = fromNowByHour(date)
      expect(result).toMatch(/\d{4}年\d{2}月\d{2}日 \d{2}:\d{2}/)
    })

    it('should return empty string for falsy date', () => {
      expect(fromNowByHour(null as any)).toBe('')
      expect(fromNowByHour(undefined as any)).toBe('')
    })
  })

  describe('formatDate', () => {
    it('should format date with default format', () => {
      const date = new Date('2024-01-15T10:30:00')
      const result = formatDate(date)
      expect(result).toBe('2024年01月15日')
    })

    it('should format date with custom format', () => {
      const date = new Date('2024-01-15T10:30:00')
      const result = formatDate(date, 'YYYY-MM-DD HH:mm')
      expect(result).toBe('2024-01-15 10:30')
    })

    it('should return empty string for falsy date', () => {
      expect(formatDate(null as any)).toBe('')
      expect(formatDate(undefined as any)).toBe('')
    })
  })

  describe('formatDateForMessage', () => {
    it('should return time only for today', () => {
      const today = new Date()
      const result = formatDateForMessage(today)
      expect(result).toMatch(/^\d{2}:\d{2}$/)
    })

    it('should return month/day and time for this year', () => {
      const thisYear = new Date()
      thisYear.setMonth(thisYear.getMonth() - 1) // 1 month ago
      const result = formatDateForMessage(thisYear)
      expect(result).toMatch(/^\d{1,2}月\d{1,2}日 \d{2}:\d{2}$/)
    })

    it('should return full date for different year', () => {
      const differentYear = new Date()
      differentYear.setFullYear(differentYear.getFullYear() - 1)
      const result = formatDateForMessage(differentYear)
      expect(result).toMatch(/^\d{4}年\d{1,2}月\d{1,2}日 \d{2}:\d{2}$/)
    })

    it('should return empty string for falsy date', () => {
      expect(formatDateForMessage(null as any)).toBe('')
      expect(formatDateForMessage(undefined as any)).toBe('')
    })
  })

  describe('fromNowByDatetime', () => {
    it('should return relative time for valid datetime string', () => {
      const datetime = dayjs().subtract(2, 'hours').toISOString()
      const result = fromNowByDatetime(datetime)
      expect(result).toContain('時間前')
    })

    it('should return empty string for falsy datetime', () => {
      expect(fromNowByDatetime(undefined)).toBe('')
      expect(fromNowByDatetime('')).toBe('')
    })
  })

  describe('parseTimeToObject', () => {
    it('should parse time string to object with default format', () => {
      const result = parseTimeToObject('14:30')
      expect(result).toEqual({
        hours: 14,
        minutes: 30
      })
    })

    it('should parse time string with custom format', () => {
      const result = parseTimeToObject('2:30 PM', 'h:mm A')
      expect(result).toEqual({
        hours: 2,
        minutes: 30
      })
    })
  })

  describe('parseTimeToString', () => {
    it('should convert time object to string with default format', () => {
      const timeObj = { hours: 14, minutes: 30 }
      const result = parseTimeToString(timeObj)
      expect(result).toBe('14:30')
    })

    it('should convert time object to string with custom format', () => {
      const timeObj = { hours: 14, minutes: 30 }
      const result = parseTimeToString(timeObj, 'h:mm A')
      expect(result).toBe('2:30 午後')
    })
  })

  describe('parseYearMonthToString', () => {
    it('should format year and month with default format', () => {
      const result = parseYearMonthToString(2024, 3)
      expect(result).toBe('202403')
    })

    it('should format year and month with custom format', () => {
      const result = parseYearMonthToString(2024, 3, 'YYYY-MM')
      expect(result).toBe('2024-03')
    })

    it('should handle month adjustment correctly', () => {
      const result = parseYearMonthToString(2024, 12)
      expect(result).toBe('202412')
    })
  })

  describe('isValidDate', () => {
    it('should return true for valid date with default format', () => {
      expect(isValidDate('2024-01-15')).toBe(true)
    })

    it('should return false for invalid date with default format', () => {
      expect(isValidDate('invalid-date')).toBe(false)
    })

    it('should validate date with custom format', () => {
      expect(isValidDate('15/01/2024', 'DD/MM/YYYY')).toBe(true)
    })
  })

  describe('nowDate', () => {
    it('should return current date in YYYY-MM-DD format', () => {
      const result = nowDate()
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/)
      
      // Verify it's actually today's date
      const today = dayjs().format('YYYY-MM-DD')
      expect(result).toBe(today)
    })
  })

  describe('isChatAvailableTime', () => {
    beforeEach(() => {
      // Reset mock config
      mockRuntimeConfig.public.hamamatsuChatAvailable.hardAvailable = false
    })

    it('should return true for non-hamamatsu-city slug', () => {
      expect(isChatAvailableTime('other-city')).toBe(true)
    })

    it('should return true when hardAvailable is true', () => {
      mockRuntimeConfig.public.hamamatsuChatAvailable.hardAvailable = true
      expect(isChatAvailableTime('hamamatsu-city')).toBe(true)
    })

    it('should execute without errors for hamamatsu-city', () => {
      // Test that the function runs without throwing errors
      // The actual result depends on current time and configuration
      const result = isChatAvailableTime('hamamatsu-city')
      expect(typeof result).toBe('boolean')
    })

    it('should respect configuration settings', () => {
      // Test with different config values
      const originalDays = mockRuntimeConfig.public.hamamatsuChatAvailable.days
      const originalTimes = mockRuntimeConfig.public.hamamatsuChatAvailable.times
      
      mockRuntimeConfig.public.hamamatsuChatAvailable.days = []
      mockRuntimeConfig.public.hamamatsuChatAvailable.times = []
      
      const result = isChatAvailableTime('hamamatsu-city')
      expect(typeof result).toBe('boolean')
      
      // Restore original values
      mockRuntimeConfig.public.hamamatsuChatAvailable.days = originalDays
      mockRuntimeConfig.public.hamamatsuChatAvailable.times = originalTimes
    })
  })
})
