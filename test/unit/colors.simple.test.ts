import { describe, it, expect } from 'vitest'
import {
  getSNSColor,
  getCaseStatusColor,
  getCasePriorityColorIcon,
  getHasAlertWork,
  getConsultantRoleColor,
  getCounselorRoleColor,
  getSampleMessageTypeColor,
  getSegmentDeliveryStatusColor,
  getDayColor,
  getSurveyStatusColor,
  getColor
} from '../../utils/colors'

describe('colors utils (simple tests)', () => {
  describe('getSNSColor', () => {
    it('should return blue for facebook', () => {
      expect(getSNSColor('facebook')).toBe('blue')
    })

    it('should return green for line', () => {
      expect(getSNSColor('line')).toBe('green')
    })

    it('should return amber for application', () => {
      expect(getSNSColor('application')).toBe('amber')
    })

    it('should return primary for unknown SNS', () => {
      expect(getSNSColor('unknown')).toBe('primary')
    })
  })

  describe('getCaseStatusColor', () => {
    it('should return correct colors for case statuses', () => {
      expect(getCaseStatusColor('before_start')).toBe('yellow')
      expect(getCaseStatusColor('open')).toBe('red')
      expect(getCaseStatusColor('in_progress')).toBe('cyan')
      expect(getCaseStatusColor('waiting')).toBe('purple')
      expect(getCaseStatusColor('resolved')).toBe('green')
      expect(getCaseStatusColor('cancelled')).toBe('gray')
    })

    it('should return gray for unknown status', () => {
      expect(getCaseStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getCasePriorityColorIcon', () => {
    it('should return correct color and icon for high priority', () => {
      const result = getCasePriorityColorIcon('high')
      expect(result).toEqual({
        color: 'red',
        icon: 'i-heroicons-arrow-up'
      })
    })

    it('should return correct color and icon for normal priority', () => {
      const result = getCasePriorityColorIcon('normal')
      expect(result).toEqual({
        color: 'green',
        icon: 'i-heroicons-arrow-right'
      })
    })

    it('should return correct color and icon for low priority', () => {
      const result = getCasePriorityColorIcon('low')
      expect(result).toEqual({
        color: 'gray',
        icon: 'i-heroicons-arrow-down'
      })
    })

    it('should return default color and icon for unknown priority', () => {
      const result = getCasePriorityColorIcon('unknown')
      expect(result).toEqual({
        color: 'gray',
        icon: 'i-heroicons-arrow-right'
      })
    })
  })

  describe('getHasAlertWork', () => {
    it('should return red text class when hasAlertWork is true', () => {
      expect(getHasAlertWork(true)).toBe('text-red-500')
    })

    it('should return empty string when hasAlertWork is false', () => {
      expect(getHasAlertWork(false)).toBe('')
    })
  })

  describe('getConsultantRoleColor', () => {
    it('should return correct colors for consultant roles', () => {
      expect(getConsultantRoleColor('admin')).toBe('red')
      expect(getConsultantRoleColor('supervisor')).toBe('primary')
      expect(getConsultantRoleColor('general')).toBe('blue')
    })

    it('should return gray for unknown role', () => {
      expect(getConsultantRoleColor('unknown')).toBe('gray')
    })
  })

  describe('getCounselorRoleColor', () => {
    it('should return correct colors for counselor account roles', () => {
      expect(getCounselorRoleColor('admin')).toBe('primary')
      expect(getCounselorRoleColor('counselor')).toBe('green')
    })

    it('should return gray for unknown role', () => {
      expect(getCounselorRoleColor('unknown' as any)).toBe('gray')
    })
  })

  describe('getSampleMessageTypeColor', () => {
    it('should return orange for common type', () => {
      expect(getSampleMessageTypeColor('common')).toBe('orange')
    })

    it('should return primary for personal type', () => {
      expect(getSampleMessageTypeColor('personal')).toBe('primary')
    })

    it('should return gray for unknown type', () => {
      expect(getSampleMessageTypeColor('unknown')).toBe('gray')
    })
  })

  describe('getSegmentDeliveryStatusColor', () => {
    it('should return correct colors for segment statuses', () => {
      expect(getSegmentDeliveryStatusColor('before_start')).toBe('gray')
      expect(getSegmentDeliveryStatusColor('in_progress')).toBe('primary')
      expect(getSegmentDeliveryStatusColor('resolved')).toBe('green')
      expect(getSegmentDeliveryStatusColor('error')).toBe('red')
    })

    it('should return gray for unknown status', () => {
      expect(getSegmentDeliveryStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getDayColor', () => {
    it('should return red for Sunday (0)', () => {
      expect(getDayColor(0)).toBe('red')
    })

    it('should return blue for Saturday (6)', () => {
      expect(getDayColor(6)).toBe('blue')
    })

    it('should return gray for weekdays', () => {
      expect(getDayColor(1)).toBe('gray')
      expect(getDayColor(2)).toBe('gray')
      expect(getDayColor(3)).toBe('gray')
      expect(getDayColor(4)).toBe('gray')
      expect(getDayColor(5)).toBe('gray')
    })
  })

  describe('getSurveyStatusColor', () => {
    it('should return correct colors for survey statuses', () => {
      expect(getSurveyStatusColor('draft')).toBe('gray')
      expect(getSurveyStatusColor('active')).toBe('green')
      expect(getSurveyStatusColor('inactive')).toBe('orange')
    })

    it('should return gray for unknown status', () => {
      expect(getSurveyStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getColor', () => {
    it('should return correct hex color for known color names', () => {
      expect(getColor('red')).toBe('#f44336')
      expect(getColor('blue')).toBe('#2196f3')
      expect(getColor('green')).toBe('#4caf50')
      expect(getColor('white')).toBe('#ffffff')
      expect(getColor('black')).toBe('#000000')
    })

    it('should return undefined for unknown color names', () => {
      expect(getColor('unknown-color')).toBeUndefined()
    })

    it('should handle color variants', () => {
      expect(getColor('red-500')).toBe('#f44336')
      expect(getColor('blue-100')).toBe('#bbdefb')
    })
  })
})
