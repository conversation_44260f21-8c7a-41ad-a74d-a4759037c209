# Unit Tests for Utils Functions

このディレクトリには、`utils/` フォルダ内の各ユーティリティ関数のユニットテストが含まれています。

## テストファイル構成

### 完成したテストファイル
- `colors.simple.test.ts` - カラー関連のユーティリティ関数のテスト
- `dateTimes.simple.test.ts` - 日時関連のユーティリティ関数のテスト  
- `permissions.test.ts` - 権限関連のユーティリティ関数のテスト（現在は空のファイル用のプレースホルダー）

### 作成済みだが調整が必要なテストファイル
- `caseStatus.test.ts` - ケースステータス関連の関数のテスト
- `colors.test.ts` - カラー関連の関数のテスト（enum import問題あり）
- `dateTimes.test.ts` - 日時関連の関数のテスト（mock設定問題あり）
- `icons.test.ts` - アイコン関連の関数のテスト
- `index.test.ts` - インデックスファイルの関数のテスト
- `mocks.test.ts` - モック関数のテスト
- `utils.integration.test.ts` - 統合テスト

## テストの実行方法

### 個別のテストファイルを実行
```bash
# カラーユーティリティのテスト
npx vitest run test/unit/colors.simple.test.ts

# 日時ユーティリティのテスト
npx vitest run test/unit/dateTimes.simple.test.ts

# 権限ユーティリティのテスト
npx vitest run test/unit/permissions.test.ts
```

### 動作するすべてのテストを実行
```bash
npx vitest run test/unit/*.simple.test.ts test/unit/permissions.test.ts
```

### すべてのユニットテストを実行（一部失敗する可能性があります）
```bash
npx vitest run test/unit/
```

## テスト対象の関数

### colors.ts
- `getSNSColor()` - SNSチャンネルに応じた色を返す
- `getCaseStatusColor()` - ケースステータスに応じた色を返す
- `getCasePriorityColorIcon()` - ケース優先度に応じた色とアイコンを返す
- `getHasAlertWork()` - アラート作業の有無に応じたCSSクラスを返す
- `getConsultantRoleColor()` - コンサルタントロールに応じた色を返す
- `getCounselorRoleColor()` - カウンセラーロールに応じた色を返す
- `getSampleMessageTypeColor()` - サンプルメッセージタイプに応じた色を返す
- `getSegmentDeliveryStatusColor()` - セグメント配信ステータスに応じた色を返す
- `getDayColor()` - 曜日に応じた色を返す
- `getSurveyStatusColor()` - アンケートステータスに応じた色を返す
- `getColor()` - カラー名からHEXコードを取得

### dateTimes.ts
- `fromNowByTimestamp()` - タイムスタンプから相対時間を取得
- `fromNow()` - 日付から相対時間を取得
- `formatDate()` - 日付をフォーマット
- `parseTimeToObject()` - 時間文字列をオブジェクトに変換
- `parseTimeToString()` - 時間オブジェクトを文字列に変換
- `parseYearMonthToString()` - 年月を文字列にフォーマット
- `isValidDate()` - 日付の妥当性を検証
- `nowDate()` - 現在の日付を取得

## 注意事項

1. **Nuxt環境**: テストはNuxt環境で実行されるため、初期化に時間がかかります
2. **Enum値**: テストでは実際のenum値（snake_case、lowercase）を使用してください
3. **日本語ロケール**: dayjs は日本語ロケールで設定されているため、一部の出力が日本語になります
4. **Mock設定**: 複雑なNuxtコンポーザブルを使用する関数のテストには追加のmock設定が必要です

## 今後の改善点

1. 残りのテストファイルのimport問題を解決
2. より包括的なテストケースの追加
3. エラーハンドリングのテストケース追加
4. パフォーマンステストの追加
5. テストカバレッジの向上

## トラブルシューティング

### よくある問題

1. **Module not found エラー**: 
   - パスエイリアス（`~/`）の解決問題
   - 相対パスを使用するか、vitest設定を調整

2. **Enum import エラー**:
   - `.d.ts` ファイルのimportパス問題
   - 正しいファイル拡張子を使用

3. **Mock関連エラー**:
   - Nuxtコンポーザブルのmock設定不足
   - `setup.ts` ファイルでグローバルmockを設定

4. **Locale関連の問題**:
   - dayjs の日本語ロケール設定による出力の違い
   - 期待値を実際の出力に合わせて調整
