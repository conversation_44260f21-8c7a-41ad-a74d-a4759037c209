import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { useScenarioService } from '~/composables/useScenarioService'
import type { Scenario, EndTemplate } from '~/types'

// Mock the useAppService composable
vi.mock('~/composables/useAppService', () => ({
  useAppService: vi.fn()
}))

// Mock Nuxt composables
vi.mock('#app', () => ({
  useAppService: vi.fn(),
  ref: vi.fn((val) => ({ value: val })),
  computed: vi.fn((fn) => ({ value: fn() }))
}))

const mockScenario: Scenario = {
  id: '1',
  name: 'Test Scenario',
  description: 'Test description',
  isActive: true,
  questions: [{
    id: 'q1',
    text: 'Test question',
    choices: [{
      id: 'c1',
      text: 'Test choice',
      action: { type: 'end_scenario' }
    }],
    isFirstQuestion: true,
    order: 1
  }],
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z'
}

const mockEndTemplate: EndTemplate = {
  id: '1',
  templateName: 'Test Template',
  finalQuestion: 'Final question',
  choices: [],
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z'
}

describe('useScenarioService', () => {
  let mockAdminService: any
  
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Mock app service response
    mockAppService = {
      data: Promise.resolve({
        value: {
          scenarios: [mockScenario],
          totalCount: 1,
          scenario: mockScenario,
          endTemplates: [mockEndTemplate]
        }
      })
    }
    
    // Mock useAppService to return our mock
    const { useAppService } = await import('~/composables/useAppService')
    vi.mocked(useAppService).mockReturnValue(mockAppService)
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  it('initializes with correct default values', () => {
    const service = useScenarioService()
    
    expect(service.scenarios.value).toEqual([])
    expect(service.loading.value).toBe(false)
    expect(service.saving.value).toBe(false)
    expect(service.totalCount.value).toBe(0)
    expect(service.currentPage.value).toBe(1)
    expect(service.pageSize.value).toBe(10)
  })

  it('fetchScenarios calls admin service with correct parameters', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    await service.fetchScenarios(2, 5)
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios?page=2&limit=5')
  })

  it('fetchScenarios handles successful response', async () => {
    const service = useScenarioService()
    
    await service.fetchScenarios()
    
    expect(service.scenarios.value).toEqual([mockScenario])
    expect(service.totalCount.value).toBe(1)
    expect(service.loading.value).toBe(false)
  })

  it('fetchScenarios handles error response', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    // Mock error response
    vi.mocked(useAdminService).mockReturnValue({
      data: Promise.reject(new Error('API Error'))
    })
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    await service.fetchScenarios()
    
    expect(service.scenarios.value).toEqual([])
    expect(service.totalCount.value).toBe(0)
    expect(service.loading.value).toBe(false)
    expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch scenarios:', expect.any(Error))
    
    consoleSpy.mockRestore()
  })

  it('getScenario calls admin service with correct ID', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    await service.getScenario('123')
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios/123')
  })

  it('getScenario returns scenario on success', async () => {
    const service = useScenarioService()
    
    const result = await service.getScenario('1')
    
    expect(result).toEqual(mockScenario)
  })

  it('getScenario returns null on error', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    vi.mocked(useAdminService).mockReturnValue({
      data: Promise.reject(new Error('Not found'))
    })
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    const result = await service.getScenario('999')
    
    expect(result).toBe(null)
    expect(consoleSpy).toHaveBeenCalled()
    
    consoleSpy.mockRestore()
  })

  it('createScenario calls admin service with POST method', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    const scenarioData = { name: 'New Scenario' }
    
    await service.createScenario(scenarioData)
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios', {
      method: 'POST',
      data: scenarioData
    })
  })

  it('updateScenario calls admin service with PUT method', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    const scenarioData = { name: 'Updated Scenario' }
    
    await service.updateScenario('123', scenarioData)
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios/123', {
      method: 'PUT',
      data: scenarioData
    })
  })

  it('deleteScenario calls admin service with DELETE method', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    await service.deleteScenario('123')
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios/123', {
      method: 'DELETE'
    })
  })

  it('toggleScenario calls admin service with PATCH method', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    await service.toggleScenario('123', false)
    
    expect(useAdminService).toHaveBeenCalledWith('/scenarios/123/toggle', {
      method: 'PATCH',
      data: { isActive: false }
    })
  })

  it('fetchEndTemplates calls admin service correctly', async () => {
    const service = useScenarioService()
    const { useAdminService } = await import('~/composables/useAdminService')
    
    await service.fetchEndTemplates()
    
    expect(useAdminService).toHaveBeenCalledWith('/end-templates')
    expect(service.endTemplates.value).toEqual([mockEndTemplate])
  })

  it('manages loading states correctly', async () => {
    const service = useScenarioService()
    
    // Initial state
    expect(service.loading.value).toBe(false)
    expect(service.saving.value).toBe(false)
    expect(service.loadingEndTemplates.value).toBe(false)
    
    // Note: In a real test, we would need to mock the reactive refs
    // and test the loading state changes during async operations
  })
})