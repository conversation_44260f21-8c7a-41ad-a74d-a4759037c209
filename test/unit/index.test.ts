import { describe, it, expect, vi, beforeEach } from 'vitest'
import * as yup from 'yup'
import { AgeDecade, CounselorRole } from '~/types/enums.d.ts'
import type { SurveyFormElement } from '@/types'
import {
  generateUniqueOption,
  createYupSchema,
  getSocketStatusObject,
  getAgeDecadeLabel,
  getLinkSurvey,
  getCounselortRoleIcon,
  getSchoolName,
  isALink
} from '~/utils/index'

// Mock useConstants
const mockAgeOptions = [
  { value: AgeDecade.Teens, label: '10代' },
  { value: AgeDecade.Twenties, label: '20代' },
  { value: AgeDecade.Thirties, label: '30代' }
]

const mockSchoolList = [
  { schoolId: 'school-1', schoolName: '東京大学' },
  { schoolId: 'school-2', schoolName: '京都大学' }
]

vi.mock('#app', () => ({
  useConstants: () => ({
    ageOptions: mockAgeOptions,
    SCHOOL_LIST: mockSchoolList
  }),
  useRouter: () => ({
    resolve: vi.fn((route) => ({
      href: `/survey?sid=${route.query.sid}&c=${route.query.c}&u=${route.query.u || ''}`
    }))
  })
}))

describe('index utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('generateUniqueOption', () => {
    it('should return original label if not in options', () => {
      const options = ['Option 1', 'Option 2']
      const result = generateUniqueOption('New Option', options)
      expect(result).toBe('New Option')
    })

    it('should append number if label exists in options', () => {
      const options = ['Option 1', 'Option 2']
      const result = generateUniqueOption('Option 1', options)
      expect(result).toBe('Option 1 2')
    })

    it('should increment number until unique', () => {
      const options = ['Option 1', 'Option 1 2', 'Option 1 3']
      const result = generateUniqueOption('Option 1', options)
      expect(result).toBe('Option 1 4')
    })

    it('should handle empty options array', () => {
      const result = generateUniqueOption('Test', [])
      expect(result).toBe('Test')
    })
  })

  describe('createYupSchema', () => {
    it('should create schema for required text fields', () => {
      const formTemplate: SurveyFormElement[] = [
        {
          _id: 'field1',
          type: 'text',
          required: true
        } as SurveyFormElement,
        {
          _id: 'field2',
          type: 'text',
          required: false
        } as SurveyFormElement
      ]

      const schema = createYupSchema(formTemplate)
      expect(schema.fields).toHaveProperty('field1')
      expect(schema.fields).not.toHaveProperty('field2')
    })

    it('should create array schema for required selectMulti fields', () => {
      const formTemplate: SurveyFormElement[] = [
        {
          _id: 'multiField',
          type: 'selectMulti',
          required: true
        } as SurveyFormElement
      ]

      const schema = createYupSchema(formTemplate)
      expect(schema.fields).toHaveProperty('multiField')
      
      // Test validation
      expect(() => schema.validateSync({ multiField: [] })).toThrow('必須項目です')
      expect(() => schema.validateSync({ multiField: ['option1'] })).not.toThrow()
    })

    it('should handle empty form template', () => {
      const schema = createYupSchema([])
      expect(Object.keys(schema.fields)).toHaveLength(0)
    })
  })

  describe('getSocketStatusObject', () => {
    it('should return correct object for OPEN status', () => {
      const result = getSocketStatusObject('OPEN')
      expect(result).toEqual({
        color: 'green',
        text: '接続'
      })
    })

    it('should return correct object for CONNECTING status', () => {
      const result = getSocketStatusObject('CONNECTING')
      expect(result).toEqual({
        color: 'yellow',
        text: '接続中'
      })
    })

    it('should return correct object for CLOSED status', () => {
      const result = getSocketStatusObject('CLOSED')
      expect(result).toEqual({
        color: 'red',
        text: 'NG'
      })
    })

    it('should return default object for unknown status', () => {
      const result = getSocketStatusObject('UNKNOWN' as any)
      expect(result).toEqual({
        color: 'gray',
        text: ''
      })
    })
  })

  describe('getAgeDecadeLabel', () => {
    it('should return correct label for known age decade', () => {
      const result = getAgeDecadeLabel(AgeDecade.Twenties)
      expect(result).toBe('20代')
    })

    it('should return empty string for unknown age decade', () => {
      const result = getAgeDecadeLabel('UNKNOWN' as AgeDecade)
      expect(result).toBe('')
    })
  })

  describe('getLinkSurvey', () => {
    it('should generate survey link with all parameters', () => {
      const result = getLinkSurvey('survey-123', 'customer-456', 'counselee-789')
      expect(result).toBe('/survey?sid=survey-123&c=customer-456&u=counselee-789')
    })

    it('should generate survey link without counselee ID', () => {
      const result = getLinkSurvey('survey-123', 'customer-456')
      expect(result).toBe('/survey?sid=survey-123&c=customer-456&u=')
    })
  })

  describe('getCounselortRoleIcon', () => {
    it('should return correct icons for counselor roles', () => {
      expect(getCounselortRoleIcon(CounselorRole.ADMIN)).toBe('eos-icons:admin')
      expect(getCounselortRoleIcon(CounselorRole.SUPERVISOR)).toBe('mdi:account-star')
      expect(getCounselortRoleIcon(CounselorRole.GENERAL)).toBe('mdi:account-settings')
    })

    it('should return default icon for unknown role', () => {
      const result = getCounselortRoleIcon('unknown')
      expect(result).toBe('solar:eye-scan-bold')
    })
  })

  describe('getSchoolName', () => {
    it('should return correct school name for known school ID', () => {
      const result = getSchoolName('10001')
      expect(result).toBe('東部中学校')
    })

    it('should return "不明" for unknown school ID', () => {
      const result = getSchoolName('unknown-school')
      expect(result).toBe('不明')
    })
  })

  describe('isALink', () => {
    it('should return true for HTTP URLs', () => {
      expect(isALink('http://example.com')).toBe(true)
      expect(isALink('http://www.example.com/path')).toBe(true)
    })

    it('should return true for HTTPS URLs', () => {
      expect(isALink('https://example.com')).toBe(true)
      expect(isALink('https://www.example.com/path')).toBe(true)
    })

    it('should return false for non-URLs', () => {
      expect(isALink('example.com')).toBe(false)
      expect(isALink('ftp://example.com')).toBe(false)
      expect(isALink('not a url')).toBe(false)
      expect(isALink('')).toBe(false)
    })

    it('should return false for malformed URLs', () => {
      expect(isALink('http://')).toBe(false)
      expect(isALink('https://')).toBe(false)
      expect(isALink('http')).toBe(false)
    })
  })
})
