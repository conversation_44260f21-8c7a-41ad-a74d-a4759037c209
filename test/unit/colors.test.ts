import { describe, it, expect } from 'vitest'
import {
  CaseStatus,
  CasePriority,
  CounselorRole,
  CounselorAccountRole,
  SegmentStatus
} from '../../types/enums.d.ts'
import { SNS_CHANNELS } from '../../types'
import {
  getSNSColor,
  getCaseStatusColor,
  getCasePriorityColorIcon,
  getHasAlertWork,
  getConsultantRoleColor,
  getCounselorRoleColor,
  getSampleMessageTypeColor,
  getSegmentDeliveryStatusColor,
  getDayColor,
  getSurveyStatusColor,
  getColor
} from '../../utils/colors'

describe('colors utils', () => {
  describe('getSNSColor', () => {
    it('should return blue for FACEBOOK', () => {
      expect(getSNSColor(SNS_CHANNELS.FACEBOOK)).toBe('blue')
    })

    it('should return green for LINE', () => {
      expect(getSNSColor(SNS_CHANNELS.LINE)).toBe('green')
    })

    it('should return amber for APPLICATION', () => {
      expect(getSNSColor(SNS_CHANNELS.APPLICATION)).toBe('amber')
    })

    it('should return primary for unknown SNS', () => {
      expect(getSNSColor('unknown')).toBe('primary')
    })
  })

  describe('getCaseStatusColor', () => {
    it('should return correct colors for all case statuses', () => {
      expect(getCaseStatusColor(CaseStatus.BEFORE_START)).toBe('yellow')
      expect(getCaseStatusColor(CaseStatus.OPEN)).toBe('red')
      expect(getCaseStatusColor(CaseStatus.IN_PROGRESS)).toBe('cyan')
      expect(getCaseStatusColor(CaseStatus.WAITING)).toBe('purple')
      expect(getCaseStatusColor(CaseStatus.RESOLVED)).toBe('green')
      expect(getCaseStatusColor(CaseStatus.CANCELLED)).toBe('gray')
    })

    it('should return gray for unknown status', () => {
      expect(getCaseStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getCasePriorityColorIcon', () => {
    it('should return correct color and icon for HIGH priority', () => {
      const result = getCasePriorityColorIcon(CasePriority.HIGH)
      expect(result).toEqual({
        color: 'red',
        icon: 'i-heroicons-arrow-up'
      })
    })

    it('should return correct color and icon for NORMAL priority', () => {
      const result = getCasePriorityColorIcon(CasePriority.NORMAL)
      expect(result).toEqual({
        color: 'green',
        icon: 'i-heroicons-arrow-right'
      })
    })

    it('should return correct color and icon for LOW priority', () => {
      const result = getCasePriorityColorIcon(CasePriority.LOW)
      expect(result).toEqual({
        color: 'gray',
        icon: 'i-heroicons-arrow-down'
      })
    })

    it('should return default color and icon for unknown priority', () => {
      const result = getCasePriorityColorIcon('unknown')
      expect(result).toEqual({
        color: 'gray',
        icon: 'i-heroicons-arrow-right'
      })
    })
  })

  describe('getHasAlertWork', () => {
    it('should return red text class when hasAlertWork is true', () => {
      expect(getHasAlertWork(true)).toBe('text-red-500')
    })

    it('should return empty string when hasAlertWork is false', () => {
      expect(getHasAlertWork(false)).toBe('')
    })
  })

  describe('getConsultantRoleColor', () => {
    it('should return correct colors for consultant roles', () => {
      expect(getConsultantRoleColor(CounselorRole.ADMIN)).toBe('red')
      expect(getConsultantRoleColor(CounselorRole.SUPERVISOR)).toBe('primary')
      expect(getConsultantRoleColor(CounselorRole.GENERAL)).toBe('blue')
    })

    it('should return gray for unknown role', () => {
      expect(getConsultantRoleColor('unknown')).toBe('gray')
    })
  })

  describe('getCounselorRoleColor', () => {
    it('should return correct colors for counselor account roles', () => {
      expect(getCounselorRoleColor(CounselorAccountRole.ADMIN)).toBe('primary')
      expect(getCounselorRoleColor(CounselorAccountRole.COUNSELOR)).toBe('green')
    })

    it('should return gray for unknown role', () => {
      expect(getCounselorRoleColor('unknown' as CounselorAccountRole)).toBe('gray')
    })
  })

  describe('getSampleMessageTypeColor', () => {
    it('should return orange for common type', () => {
      expect(getSampleMessageTypeColor('common')).toBe('orange')
    })

    it('should return primary for personal type', () => {
      expect(getSampleMessageTypeColor('personal')).toBe('primary')
    })

    it('should return gray for unknown type', () => {
      expect(getSampleMessageTypeColor('unknown')).toBe('gray')
    })
  })

  describe('getSegmentDeliveryStatusColor', () => {
    it('should return correct colors for segment statuses', () => {
      expect(getSegmentDeliveryStatusColor(SegmentStatus.BEFORE_START)).toBe('gray')
      expect(getSegmentDeliveryStatusColor(SegmentStatus.IN_PROGRESS)).toBe('primary')
      expect(getSegmentDeliveryStatusColor(SegmentStatus.RESOLVED)).toBe('green')
      expect(getSegmentDeliveryStatusColor(SegmentStatus.ERROR)).toBe('red')
    })

    it('should return gray for unknown status', () => {
      expect(getSegmentDeliveryStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getDayColor', () => {
    it('should return red for Sunday (0)', () => {
      expect(getDayColor(0)).toBe('red')
    })

    it('should return blue for Saturday (6)', () => {
      expect(getDayColor(6)).toBe('blue')
    })

    it('should return gray for weekdays', () => {
      expect(getDayColor(1)).toBe('gray')
      expect(getDayColor(2)).toBe('gray')
      expect(getDayColor(3)).toBe('gray')
      expect(getDayColor(4)).toBe('gray')
      expect(getDayColor(5)).toBe('gray')
    })
  })

  describe('getSurveyStatusColor', () => {
    it('should return correct colors for survey statuses', () => {
      expect(getSurveyStatusColor('draft')).toBe('gray')
      expect(getSurveyStatusColor('active')).toBe('green')
      expect(getSurveyStatusColor('inactive')).toBe('orange')
    })

    it('should return gray for unknown status', () => {
      expect(getSurveyStatusColor('unknown')).toBe('gray')
    })
  })

  describe('getColor', () => {
    it('should return correct hex color for known color names', () => {
      expect(getColor('red')).toBe('#f44336')
      expect(getColor('blue')).toBe('#2196f3')
      expect(getColor('green')).toBe('#4caf50')
      expect(getColor('white')).toBe('#ffffff')
      expect(getColor('black')).toBe('#000000')
    })

    it('should return undefined for unknown color names', () => {
      expect(getColor('unknown-color')).toBeUndefined()
    })

    it('should handle color variants', () => {
      expect(getColor('red-500')).toBe('#f44336')
      expect(getColor('blue-100')).toBe('#bbdefb')
      expect(getColor('green-accent')).toBeUndefined()
    })
  })
})
