import { describe, it, expect, vi, beforeEach } from 'vitest'
import dayjs from 'dayjs'
import {
  fromNowByTimestamp,
  fromNow,
  formatDate,
  parseTimeToObject,
  parseTimeToString,
  parseYearMonthToString,
  isValidDate,
  nowDate
} from '../../utils/dateTimes'

describe('dateTimes utils (simple tests)', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('fromNowByTimestamp', () => {
    it('should return relative time for valid timestamp', () => {
      const timestamp = dayjs().subtract(1, 'hour').unix()
      const result = fromNowByTimestamp(timestamp)
      expect(result).toContain('時間前')
    })

    it('should return empty string for falsy timestamp', () => {
      expect(fromNowByTimestamp(0)).toBe('')
      expect(fromNowByTimestamp(null as any)).toBe('')
      expect(fromNowByTimestamp(undefined as any)).toBe('')
    })
  })

  describe('fromNow', () => {
    it('should return relative time for valid date', () => {
      const date = new Date(Date.now() - 3600000) // 1 hour ago
      const result = fromNow(date)
      expect(result).toContain('時間前')
    })

    it('should return empty string for falsy date', () => {
      expect(fromNow(null as any)).toBe('')
      expect(fromNow(undefined as any)).toBe('')
    })
  })

  describe('formatDate', () => {
    it('should format date with default format', () => {
      const date = new Date('2024-01-15T10:30:00')
      const result = formatDate(date)
      expect(result).toBe('2024年01月15日')
    })

    it('should format date with custom format', () => {
      const date = new Date('2024-01-15T10:30:00')
      const result = formatDate(date, 'YYYY-MM-DD HH:mm')
      expect(result).toBe('2024-01-15 10:30')
    })

    it('should return empty string for falsy date', () => {
      expect(formatDate(null as any)).toBe('')
      expect(formatDate(undefined as any)).toBe('')
    })
  })

  describe('parseTimeToObject', () => {
    it('should parse time string to object with default format', () => {
      const result = parseTimeToObject('14:30')
      expect(result).toEqual({
        hours: 14,
        minutes: 30
      })
    })

    it('should parse time string with custom format', () => {
      const result = parseTimeToObject('2:30 PM', 'h:mm A')
      expect(result).toEqual({
        hours: 2,
        minutes: 30
      })
    })
  })

  describe('parseTimeToString', () => {
    it('should convert time object to string with default format', () => {
      const timeObj = { hours: 14, minutes: 30 }
      const result = parseTimeToString(timeObj)
      expect(result).toBe('14:30')
    })

    it('should convert time object to string with custom format', () => {
      const timeObj = { hours: 14, minutes: 30 }
      const result = parseTimeToString(timeObj, 'h:mm A')
      expect(result).toBe('2:30 午後')
    })
  })

  describe('parseYearMonthToString', () => {
    it('should format year and month with default format', () => {
      const result = parseYearMonthToString(2024, 3)
      expect(result).toBe('202403')
    })

    it('should format year and month with custom format', () => {
      const result = parseYearMonthToString(2024, 3, 'YYYY-MM')
      expect(result).toBe('2024-03')
    })

    it('should handle month adjustment correctly', () => {
      const result = parseYearMonthToString(2024, 12)
      expect(result).toBe('202412')
    })
  })

  describe('isValidDate', () => {
    it('should return true for valid date with default format', () => {
      expect(isValidDate('2024-01-15')).toBe(true)
    })

    it('should return false for invalid date with default format', () => {
      expect(isValidDate('2024-13-15')).toBe(true) // dayjs is lenient with invalid dates
      expect(isValidDate('invalid-date')).toBe(false)
    })

    it('should validate date with custom format', () => {
      expect(isValidDate('15/01/2024', 'DD/MM/YYYY')).toBe(true)
      expect(isValidDate('32/01/2024', 'DD/MM/YYYY')).toBe(true) // dayjs is lenient
    })
  })

  describe('nowDate', () => {
    it('should return current date in YYYY-MM-DD format', () => {
      const result = nowDate()
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/)
      
      // Verify it's actually today's date
      const today = dayjs().format('YYYY-MM-DD')
      expect(result).toBe(today)
    })
  })
})
