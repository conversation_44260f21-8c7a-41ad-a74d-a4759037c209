import { describe, it, expect, vi, beforeEach } from 'vitest'
import { CaseStatus } from '~/types/enums.d.ts'
import type { User } from '~/types'
import {
  getNextCaseStatusList,
  getNextCaseStatusOptions,
  needAddAssignee,
  canUpdateCase
} from '~/utils/caseStatus'

// Mock useI18n
const mockT = vi.fn((key: string) => key)
vi.mock('#app', () => ({
  useI18n: () => ({ t: mockT })
}))

// Mock getCaseStatusColor
vi.mock('~/utils/colors', () => ({
  getCaseStatusColor: vi.fn((status: string) => 'mocked-color')
}))

describe('caseStatus utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getNextCaseStatusList', () => {
    it('should return correct next status list for BEFORE_START', () => {
      const result = getNextCaseStatusList(CaseStatus.BEFORE_START)
      expect(result).toEqual([
        CaseStatus.OPEN,
        CaseStatus.IN_PROGRESS,
        CaseStatus.RESOLVED,
        CaseStatus.CANCELLED
      ])
    })

    it('should return correct next status list for OPEN', () => {
      const result = getNextCaseStatusList(CaseStatus.OPEN)
      expect(result).toEqual([
        CaseStatus.IN_PROGRESS,
        CaseStatus.RESOLVED,
        CaseStatus.CANCELLED
      ])
    })

    it('should return correct next status list for IN_PROGRESS', () => {
      const result = getNextCaseStatusList(CaseStatus.IN_PROGRESS)
      expect(result).toEqual([
        CaseStatus.OPEN,
        CaseStatus.WAITING,
        CaseStatus.RESOLVED
      ])
    })

    it('should return correct next status list for WAITING', () => {
      const result = getNextCaseStatusList(CaseStatus.WAITING)
      expect(result).toEqual([CaseStatus.IN_PROGRESS, CaseStatus.RESOLVED])
    })

    it('should return correct next status list for RESOLVED', () => {
      const result = getNextCaseStatusList(CaseStatus.RESOLVED)
      expect(result).toEqual([CaseStatus.IN_PROGRESS])
    })

    it('should return correct next status list for CANCELLED', () => {
      const result = getNextCaseStatusList(CaseStatus.CANCELLED)
      expect(result).toEqual([CaseStatus.OPEN])
    })

    it('should return empty array for unknown status', () => {
      const result = getNextCaseStatusList('UNKNOWN_STATUS' as CaseStatus)
      expect(result).toEqual([])
    })
  })

  describe('needAddAssignee', () => {
    it('should return true when transitioning from BEFORE_START to IN_PROGRESS', () => {
      const result = needAddAssignee(CaseStatus.BEFORE_START, CaseStatus.IN_PROGRESS)
      expect(result).toBe(true)
    })

    it('should return true when transitioning from BEFORE_START to RESOLVED', () => {
      const result = needAddAssignee(CaseStatus.BEFORE_START, CaseStatus.RESOLVED)
      expect(result).toBe(true)
    })

    it('should return false when transitioning from BEFORE_START to OPEN', () => {
      const result = needAddAssignee(CaseStatus.BEFORE_START, CaseStatus.OPEN)
      expect(result).toBe(false)
    })

    it('should return true when transitioning from OPEN to IN_PROGRESS', () => {
      const result = needAddAssignee(CaseStatus.OPEN, CaseStatus.IN_PROGRESS)
      expect(result).toBe(true)
    })

    it('should return true when transitioning from OPEN to RESOLVED', () => {
      const result = needAddAssignee(CaseStatus.OPEN, CaseStatus.RESOLVED)
      expect(result).toBe(true)
    })

    it('should return false when transitioning from OPEN to CANCELLED', () => {
      const result = needAddAssignee(CaseStatus.OPEN, CaseStatus.CANCELLED)
      expect(result).toBe(false)
    })

    it('should return false for all transitions from IN_PROGRESS', () => {
      expect(needAddAssignee(CaseStatus.IN_PROGRESS, CaseStatus.OPEN)).toBe(false)
      expect(needAddAssignee(CaseStatus.IN_PROGRESS, CaseStatus.WAITING)).toBe(false)
      expect(needAddAssignee(CaseStatus.IN_PROGRESS, CaseStatus.RESOLVED)).toBe(false)
    })

    it('should return false for all transitions from WAITING', () => {
      expect(needAddAssignee(CaseStatus.WAITING, CaseStatus.IN_PROGRESS)).toBe(false)
      expect(needAddAssignee(CaseStatus.WAITING, CaseStatus.RESOLVED)).toBe(false)
    })

    it('should return false for all transitions from RESOLVED', () => {
      expect(needAddAssignee(CaseStatus.RESOLVED, CaseStatus.IN_PROGRESS)).toBe(false)
    })

    it('should return false for all transitions from CANCELLED', () => {
      expect(needAddAssignee(CaseStatus.CANCELLED, CaseStatus.OPEN)).toBe(false)
    })

    it('should return false for unknown status', () => {
      const result = needAddAssignee('UNKNOWN' as CaseStatus, CaseStatus.OPEN)
      expect(result).toBe(false)
    })
  })

  describe('canUpdateCase', () => {
    const mockUser: User = {
      counselorId: 'counselor-123'
    } as User

    const mockPermissions = [
      'update:case-attributes-of-open-processing-cases-i-am-in-charge',
      'update:case-attributes-of-open-processing-cases-i-am-not-in-charge',
      'update:case-attributes-of-resolved-cases'
    ]

    it('should return false for OPEN status', () => {
      const result = canUpdateCase(
        CaseStatus.OPEN,
        mockUser,
        'counselor-123',
        mockPermissions
      )
      expect(result).toBe(false)
    })

    it('should return true for IN_PROGRESS when user is in charge and has permission', () => {
      const result = canUpdateCase(
        CaseStatus.IN_PROGRESS,
        mockUser,
        'counselor-123',
        ['update:case-attributes-of-open-processing-cases-i-am-in-charge']
      )
      expect(result).toBe(true)
    })

    it('should return false for IN_PROGRESS when user is in charge but lacks permission', () => {
      const result = canUpdateCase(
        CaseStatus.IN_PROGRESS,
        mockUser,
        'counselor-123',
        []
      )
      expect(result).toBe(false)
    })

    it('should return false for IN_PROGRESS when user is not in charge and lacks permission', () => {
      const result = canUpdateCase(
        CaseStatus.IN_PROGRESS,
        mockUser,
        'different-counselor',
        ['update:case-attributes-of-open-processing-cases-i-am-in-charge']
      )
      expect(result).toBe(false)
    })

    it('should return true when user has permission for cases not in charge', () => {
      const result = canUpdateCase(
        CaseStatus.IN_PROGRESS,
        mockUser,
        'different-counselor',
        ['update:case-attributes-of-open-processing-cases-i-am-not-in-charge']
      )
      expect(result).toBe(true)
    })

    it('should return true for RESOLVED status with appropriate permission', () => {
      const result = canUpdateCase(
        CaseStatus.RESOLVED,
        mockUser,
        'counselor-123',
        ['update:case-attributes-of-resolved-cases']
      )
      expect(result).toBe(true)
    })

    it('should return false for RESOLVED status without appropriate permission', () => {
      const result = canUpdateCase(
        CaseStatus.RESOLVED,
        mockUser,
        'counselor-123',
        []
      )
      expect(result).toBe(false)
    })

    it('should handle undefined parameters gracefully', () => {
      const result = canUpdateCase(CaseStatus.IN_PROGRESS)
      expect(result).toBeOneOf([false, undefined])
    })
  })
})
