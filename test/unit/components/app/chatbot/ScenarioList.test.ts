import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import AppScenarioList from '~/components/app/chatbot/AppScenarioList.vue'
import type { Scenario } from '~/types'

// Mock Nuxt UI components
vi.mock('#ui', () => ({
  UButton: {
    template: '<button><slot /></button>',
    props: ['icon', 'label', 'color', 'variant', 'size', 'loading', 'disabled']
  },
  UBadge: {
    template: '<span><slot /></span>',
    props: ['color', 'variant']
  },
  UIcon: {
    template: '<i></i>',
    props: ['name']
  },
  USpinner: {
    template: '<div class="spinner"></div>',
    props: ['size']
  },
  UDropdown: {
    template: '<div><slot /></div>',
    props: ['items']
  },
  UPagination: {
    template: '<div class="pagination"></div>',
    props: ['modelValue', 'pageCount', 'total']
  },
  UModal: {
    template: '<div v-if="modelValue"><slot /></div>',
    props: ['modelValue']
  },
  UCard: {
    template: '<div class="card"><header><slot name="header" /></header><slot /><footer><slot name="footer" /></footer></div>'
  }
}))

const createMockScenario = (id: string, name: string, isActive = true): Scenario => ({
  id,
  name,
  description: `Description for ${name}`,
  isActive,
  customerId: 'test-customer',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-02T00:00:00Z',
  questions: [
    {
      id: `q1-${id}`,
      text: 'Sample question',
      choices: [
        {
          id: `c1-${id}`,
          text: 'Choice 1',
          responseMessage: 'Thank you',
          action: {
            type: 'end_scenario'
          }
        }
      ],
      isFirstQuestion: true,
      order: 1
    }
  ]
})

describe('AppScenarioList', () => {
  let wrapper: any

  const mockScenarios: Scenario[] = [
    createMockScenario('1', 'Test Scenario 1'),
    createMockScenario('2', 'Test Scenario 2', false),
    createMockScenario('3', 'Test Scenario 3')
  ]

  beforeEach(() => {
    wrapper = mount(AppScenarioList, {
      global: {
        stubs: {
          UButton: true,
          UBadge: true,
          UIcon: true,
          USpinner: true,
          UDropdown: true,
          UPagination: true,
          UModal: true,
          UCard: true
        }
      },
      props: {
        scenarios: mockScenarios,
        loading: false,
        totalCount: 3,
        currentPage: 1,
        pageSize: 10
      }
    })
  })

  it('renders scenario list correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('h1').text()).toBe('シナリオ管理')
  })

  it('displays scenarios correctly', () => {
    const scenarioCards = wrapper.findAll('.bg-white')
    expect(scenarioCards.length).toBe(3)
  })

  it('shows active/inactive badges correctly', () => {
    // This would need proper component mocking to test badge colors
    expect(wrapper.text()).toContain('Test Scenario 1')
    expect(wrapper.text()).toContain('Test Scenario 2')
  })

  it('emits create-scenario event when create button is clicked', async () => {
    const createButton = wrapper.find('[data-testid="create-scenario-btn"]')
    if (createButton.exists()) {
      await createButton.trigger('click')
      expect(wrapper.emitted('create-scenario')).toBeTruthy()
    }
  })

  it('displays loading spinner when loading', async () => {
    await wrapper.setProps({ loading: true })
    expect(wrapper.find('.spinner').exists()).toBe(true)
  })

  it('displays empty state when no scenarios', async () => {
    await wrapper.setProps({ scenarios: [] })
    expect(wrapper.text()).toContain('シナリオがありません')
  })

  it('displays question count correctly', () => {
    expect(wrapper.text()).toContain('1個の設問')
  })

  it('formats dates correctly', () => {
    // Test date formatting logic
    const component = wrapper.vm
    const formattedDate = component.formatDate('2023-01-02T00:00:00Z')
    expect(formattedDate).toMatch(/2023/)
  })

  it('shows pagination when totalCount > pageSize', async () => {
    await wrapper.setProps({ totalCount: 15 })
    expect(wrapper.find('.pagination').exists()).toBe(true)
  })

  it('handles delete confirmation flow', async () => {
    const component = wrapper.vm
    const scenario = mockScenarios[0]
    
    // Initiate delete
    component.initiateDelete(scenario)
    expect(component.showDeleteModal).toBe(true)
    expect(component.scenarioToDelete).toBe(scenario)
    
    // Cancel delete
    component.cancelDelete()
    expect(component.showDeleteModal).toBe(false)
    expect(component.scenarioToDelete).toBe(null)
  })

  it('emits correct events for scenario actions', async () => {
    const component = wrapper.vm
    const scenario = mockScenarios[0]
    
    // Test edit event
    component.$emit('edit-scenario', scenario)
    expect(wrapper.emitted('edit-scenario')).toBeTruthy()
    expect(wrapper.emitted('edit-scenario')[0]).toEqual([scenario])
    
    // Test view event
    component.$emit('view-scenario', scenario)
    expect(wrapper.emitted('view-scenario')).toBeTruthy()
    
    // Test toggle event
    component.$emit('toggle-scenario', scenario)
    expect(wrapper.emitted('toggle-scenario')).toBeTruthy()
  })

  it('generates correct dropdown items', () => {
    const component = wrapper.vm
    const activeScenario = mockScenarios[0]
    const inactiveScenario = mockScenarios[1]
    
    const activeItems = component.getDropdownItems(activeScenario)
    expect(activeItems[0][0].label).toBe('無効化')
    
    const inactiveItems = component.getDropdownItems(inactiveScenario)
    expect(inactiveItems[0][0].label).toBe('有効化')
  })
})