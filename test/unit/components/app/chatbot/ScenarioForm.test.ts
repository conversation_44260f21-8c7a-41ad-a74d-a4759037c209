import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import AppScenarioForm from '~/components/app/chatbot/AppScenarioForm.vue'
import type { Scenario, EndTemplate } from '~/types'
import { SCENARIO_VALIDATION } from '~/utils/scenarioConstants'

const mockEndTemplates: EndTemplate[] = [
  {
    id: '1',
    templateName: 'Template 1',
    finalQuestion: 'Final question',
    choices: [],
    isActive: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }
]

describe('AppScenarioForm', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(AppScenarioForm, {
      global: {
        stubs: {
          UButton: true,
          UInput: true,
          UTextarea: true,
          UToggle: true,
          USelect: true,
          UCard: true,
          UFormGroup: true,
          UIcon: true
        }
      },
      props: {
        endTemplates: mockEndTemplates,
        saving: false,
        loadingEndTemplates: false
      }
    })
  })

  it('renders form correctly for new scenario', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('シナリオ新規作成')
  })

  it('renders form correctly for editing scenario', async () => {
    const scenario: Scenario = {
      id: '1',
      name: 'Test Scenario',
      description: 'Test description',
      isActive: true,
      questions: [],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }

    await wrapper.setProps({ scenario })
    expect(wrapper.text()).toContain('シナリオ編集')
  })

  it('validates scenario name correctly', async () => {
    const component = wrapper.vm
    
    // Test empty name
    component.form.name = ''
    component.validateField('name')
    expect(component.formErrors.name).toBe('シナリオ名は必須です')
    
    // Test name too long
    component.form.name = 'a'.repeat(SCENARIO_VALIDATION.NAME_MAX_LENGTH + 1)
    component.validateField('name')
    expect(component.formErrors.name).toContain('文字以内で入力してください')
    
    // Test valid name
    component.form.name = 'Valid Name'
    component.validateField('name')
    expect(component.formErrors.name).toBeUndefined()
  })

  it('validates question text correctly', async () => {
    const component = wrapper.vm
    component.form.questions = [{
      id: '1',
      text: '',
      choices: [],
      isFirstQuestion: true,
      order: 1
    }]
    
    // Test empty question text
    component.validateQuestion(0, 'text')
    expect(component.formErrors.questions[0].text).toBe('設問内容は必須です')
    
    // Test question text too long
    component.form.questions[0].text = 'a'.repeat(SCENARIO_VALIDATION.QUESTION_MAX_LENGTH + 1)
    component.validateQuestion(0, 'text')
    expect(component.formErrors.questions[0].text).toContain('文字以内で入力してください')
  })

  it('validates choice text correctly', async () => {
    const component = wrapper.vm
    component.form.questions = [{
      id: '1',
      text: 'Test question',
      choices: [{
        id: '1',
        text: '',
        action: { type: 'end_scenario' }
      }],
      isFirstQuestion: true,
      order: 1
    }]
    
    // Test empty choice text
    component.validateChoice(0, 0, 'text')
    expect(component.formErrors.questions[0].choices[0].text).toBe('選択肢テキストは必須です')
    
    // Test choice text too long
    component.form.questions[0].choices[0].text = 'a'.repeat(SCENARIO_VALIDATION.CHOICE_MAX_LENGTH + 1)
    component.validateChoice(0, 0, 'text')
    expect(component.formErrors.questions[0].choices[0].text).toContain('文字以内で入力してください')
  })

  it('adds questions correctly', async () => {
    const component = wrapper.vm
    const initialLength = component.form.questions.length
    
    component.addQuestion()
    expect(component.form.questions.length).toBe(initialLength + 1)
    
    const newQuestion = component.form.questions[component.form.questions.length - 1]
    expect(newQuestion.text).toBe('')
    expect(newQuestion.choices).toEqual([])
    expect(newQuestion.order).toBe(component.form.questions.length)
  })

  it('removes questions correctly', async () => {
    const component = wrapper.vm
    component.form.questions = [
      {
        id: '1',
        text: 'Question 1',
        choices: [],
        isFirstQuestion: true,
        order: 1
      },
      {
        id: '2', 
        text: 'Question 2',
        choices: [],
        isFirstQuestion: false,
        order: 2
      }
    ]
    
    component.removeQuestion(0)
    expect(component.form.questions.length).toBe(1)
    expect(component.form.questions[0].text).toBe('Question 2')
    expect(component.form.questions[0].isFirstQuestion).toBe(true) // Should become first
  })

  it('adds choices correctly', async () => {
    const component = wrapper.vm
    component.form.questions = [{
      id: '1',
      text: 'Test question',
      choices: [],
      isFirstQuestion: true,
      order: 1
    }]
    
    component.addChoice(0)
    expect(component.form.questions[0].choices.length).toBe(1)
    
    const newChoice = component.form.questions[0].choices[0]
    expect(newChoice.text).toBe('')
    expect(newChoice.action.type).toBe('end_scenario')
  })

  it('enforces maximum choices per question', async () => {
    const component = wrapper.vm
    component.form.questions = [{
      id: '1',
      text: 'Test question',
      choices: new Array(SCENARIO_VALIDATION.MAX_CHOICES_PER_QUESTION).fill(null).map((_, i) => ({
        id: `${i}`,
        text: `Choice ${i}`,
        action: { type: 'end_scenario' }
      })),
      isFirstQuestion: true,
      order: 1
    }]
    
    const initialLength = component.form.questions[0].choices.length
    component.addChoice(0)
    expect(component.form.questions[0].choices.length).toBe(initialLength) // Should not add more
  })

  it('sets first question correctly', async () => {
    const component = wrapper.vm
    component.form.questions = [
      {
        id: '1',
        text: 'Question 1',
        choices: [],
        isFirstQuestion: true,
        order: 1
      },
      {
        id: '2',
        text: 'Question 2', 
        choices: [],
        isFirstQuestion: false,
        order: 2
      }
    ]
    
    component.setAsFirstQuestion(1)
    expect(component.form.questions[0].isFirstQuestion).toBe(false)
    expect(component.form.questions[1].isFirstQuestion).toBe(true)
  })

  it('computes canSave correctly', async () => {
    const component = wrapper.vm
    
    // Invalid form - no name
    component.form.name = ''
    expect(component.canSave).toBe(false)
    
    // Invalid form - no questions
    component.form.name = 'Test'
    component.form.questions = []
    expect(component.canSave).toBe(false)
    
    // Invalid form - no first question
    component.form.questions = [{
      id: '1',
      text: 'Test',
      choices: [{ id: '1', text: 'Choice', action: { type: 'end_scenario' } }],
      isFirstQuestion: false,
      order: 1
    }]
    expect(component.canSave).toBe(false)
    
    // Valid form
    component.form.questions[0].isFirstQuestion = true
    await component.$nextTick()
    expect(component.canSave).toBe(true)
  })

  it('emits save event with correct data', async () => {
    const component = wrapper.vm
    component.form = {
      id: '',
      name: 'Test Scenario',
      description: 'Test description',
      isActive: true,
      questions: [{
        id: '1',
        text: 'Test question',
        choices: [{
          id: '1',
          text: 'Test choice',
          action: { type: 'end_scenario' }
        }],
        isFirstQuestion: true,
        order: 1
      }]
    }
    
    component.handleSave()
    
    expect(wrapper.emitted('save')).toBeTruthy()
    expect(wrapper.emitted('save')[0][0]).toMatchObject({
      name: 'Test Scenario',
      description: 'Test description',
      isActive: true
    })
  })
})