import { vi } from 'vitest'

// Global test setup for utils tests

// Mock Nuxt composables that are commonly used across utils
export const setupGlobalMocks = () => {
  // Mock useI18n
  vi.mock('#app', () => ({
    useI18n: () => ({
      t: vi.fn((key: string) => key)
    }),
    useRuntimeConfig: () => ({
      public: {
        hamamatsuChatAvailable: {
          days: [1, 2, 3, 4, 5],
          times: [9, 10, 11, 12, 13, 14, 15, 16, 17],
          dates: [
            ['2024-01-01', '2024-01-31'],
            ['2024-06-01', '2024-06-30']
          ],
          hardAvailable: false
        }
      }
    }),
    useRouter: () => ({
      resolve: vi.fn((route) => ({
        href: `/survey?sid=${route.query.sid}&c=${route.query.c}&u=${route.query.u || ''}`
      }))
    }),
    useConstants: () => ({
      ageOptions: [
        { value: 'TEENS', label: '10代' },
        { value: 'TWENTIES', label: '20代' },
        { value: 'THIRTIES', label: '30代' },
        { value: 'FORTIES', label: '40代' },
        { value: 'FIFTIES', label: '50代' },
        { value: 'SIXTIES_AND_ABOVE', label: '60歳以上' }
      ],
      SCHOOL_LIST: [
        { schoolId: 'school-1', schoolName: '東京大学' },
        { schoolId: 'school-2', schoolName: '京都大学' },
        { schoolId: 'school-3', schoolName: '大阪大学' }
      ]
    })
  }))

  // Mock SVG imports
  vi.mock('~/assets/images/facebook.svg', () => ({
    default: 'FacebookSvg'
  }))

  vi.mock('~/assets/images/line.svg', () => ({
    default: 'LineSvg'
  }))

  vi.mock('~/assets/images/application.svg', () => ({
    default: 'ApplicationSvg'
  }))

  // Mock external libraries
  vi.mock('@faker-js/faker/locale/ja', () => ({
    faker: {
      person: {
        fullName: vi.fn(() => 'テスト太郎')
      },
      string: {
        numeric: vi.fn(() => '12345'),
        uuid: vi.fn(() => 'test-uuid-123')
      },
      image: {
        avatar: vi.fn(() => 'https://example.com/avatar.jpg')
      },
      lorem: {
        sentence: vi.fn(() => 'これはテストの文章です。')
      },
      datatype: {
        boolean: vi.fn(() => true)
      },
      internet: {
        email: vi.fn(() => '<EMAIL>')
      },
      company: {
        name: vi.fn(() => 'テスト会社')
      }
    }
  }))

  vi.mock('lodash/random', () => ({
    default: vi.fn((from: number, to: number) => Math.floor((from + to) / 2))
  }))
}

// Helper functions for common test scenarios
export const createMockUser = (overrides = {}) => ({
  counselorId: 'test-counselor-123',
  name: 'テストユーザー',
  email: '<EMAIL>',
  ...overrides
})

export const createMockPermissions = (permissions: string[] = []) => [
  'update:case-attributes-of-open-processing-cases-i-am-in-charge',
  'update:case-attributes-of-open-processing-cases-i-am-not-in-charge',
  'update:case-attributes-of-resolved-cases',
  ...permissions
]

export const createMockSurveyFormElement = (overrides = {}) => ({
  _id: 'test-field',
  type: 'text',
  required: false,
  label: 'Test Field',
  ...overrides
})

// Test data constants
export const TEST_CONSTANTS = {
  VALID_DATES: {
    TODAY: new Date(),
    YESTERDAY: new Date(Date.now() - 86400000),
    LAST_WEEK: new Date(Date.now() - 604800000),
    LAST_MONTH: new Date(Date.now() - 2592000000)
  },
  VALID_TIMESTAMPS: {
    NOW: Math.floor(Date.now() / 1000),
    HOUR_AGO: Math.floor((Date.now() - 3600000) / 1000),
    DAY_AGO: Math.floor((Date.now() - 86400000) / 1000)
  },
  VALID_URLS: [
    'http://example.com',
    'https://example.com',
    'https://www.example.com/path',
    'http://subdomain.example.com'
  ],
  INVALID_URLS: [
    'example.com',
    'ftp://example.com',
    'not a url',
    '',
    'http://',
    'https://'
  ]
}

// Setup function to be called in test files
export const setupUtilsTest = () => {
  setupGlobalMocks()
  
  // Reset all mocks before each test
  beforeEach(() => {
    vi.clearAllMocks()
  })
}

// Export for use in individual test files
export { vi }
