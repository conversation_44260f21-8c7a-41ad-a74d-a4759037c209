import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'

import FormTextAndPulldown from '~/components/form/FormTextAndPulldown.vue'

describe('FormTextAndPulldown', () => {
  const defaultProps = {
    elementId: 'test-text-pulldown',
    label: 'Test Label',
    options: ['Option 1', 'Option 2', 'Option 3'],
    modelValue: '',
  }

  it('should render correctly with basic props', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.find('.flex.flex-col.space-y-1').exists()).toBe(true)
    expect(wrapper.find('.text-sm').text()).toBe('Test Label')
  })

  it('should render default label when no label provided', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        label: undefined,
      },
    })
    
    expect(wrapper.text()).toContain('<ラベル>')
  })

  it('should emit update:model-value when selection changes', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    // Test with string value (direct selection)
    await wrapper.vm.$emit('update:model-value', 'test-text-pulldown', 'Option 1')
    
    expect(wrapper.emitted('update:model-value')).toBeTruthy()
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-text-pulldown', 'Option 1'])
  })

  it('should emit update:model-value with object label when creating new option', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    // Test with object value (new option creation)
    const newOption = { label: 'New Custom Option' }
    await wrapper.vm.$emit('update:model-value', 'test-text-pulldown', newOption.label)
    
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-text-pulldown', 'New Custom Option'])
  })

  it('should pass correct props to USelectMenu', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        disabled: true,
        modelValue: 'Test Value',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Option 1', 'Option 2', 'Option 3'])
    expect(wrapper.props('disabled')).toBe(true)
    expect(wrapper.props('modelValue')).toBe('Test Value')
  })

  it('should handle empty options array', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: [],
      },
    })
    
    expect(wrapper.props('options')).toEqual([])
    expect(wrapper.text()).toContain('Test Label')
  })

  it('should handle disabled state', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        disabled: true,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle different elementId values', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        elementId: 'custom-text-pulldown-id',
      },
    })
    
    expect(wrapper.props('elementId')).toBe('custom-text-pulldown-id')
  })

  it('should handle modelValue updates', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ modelValue: 'Updated Value' })
    expect(wrapper.props('modelValue')).toBe('Updated Value')
  })

  it('should handle options updates', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    const newOptions = ['New Option 1', 'New Option 2']
    await wrapper.setProps({ options: newOptions })
    expect(wrapper.props('options')).toEqual(newOptions)
  })

  it('should handle label updates', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ label: 'Updated Label' })
    expect(wrapper.find('.text-sm').text()).toBe('Updated Label')
  })

  it('should work with Japanese options and labels', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['選択肢1', '選択肢2', '選択肢3'],
        label: '日本語ラベル',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['選択肢1', '選択肢2', '選択肢3'])
    expect(wrapper.find('.text-sm').text()).toBe('日本語ラベル')
  })

  it('should handle undefined/null modelValue', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        modelValue: undefined as any,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('')
  })

  it('should emit with correct elementId when changed', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        elementId: 'special-id',
      },
    })
    
    await wrapper.vm.$emit('update:model-value', 'special-id', 'Selected Value')
    
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['special-id', 'Selected Value'])
  })

  it('should maintain component structure', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    const container = wrapper.find('.flex.flex-col.space-y-1')
    expect(container.exists()).toBe(true)
    
    const label = container.find('.text-sm')
    expect(label.exists()).toBe(true)
    expect(label.text()).toBe('Test Label')
  })

  it('should handle boolean disabled prop correctly', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        disabled: false,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(false)
    
    await wrapper.setProps({ disabled: true })
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should support searchable functionality', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    // Component should be searchable by default with Japanese placeholder
    expect(wrapper.html()).not.toBeUndefined()
  })

  it('should handle complex option values', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['Option with spaces', 'Option-with-dashes', 'Option_with_underscores'],
      },
    })
    
    expect(wrapper.props('options')).toEqual([
      'Option with spaces',
      'Option-with-dashes',
      'Option_with_underscores'
    ])
  })

  it('should handle long option text', () => {
    const longOption = 'This is a very long option text that should be handled properly by the component'
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: [longOption, 'Short option'],
      },
    })
    
    expect(wrapper.props('options')).toContain(longOption)
  })

  it('should handle special characters in options', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['Option!@#', 'Option$%^', 'Option&*()'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Option!@#', 'Option$%^', 'Option&*()'])
  })

  it('should handle numeric string options', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['1', '10', '100', '1000'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['1', '10', '100', '1000'])
  })

  it('should handle single option', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['Single Option'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Single Option'])
  })

  it('should handle emoji in options', () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: {
        ...defaultProps,
        options: ['📝 Text', '📋 List', '🔍 Search'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['📝 Text', '📋 List', '🔍 Search'])
  })

  it('should handle multiple emit calls with different values', async () => {
    const wrapper = mount(FormTextAndPulldown, {
      props: defaultProps,
    })
    
    await wrapper.vm.$emit('update:model-value', 'test-text-pulldown', 'First Value')
    await wrapper.vm.$emit('update:model-value', 'test-text-pulldown', 'Second Value')
    
    expect(wrapper.emitted('update:model-value')).toHaveLength(2)
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-text-pulldown', 'First Value'])
    expect(wrapper.emitted('update:model-value')?.[1]).toEqual(['test-text-pulldown', 'Second Value'])
  })
})