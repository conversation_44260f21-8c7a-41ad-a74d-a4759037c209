import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'

import FormText from '~/components/form/FormText.vue'

describe('FormText', () => {
  const defaultProps = {
    elementId: 'test-text',
    label: 'Test Label',
    modelValue: '',
  }

  it('should render correctly with basic props', () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.find('.flex.flex-col.space-y-1').exists()).toBe(true)
    expect(wrapper.find('.text-sm').text()).toBe('Test Label')
  })

  it('should render default label when no label provided', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        label: undefined,
      },
    })
    
    expect(wrapper.text()).toContain('<ラベル>')
  })

  it('should emit update:model-value when input changes', async () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    // Trigger the emit directly since we can't interact with UInput in this environment
    await wrapper.vm.$emit('update:model-value', 'test-text', 'New Value')
    
    expect(wrapper.emitted('update:model-value')).toBeTruthy()
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-text', 'New Value'])
  })

  it('should pass correct props to UInput', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        disabled: true,
        modelValue: 'Test Value',
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
    expect(wrapper.props('modelValue')).toBe('Test Value')
  })

  it('should handle disabled state', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        disabled: true,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle different elementId values', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        elementId: 'custom-text-id',
      },
    })
    
    expect(wrapper.props('elementId')).toBe('custom-text-id')
  })

  it('should handle modelValue updates', async () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ modelValue: 'Updated Value' })
    expect(wrapper.props('modelValue')).toBe('Updated Value')
  })

  it('should handle label updates', async () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ label: 'Updated Label' })
    expect(wrapper.find('.text-sm').text()).toBe('Updated Label')
  })

  it('should work with Japanese text', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        label: '日本語ラベル',
        modelValue: 'こんにちは世界',
      },
    })
    
    expect(wrapper.find('.text-sm').text()).toBe('日本語ラベル')
    expect(wrapper.props('modelValue')).toBe('こんにちは世界')
  })

  it('should handle empty modelValue', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: '',
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('')
  })

  it('should handle undefined/null modelValue', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: undefined as any,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('')
  })

  it('should emit with correct elementId when changed', async () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        elementId: 'special-text-id',
      },
    })
    
    await wrapper.vm.$emit('update:model-value', 'special-text-id', 'Input Text')
    
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['special-text-id', 'Input Text'])
  })

  it('should maintain component structure', () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    const container = wrapper.find('.flex.flex-col.space-y-1')
    expect(container.exists()).toBe(true)
    
    const label = container.find('.text-sm')
    expect(label.exists()).toBe(true)
    expect(label.text()).toBe('Test Label')
  })

  it('should handle boolean disabled prop correctly', async () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        disabled: false,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(false)
    
    await wrapper.setProps({ disabled: true })
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle long text values', () => {
    const longText = 'This is a very long text value that should be handled properly by the component without any issues'
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: longText,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe(longText)
  })

  it('should handle special characters in text', () => {
    const specialText = '!@#$%^&*()_+-=[]{}|;:,.<>?'
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: specialText,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe(specialText)
  })

  it('should handle numeric string values', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: '12345',
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('12345')
  })

  it('should handle whitespace in text values', () => {
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: '   text with spaces   ',
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('   text with spaces   ')
  })

  it('should handle multiline text representation', () => {
    const multilineText = 'Line 1\nLine 2\nLine 3'
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: multilineText,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe(multilineText)
  })

  it('should handle emoji in text values', () => {
    const emojiText = 'Hello 👋 World 🌍'
    const wrapper = mount(FormText, {
      props: {
        ...defaultProps,
        modelValue: emojiText,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe(emojiText)
  })

  it('should handle multiple emit calls', async () => {
    const wrapper = mount(FormText, {
      props: defaultProps,
    })
    
    await wrapper.vm.$emit('update:model-value', 'test-text', 'First Value')
    await wrapper.vm.$emit('update:model-value', 'test-text', 'Second Value')
    
    expect(wrapper.emitted('update:model-value')).toHaveLength(2)
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-text', 'First Value'])
    expect(wrapper.emitted('update:model-value')?.[1]).toEqual(['test-text', 'Second Value'])
  })
})