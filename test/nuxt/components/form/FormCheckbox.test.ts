import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'

import FormCheckbox from '~/components/form/FormCheckbox.vue'

describe('FormCheckbox', () => {
  const defaultProps = {
    elementId: 'test-checkbox',
    label: 'Test Label',
    options: ['Option 1', 'Option 2', 'Option 3'],
    modelValue: [],
  }

  it('should render correctly with basic props', () => {
    const wrapper = mount(FormCheckbox, {
      props: defaultProps,
    })
    
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.text()).toContain('Option 1')
    expect(wrapper.text()).toContain('Option 2')
    expect(wrapper.text()).toContain('Option 3')
  })

  it('should render default label when no label provided', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        label: undefined,
      },
    })
    
    expect(wrapper.text()).toContain('<ラベル>')
  })

  it('should render correct number of options', () => {
    const wrapper = mount(FormCheckbox, {
      props: defaultProps,
    })
    
    // Check that all options are rendered in the text content
    const text = wrapper.text()
    expect(text).toContain('Option 1')
    expect(text).toContain('Option 2') 
    expect(text).toContain('Option 3')
  })

  it('should render with correct structure', () => {
    const wrapper = mount(FormCheckbox, {
      props: defaultProps,
    })

    // Check the wrapper structure
    expect(wrapper.find('.flex.flex-col.space-y-1').exists()).toBe(true)
    expect(wrapper.find('.text-sm').text()).toBe('Test Label')
  })

  it('should test internal logic by simulating component behavior', async () => {
    const wrapper = mount(FormCheckbox, {
      props: defaultProps,
    })

    // We can't directly call onUpdate, but we can test the expected behavior
    // by examining the component's props and structure
    expect(wrapper.props('modelValue')).toEqual([])
    expect(wrapper.props('options')).toEqual(['Option 1', 'Option 2', 'Option 3'])
  })

  it('should have correct props configuration', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        modelValue: ['Option 1', 'Option 2'],
      },
    })

    expect(wrapper.props('modelValue')).toEqual(['Option 1', 'Option 2'])
    expect(wrapper.props('elementId')).toBe('test-checkbox')
    expect(wrapper.props('disabled')).toBe(false)
  })

  it('should pass disabled prop to component', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        disabled: true,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle empty options array', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        options: [],
      },
    })
    
    // Should only show the label
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.text()).not.toContain('Option')
  })

  it('should handle non-array modelValue gracefully', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        modelValue: null as any,
      },
    })
    
    // Should not crash and should render the options
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.text()).toContain('Option 1')
  })

  it('should handle props update correctly', async () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        modelValue: ['Option 1'],
      },
    })
    
    // Test updating props
    await wrapper.setProps({ modelValue: ['Option 1', 'Option 2'] })
    expect(wrapper.props('modelValue')).toEqual(['Option 1', 'Option 2'])
  })

  it('should handle changing options', async () => {
    const wrapper = mount(FormCheckbox, {
      props: defaultProps,
    })
    
    // Change options
    await wrapper.setProps({ options: ['New Option 1', 'New Option 2'] })
    expect(wrapper.text()).toContain('New Option 1')
    expect(wrapper.text()).toContain('New Option 2')
  })

  it('should handle different elementId values', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        elementId: 'custom-element-id',
      },
    })
    
    expect(wrapper.props('elementId')).toBe('custom-element-id')
  })

  it('should work with complex selection scenarios', async () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        modelValue: ['Option 1'],
      },
    })
    
    // Update to have multiple selections
    await wrapper.setProps({ modelValue: ['Option 1', 'Option 2', 'Option 3'] })
    expect(wrapper.props('modelValue')).toEqual(['Option 1', 'Option 2', 'Option 3'])
    
    // Update to have no selections
    await wrapper.setProps({ modelValue: [] })
    expect(wrapper.props('modelValue')).toEqual([])
  })

  it('should work with different option types', () => {
    const wrapper = mount(FormCheckbox, {
      props: {
        ...defaultProps,
        options: ['選択肢1', '選択肢2', '選択肢3'],
      },
    })
    
    expect(wrapper.text()).toContain('選択肢1')
    expect(wrapper.text()).toContain('選択肢2')
    expect(wrapper.text()).toContain('選択肢3')
  })
})
