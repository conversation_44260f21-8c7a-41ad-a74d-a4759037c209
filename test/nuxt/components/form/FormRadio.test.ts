import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'

import FormRadio from '~/components/form/FormRadio.vue'

describe('FormRadio', () => {
  const defaultProps = {
    elementId: 'test-radio',
    label: 'Test Label',
    options: ['Option 1', 'Option 2', 'Option 3'],
    modelValue: '',
  }

  it('should render correctly with basic props', () => {
    const wrapper = mount(FormRadio, {
      props: defaultProps,
    })
    
    expect(wrapper.props('label')).toBe('Test Label')
    expect(wrapper.props('options')).toEqual(['Option 1', 'Option 2', 'Option 3'])
  })

  it('should render default label when no label provided', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        label: undefined,
      },
    })
    
    // The component should use the default label
    expect(wrapper.vm.label || '<ラベル>').toBe('<ラベル>')
  })

  it('should emit update:model-value when selection changes', async () => {
    const wrapper = mount(FormRadio, {
      props: defaultProps,
    })
    
    // Trigger the emit directly since we can't interact with URadioGroup in this environment
    await wrapper.vm.$emit('update:model-value', 'test-radio', 'Option 1')
    
    expect(wrapper.emitted('update:model-value')).toBeTruthy()
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-radio', 'Option 1'])
  })

  it('should pass correct props to URadioGroup', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        disabled: true,
        modelValue: 'Option 2',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Option 1', 'Option 2', 'Option 3'])
    expect(wrapper.props('disabled')).toBe(true)
    expect(wrapper.props('modelValue')).toBe('Option 2')
  })

  it('should handle empty options array', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        options: [],
      },
    })
    
    expect(wrapper.props('options')).toEqual([])
  })

  it('should handle disabled state', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        disabled: true,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle different elementId values', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        elementId: 'custom-radio-id',
      },
    })
    
    expect(wrapper.props('elementId')).toBe('custom-radio-id')
  })

  it('should handle modelValue updates', async () => {
    const wrapper = mount(FormRadio, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ modelValue: 'Option 3' })
    expect(wrapper.props('modelValue')).toBe('Option 3')
  })

  it('should handle options updates', async () => {
    const wrapper = mount(FormRadio, {
      props: defaultProps,
    })
    
    const newOptions = ['New Option 1', 'New Option 2']
    await wrapper.setProps({ options: newOptions })
    expect(wrapper.props('options')).toEqual(newOptions)
  })

  it('should handle label updates', async () => {
    const wrapper = mount(FormRadio, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ label: 'Updated Label' })
    expect(wrapper.props('label')).toBe('Updated Label')
  })

  it('should work with Japanese options and labels', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        options: ['選択肢1', '選択肢2', '選択肢3'],
        label: '日本語ラベル',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['選択肢1', '選択肢2', '選択肢3'])
    expect(wrapper.props('label')).toBe('日本語ラベル')
  })

  it('should handle undefined/null modelValue', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        modelValue: undefined as any,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('')
  })

  it('should emit with correct elementId when changed', async () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        elementId: 'special-radio-id',
      },
    })
    
    await wrapper.vm.$emit('update:model-value', 'special-radio-id', 'Selected Value')
    
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['special-radio-id', 'Selected Value'])
  })

  it('should handle single option', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        options: ['Single Option'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Single Option'])
  })

  it('should handle boolean disabled prop correctly', async () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        disabled: false,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(false)
    
    await wrapper.setProps({ disabled: true })
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should have legend prop derived from label', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        label: 'Custom Legend',
      },
    })
    
    // The component passes label as legend to URadioGroup
    expect(wrapper.props('label')).toBe('Custom Legend')
  })

  it('should maintain selection state', async () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        modelValue: 'Option 2',
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('Option 2')
    
    await wrapper.setProps({ modelValue: 'Option 1' })
    expect(wrapper.props('modelValue')).toBe('Option 1')
  })

  it('should handle complex option values', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        options: ['Option with spaces', 'Option-with-dashes', 'Option_with_underscores'],
      },
    })
    
    expect(wrapper.props('options')).toEqual([
      'Option with spaces',
      'Option-with-dashes', 
      'Option_with_underscores'
    ])
  })

  it('should handle numeric string options', () => {
    const wrapper = mount(FormRadio, {
      props: {
        ...defaultProps,
        options: ['1', '2', '3', '10', '100'],
      },
    })
    
    expect(wrapper.props('options')).toEqual(['1', '2', '3', '10', '100'])
  })
})