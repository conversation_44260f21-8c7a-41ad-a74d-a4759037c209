import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'

import FormPulldown from '~/components/form/FormPulldown.vue'

describe('FormPulldown', () => {
  const defaultProps = {
    elementId: 'test-pulldown',
    label: 'Test Label',
    options: ['Option 1', 'Option 2', 'Option 3'],
    modelValue: '',
  }

  it('should render correctly with basic props', () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    expect(wrapper.text()).toContain('Test Label')
    expect(wrapper.find('.flex.flex-col.space-y-1').exists()).toBe(true)
    expect(wrapper.find('.text-sm').text()).toBe('Test Label')
  })

  it('should render default label when no label provided', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        label: undefined,
      },
    })
    
    expect(wrapper.text()).toContain('<ラベル>')
  })

  it('should emit update:model-value when selection changes', async () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    // Trigger the emit directly since we can't interact with USelect in this environment
    await wrapper.vm.$emit('update:model-value', 'test-pulldown', 'Option 1')
    
    expect(wrapper.emitted('update:model-value')).toBeTruthy()
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['test-pulldown', 'Option 1'])
  })

  it('should pass correct props to USelect', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        disabled: true,
        modelValue: 'Option 2',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['Option 1', 'Option 2', 'Option 3'])
    expect(wrapper.props('disabled')).toBe(true)
    expect(wrapper.props('modelValue')).toBe('Option 2')
  })

  it('should handle empty options array', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        options: [],
      },
    })
    
    expect(wrapper.props('options')).toEqual([])
    expect(wrapper.text()).toContain('Test Label')
  })

  it('should handle disabled state', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        disabled: true,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(true)
  })

  it('should handle different elementId values', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        elementId: 'custom-pulldown-id',
      },
    })
    
    expect(wrapper.props('elementId')).toBe('custom-pulldown-id')
  })

  it('should handle modelValue updates', async () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ modelValue: 'Option 2' })
    expect(wrapper.props('modelValue')).toBe('Option 2')
  })

  it('should handle options updates', async () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    const newOptions = ['New Option 1', 'New Option 2']
    await wrapper.setProps({ options: newOptions })
    expect(wrapper.props('options')).toEqual(newOptions)
  })

  it('should handle label updates', async () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    await wrapper.setProps({ label: 'Updated Label' })
    expect(wrapper.find('.text-sm').text()).toBe('Updated Label')
  })

  it('should work with Japanese options', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        options: ['選択肢1', '選択肢2', '選択肢3'],
        label: '日本語ラベル',
      },
    })
    
    expect(wrapper.props('options')).toEqual(['選択肢1', '選択肢2', '選択肢3'])
    expect(wrapper.find('.text-sm').text()).toBe('日本語ラベル')
  })

  it('should handle undefined/null modelValue', () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        modelValue: undefined as any,
      },
    })
    
    expect(wrapper.props('modelValue')).toBe('')
  })

  it('should emit with correct elementId when changed', async () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        elementId: 'special-id',
      },
    })
    
    await wrapper.vm.$emit('update:model-value', 'special-id', 'Selected Value')
    
    expect(wrapper.emitted('update:model-value')?.[0]).toEqual(['special-id', 'Selected Value'])
  })

  it('should maintain component structure', () => {
    const wrapper = mount(FormPulldown, {
      props: defaultProps,
    })
    
    const container = wrapper.find('.flex.flex-col.space-y-1')
    expect(container.exists()).toBe(true)
    
    const label = container.find('.text-sm')
    expect(label.exists()).toBe(true)
    expect(label.text()).toBe('Test Label')
  })

  it('should handle boolean disabled prop correctly', async () => {
    const wrapper = mount(FormPulldown, {
      props: {
        ...defaultProps,
        disabled: false,
      },
    })
    
    expect(wrapper.props('disabled')).toBe(false)
    
    await wrapper.setProps({ disabled: true })
    expect(wrapper.props('disabled')).toBe(true)
  })
})