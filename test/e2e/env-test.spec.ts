import { describe, it, expect } from 'vitest'

// Load environment variables manually
const TEST_USERNAME = process.env.TEST_USERNAME || 'consul-admin'
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'P@ssw0rd'

describe('Environment Variables Test', () => {
  it('should load test credentials from environment', () => {
    console.log('TEST_USERNAME:', TEST_USERNAME)
    console.log('TEST_PASSWORD:', TEST_PASSWORD ? '***' + TEST_PASSWORD.slice(-3) : 'undefined')
    
    expect(TEST_USERNAME).toBeDefined()
    expect(TEST_PASSWORD).toBeDefined()
    expect(TEST_USERNAME.length).toBeGreaterThanOrEqual(6)
    expect(TEST_PASSWORD.length).toBeGreaterThanOrEqual(8)
  })
})