# WorkWay(SNS相談システム) - Frontend

Look at [Nuxt docs](https://nuxt.com/docs/getting-started/introduction) and [Nuxt UI docs](https://ui.nuxt.com) to learn more.

## ローカル開発環境

```
nodejs v20.x
pnpm v8.9.2 (おすすめ)
or 
npm v10.1.0
```

## Setup

Make sure to install the dependencies:

```bash
# pnpm
pnpm install

# npm
npm install
```

## Development Server

Create .env.local file

```
NUXT_USE_MOCK_API=true
```

Start the development server on `http://localhost:3000`:

```bash
# pnpm
pnpm run dev

# npm
npm run dev
```

## Production

Build the application for production:

```bash
# npm
npm run generate

# pnpm
pnpm run generate

# yarn
yarn generate

# bun
bun run generate
```

Locally preview production build:

```bash
# pnpm
pnpm run preview

# npm
npm run preview

