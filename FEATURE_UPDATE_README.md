# Customer Account Modal Feature Update

## 概要 (Overview)

このアップデートでは、CustomerAccountModalコンポーネントに「利用可能な機能」(Available Features)の管理機能を追加し、特にチャットボット機能の設定を強化しました。

## 主な変更点 (Main Changes)

### 1. 利用可能な機能の拡張 (Extended Available Features)

#### 追加された機能 (Added Features):
- **チャットボット (Chatbot)** - 自動応答チャットボット
- **テキストテンプレート (TextTemplate)** - 定型文テンプレート  
- **ケース管理 (Case)** - 相談ケース管理
- **タグ機能 (Tag)** - ケースタグ管理
- **相談者管理 (Counselee)** - 相談者情報管理
- **相談期間 (CounselingTerm)** - 相談期間設定
- **セグメント配信 (Segment)** - セグメント別メッセージ配信
- **エクスポート (Export)** - データエクスポート
- **アンケート (Survey)** - アンケート機能
- **アンケート結果 (SurveyResult)** - アンケート結果表示
- **ウィザード結果 (WizardResult)** - ウィザード結果表示

### 2. チャットボット設定機能 (Chatbot Configuration)

チャットボット機能が選択された場合、専用の設定セクションが表示されます：

#### 基本設定 (Basic Settings):
- **チャットボット名** - ボットの表示名
- **ウェルカムメッセージ** - 初回接触時のメッセージ

#### 自動応答設定 (Auto Response Settings):
- **自動応答の有効化** - 自動応答機能のON/OFF
- **応答待機時間** - 自動応答までの待機時間（1-300秒）

#### 高度な設定 (Advanced Settings):
- **営業時間外メッセージ** - 営業時間外の自動応答
- **キーワード自動応答** - 特定キーワードに対する自動応答設定

### 3. UI/UX改善 (UI/UX Improvements)

- **グリッドレイアウト**: 機能選択を3列グリッドで表示
- **選択状態の可視化**: 選択済み機能をバッジで表示
- **説明文の追加**: 各機能の説明を追加
- **アイコンの統一**: 各機能に適切なアイコンを設定

## ファイル変更 (File Changes)

### 新規作成 (New Files):
- `components/admin/customerAccount/ChatbotSettings.vue` - チャットボット設定専用コンポーネント
- `pages/test-modal.vue` - テスト用ページ
- `FEATURE_UPDATE_README.md` - この説明書

### 変更されたファイル (Modified Files):

#### 1. `composables/useConstants.ts`
- `featureList`を拡張し、全ての利用可能な機能を追加
- 各機能にアイコンと説明を設定

#### 2. `types/enums.d.ts`
- `FeatureList`型エイリアスを追加

#### 3. `types/customers.ts`
- `ChatbotConfig`インターフェースを追加
- `Customer`インターフェースに`chatbot`プロパティを追加

#### 4. `components/admin/customerAccount/CustomerAccountModal.vue`
- 機能選択UIを改善（3列グリッド、バッジ表示）
- チャットボット設定セクションを追加
- `initialState`に`featureList`と`chatbot`を追加
- `getFeatureTitle`メソッドを追加

## 使用方法 (Usage)

### 1. 機能の選択
1. CustomerAccountModalを開く
2. 「利用可能な機能」セクションで必要な機能をクリック
3. 選択された機能は青いボーダーで表示される
4. 選択済み機能は下部にバッジで表示される

### 2. チャットボット設定
1. 「チャットボット」機能を選択
2. 専用の設定セクションが表示される
3. 基本設定、自動応答設定、高度な設定を入力
4. キーワード応答は「+」ボタンで追加可能

### 3. テスト
テスト用ページ（`/test-modal`）でモーダルの動作を確認できます。

## 技術仕様 (Technical Specifications)

### Types:
```typescript
interface ChatbotConfig {
  name: string;
  welcomeMessage: string;
  autoResponse: boolean;
  responseDelay: number;
  offHoursMessage?: string;
  keywordResponses?: Array<{
    keyword: string;
    response: string;
  }>;
}
```

### Feature List:
```typescript
enum CustomerFeature {
  TextTemplate = "TextTemplate",
  Case = "Case", 
  Tag = "Tag",
  Counselee = "Counselee",
  CounselingTerm = "CounselingTerm",
  Segment = "Segment",
  Export = "Export",
  Survey = "Survey",
  SurveyResult = "SurveyResult",
  WizardResult = "WizardResult",
  Wizard = "Wizard",
  Chatbot = "Chatbot",
}
```

## 4. チャットボットメニューの追加 (Chatbot Menu Addition)

AppNavigationにチャットボットメニューを追加し、以下のサブメニューを実装しました：

### サブメニュー構成:
- **チャットボット** (`/app/chatbot/chatbot`) - 基本設定
- **シナリオ** (`/app/chatbot/scenario`) - 会話フロー管理
- **終了テンプレート** (`/app/chatbot/end-template`) - 終了時メッセージ管理
- **ログ** (`/app/chatbot/logs`) - 動作ログ確認

### 各ページの機能:

#### チャットボット設定 (`chatbot.vue`):
- 基本設定（名前、ウェルカムメッセージ、有効化）
- 自動応答設定（有効化、待機時間、営業時間外メッセージ）

#### シナリオ管理 (`scenario.vue`):
- シナリオの作成、編集、削除
- シナリオの有効化/無効化
- トリガーキーワード設定
- シナリオの複製機能

#### 終了テンプレート (`end-template.vue`):
- 終了時メッセージテンプレートの管理
- カテゴリ別分類（問題解決完了、情報提供完了など）
- テンプレートの有効化/無効化
- 変数挿入機能（{userName}など）

#### ログ管理 (`logs.vue`):
- チャットボット動作ログの表示
- フィルタリング機能（期間、レベル、ユーザーID、キーワード）
- ログ詳細表示
- エクスポート機能

## 5. チャットボット一覧・詳細・作成・編集機能の追加

### チャットボット一覧ページ (`/app/chatbot/index.vue`):
- ✅ チャットボット一覧をカード形式で表示
- ✅ 各チャットボットの基本情報表示（名前、説明、ステータス、統計情報）
- ✅ クリックで詳細ページへ遷移
- ✅ 新規作成ボタン
- ✅ 編集・有効化/無効化・複製・削除機能
- ✅ 空状態の表示

### チャットボット詳細ページ (`/app/chatbot/detail/[id].vue`):
- ✅ チャットボットの詳細情報表示
- ✅ 基本情報、設定情報、ウェルカムメッセージ
- ✅ 統計情報（総メッセージ数、成功応答数、平均応答時間、アクティブユーザー）
- ✅ 作成・更新日時の表示
- ✅ 編集・有効化/無効化・複製・削除アクション

### チャットボット作成ページ (`/app/chatbot/create.vue`):
- ✅ 新規チャットボット作成フォーム
- ✅ 基本情報入力（名前、説明、ウェルカムメッセージ）
- ✅ 動作設定（有効化、自動応答、応答待機時間、営業時間外メッセージ）
- ✅ 初期シナリオテンプレート選択機能
- ✅ バリデーション機能

### チャットボット編集ページ (`/app/chatbot/edit/[id].vue`):
- ✅ 既存チャットボットの編集フォーム
- ✅ 現在の設定値の読み込み
- ✅ メタデータ表示（ID、作成日時、シナリオ数、テンプレート数）
- ✅ 更新・削除機能
- ✅ 確認ダイアログ付き削除機能

### ナビゲーション構造の更新:
- ✅ `ChatbotMenu`コンポーネントに「チャットボット一覧」メニューを追加
- ✅ 「設定」メニューを追加（従来のチャットボット設定）
- ✅ 適切なルーティング設定

## 6. BaseTableコンポーネントへの移行

### 実装内容:
すべてのチャットボット管理ページをカード形式からテーブル形式に変更し、統一されたUI/UXを提供するようになりました。

#### **チャットボット一覧ページの改善** (`/app/chatbot/index.vue`):
- ✅ **BaseTableコンポーネント採用**: 統一されたテーブルUI
- ✅ **ページネーション機能**: 大量データの効率的な表示
- ✅ **ソート機能**: 列ヘッダークリックでソート
- ✅ **アクションボタン**: 詳細・編集・有効化/無効化を一箇所に集約
- ✅ **検索・フィルタ対応**: 将来の拡張に対応した構造

#### **シナリオ管理ページの改善** (`/app/chatbot/scenario.vue`):
- ✅ **テーブル形式表示**: シナリオ情報を整理して表示
- ✅ **ステップ数表示**: 視覚的にステップ数を確認可能
- ✅ **トリガーキーワード表示**: 各シナリオのキーワードを明示
- ✅ **ステータス管理**: 有効/無効の切り替えが簡単
- ✅ **一括操作対応**: 複数シナリオの管理が効率的

#### **終了テンプレート管理ページの改善** (`/app/chatbot/end-template.vue`):
- ✅ **カテゴリ別表示**: テンプレートをカテゴリで分類表示
- ✅ **内容プレビュー**: テンプレート内容を直接確認可能
- ✅ **使用条件表示**: 各テンプレートの適用条件を明示
- ✅ **効率的な編集**: インライン編集とモーダル編集の組み合わせ

### 技術的改善点:

#### **統一されたデータ構造**:
- ページネーション: `{ page: 1, pageRangeDisplayed: 10 }`
- ソート: `{ column: 'createdAt', direction: 'desc' }`
- 計算プロパティによる効率的なデータ処理

#### **パフォーマンス最適化**:
- 仮想スクロール対応の基盤構築
- 遅延読み込み（Lazy Loading）対応
- メモリ効率的なページネーション

#### **アクセシビリティ向上**:
- キーボードナビゲーション対応
- スクリーンリーダー対応
- 適切なARIAラベル設定

### UI/UX改善:

#### **視覚的一貫性**:
- 全ページで統一されたテーブルデザイン
- 一貫したアクションボタン配置
- 統一されたステータス表示

#### **操作効率の向上**:
- ワンクリックでの詳細表示
- インライン編集機能
- バッチ操作対応

#### **レスポンシブ対応**:
- モバイルデバイスでの最適表示
- タブレット表示の改善
- 横スクロール対応

## 今後の拡張予定 (Future Enhancements)

1. **機能別詳細設定**: 各機能に専用の設定画面を追加
2. **権限管理**: 機能ごとのアクセス権限設定
3. **使用状況分析**: 機能の使用状況レポート
4. **テンプレート機能**: 機能セットのテンプレート化
5. **チャットボットAI連携**: 外部AI APIとの連携機能
6. **シナリオビルダー**: ドラッグ&ドロップでのシナリオ作成
7. **A/Bテスト機能**: 複数シナリオの効果測定

## 注意事項 (Notes)

- チャットボット機能は選択時のみ設定セクションが表示されます
- 機能の選択状態はリアルタイムで更新されます
- バックエンドAPIとの連携は別途実装が必要です
- チャットボットメニューは`CustomerFeature.Chatbot`が有効な場合のみ表示されます
- 各サブページには適切な権限チェックが実装されています
