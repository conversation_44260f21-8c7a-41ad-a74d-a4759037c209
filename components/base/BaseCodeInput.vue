<template>
  <div class="flex flex-row space-x-2 md:space-x-4">
    <div v-for="i in 6" class="h-16 w-16">
      <UInput
        :id="`pass-code-${i}`"
        ref="passCodeInputEls"
        size="xl"
        :ui="{
          size: {
            xl: 'h-16 text-center text-3xl md:text-4xl font-medium !px-0 focus:scale-110 transition-all duration-200',
          },
        }"
        :value.number="passCode[i - 1]"
        maxlength="1"
        @input.prevent="onInput"
        :data-index="i - 1"
        @keydown.delete.prevent="onDeleteCode"
        :disabled="props.disabled"
        @keypress="isNumber($event)"
        pattern="[0-9]*"
        inputmode="numeric"
        max="9"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: String,
      default: "",
    },
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
  });

  const isNumber = (evt: KeyboardEvent): void => {
    const keysAllowed: string[] = [
      "0",
      "1",
      "2",
      "3",
      "4",
      "5",
      "6",
      "7",
      "8",
      "9",
    ];
    const keyPressed: string = evt.key;

    if (!keysAllowed.includes(keyPressed)) {
      evt.preventDefault();
    }
  };
  const emits = defineEmits(["update:modelValue"]);

  const passCode = ref<string[]>(Array.from(props.modelValue));
  const passCodeInputEls = ref<any[]>([]);
  const onInput = (e: InputEvent) => {
    // check if the input is not a number, then return
    if (isNaN(Number((e.target as HTMLInputElement).value))) {
      return;
    }
    const target = e.target as HTMLInputElement;
    const value = target.value;
    const index = Number(target.dataset.index);
    if (value) {
      passCode.value[index] = value;
      if (index < 5) {
        passCodeInputEls.value[index + 1].input?.focus();
        passCodeInputEls.value[index + 1].input?.select();
      }
    } else {
      passCode.value[index] = "";
    }
  };

  watch(
    () => props.modelValue,
    (value) => {
      // get the first 6 characters and parse it to an array
      passCode.value = value.slice(0, 6).split("");
    },
  );

  const onDeleteCode = (e: KeyboardEvent) => {
    const target = e.target as HTMLInputElement;
    const index = Number(target.dataset.index);
    passCode.value[index] = "";
    if (index > 0) {
      nextTick(() => {
        passCodeInputEls.value[index - 1].input?.focus();
        passCodeInputEls.value[index - 1].input?.select();
      });
    }
  };

  watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        passCode.value = Array.from(value);
      }
    },
  );

  watch(
    () => passCode.value,
    (value) => {
      emits("update:modelValue", value.join(""));
    },
    { deep: true },
  );

  onMounted(() => {
    nextTick(() => {
      passCodeInputEls.value[0]?.input?.focus();
    });
  });
</script>
