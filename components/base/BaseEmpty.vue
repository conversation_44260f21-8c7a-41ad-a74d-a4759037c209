<template>
  <div class="flex flex-col items-center justify-center py-6 gap-3">
    <Icon :icon="icon" class="text-gray-400 text-3xl" />
    <span class="text-sm text-gray-400"> {{ text }} </span>
  </div>
</template>

<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  const props = defineProps<{
    textClass?: string;
    wrapClass?: string;
    text: string;
    icon: string;
  }>();
</script>
