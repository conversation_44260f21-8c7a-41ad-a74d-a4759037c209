<template>
  <UInput
    v-model="password"
    :type="showPassword ? 'text' : 'password'"
    autocomplete="off"
    :ui="{ icon: { trailing: { pointer: '' } } }"
  >
    <template #trailing>
      <UButton
        :padded="false"
        @click="showPassword = !showPassword"
        color="gray"
        variant="link"
        :icon="
          showPassword ? 'i-heroicons-eye-slash-solid' : 'i-heroicons-eye-solid'
        "
      />
    </template>
  </UInput>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: String,
      required: true,
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const showPassword = ref(false);
  const password = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits("update:modelValue", value);
    },
  });
</script>
