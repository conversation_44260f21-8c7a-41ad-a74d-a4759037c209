<template>
  <div
    class="ring-1 bg-white shadow-md p-6 group-hover:shadow-lg group-hover:scale-105 group-hover:cursor-pointer transition-all duration-300"
    @click="onSelect"
    :class="{
      'ring-primary-200 ring-4': active,
      'ring-gray-200': !active,
      'rounded-3xl': img,
      'rounded-2xl': !img,
    }"
  >
    <div
      class="relative w-full h-full justify-center items-center flex group-hover:bg-primary-100 rounded-2xl"
      :class="{
        'bg-primary-100': active,
        'bg-gray-200': !active && img,
      }"
    >
      <div
        v-if="active"
        class="border-2 border-white bg-white absolute -top-2 -right-2 flex rounded-full"
      >
        <UIcon
          class="text-3xl text-primary-600"
          name="i-icon-park-twotone-check-one"
        />
      </div>
      <img
        v-if="img"
        :src="img"
        class="w-3/5 mx-auto grayscale group-hover:grayscale-0"
        :class="{
          'filter grayscale': !active,
          'grayscale-0': active,
        }"
      />
    </div>
    <div class="text-center my-2 font-semibold">
      {{ $t(value) }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from "vue";
  const props = defineProps<{
    value: string;
    img?: string;
    modelValue: string[];
  }>();

  const active = computed(() => props.modelValue.includes(props.value));

  const emits = defineEmits(["update:modelValue"]);

  const onSelect = () => {
    // // check if the value is already selected
    // if (props.modelValue.includes(props.value)) {
    //   // remove the value from the modelValue
    //   emits(
    //     "update:modelValue",
    //     props.modelValue.filter((v) => v !== props.value),
    //   );
    // } else {
    //   // add the value to the modelValue
    //   emits("update:modelValue", [...props.modelValue, props.value]);
    // }

    emits("update:modelValue", [props.value]);
  };
</script>
