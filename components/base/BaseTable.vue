<script lang="ts" setup>
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  pagination: {
    type: Object as PropType<Pagination>,
    default: () => ({})
  },
  pageFrom: {
    type: Number,
    default: 0
  },
  pageTo: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  }
})

const emits = defineEmits(['update:page', 'update:page-count'])
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-4 py-5' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700',
      },
      footer: { padding: 'p-4' },
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="font-semibold text-xl text-gray-900 dark:text-white leading-tight"
        >
          {{ props.title }}
        </h2>
        <div>
          <slot name="header-right" />
        </div>
      </div>
    </template>

    <!-- Header and Action buttons -->
    <div class="flex justify-between items-center w-full px-4 py-3">
      <div class="flex items-center gap-1.5">
        <span class="text-sm leading-5">1ページあたりの表示数</span>

        <USelect
          :model-value="pagination.pageRangeDisplayed"
          :options="[3, 5, 10, 20, 30, 40]"
          class="me-2 w-20"
          size="xs"
          @update:model-value="(value: any) => emits('update:page-count', value)"
        />
      </div>

      <div class="flex gap-1.5 items-center">
        <slot name="action"></slot>
      </div>
    </div>

    <!-- Table -->
    <div>
      <slot />
    </div>
    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div>
          <span class="text-sm leading-5">
            <span class="font-medium">{{ total }}</span> 件中
            <span class="font-medium">{{ pageFrom }}</span> ～
            <span class="font-medium">{{ pageTo }}</span> 件の結果を表示
          </span>
        </div>

        <UPagination
          :model-value="pagination.page"
          :page-count="pagination.pageRangeDisplayed"
          :total="total"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline',
              },
            },
          }"
          @update:model-value="(value: any) => emits('update:page', value)"
        />
      </div>
    </template>
  </UCard>
</template>
