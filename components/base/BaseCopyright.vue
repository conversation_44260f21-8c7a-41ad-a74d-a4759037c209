<template>
  <div class="space-x-3">
    <div
      class="flex flex-row items-center space-x-2 text-xs py-2 px-4 rounded-md bg-gray-500/10"
      :class="wrapClass"
    >
      <div :class="textClass">
        Copyright © {{ new Date().getFullYear() }} {{ $t("copyright") }}
      </div>
      <div class="text-gray-500">Version {{ version }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps<{
    textClass?: string;
    wrapClass?: string;
  }>();
  const runtimeConfig = useRuntimeConfig();

  const version = computed(() => {
    const { NUXT_APP_MAJOR_VERSION, NUXT_APP_VERSION, NUXT_APP_BUILD_DATE } =
      runtimeConfig.public;
    return `${NUXT_APP_MAJOR_VERSION}.${NUXT_APP_VERSION}-${NUXT_APP_BUILD_DATE}`;
  });
</script>
