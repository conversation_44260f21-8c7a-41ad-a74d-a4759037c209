<template>
  <div>
    <div class="relative rounded-lg w-14 text-center text-4xl h-16">
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-[1px] rounded-t-lg w-full z-50"
        :class="[`bg-${color}-100`]"
      ></div>
      <div
        class="absolute top-0 h-1/2 rounded-t-lg w-full flip"
        :id="label"
        :class="[`bg-${color}-100`]"
      ></div>
      <div
        class="absolute bottom-0 h-1/2 rounded-b-lg w-full"
        :class="[`bg-${color}-200`]"
      ></div>
      <div class="bg-red-200 hidden"></div>
      <div class="bg-green-200 hidden"></div>
      <div
        class="absolute h-full flex items-center justify-center w-full"
        :class="[`bg-${color}-50/20`]"
      >
        {{ number }}
      </div>
    </div>
    <div class="text-center text-sm">{{ label }}</div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    number: {
      type: Number,
      default: 0,
    },
    label: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: "gray",
    },
  });
</script>
