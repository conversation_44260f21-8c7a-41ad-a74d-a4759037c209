<template><VCalendar locale="ja" /></template>

<script setup lang="ts">
const attributes = [
  // This is a single attribute
  {
    // An optional key can be used for retrieving this attribute later,
    // and will most likely be derived from your data object
    key: "Any",
    // Attribute type definitions
    content: "red", // <PERSON>olean, String, Object
    highlight: true, // <PERSON>olean, String, Object
    dot: true, // Boolean, String, Object
    bar: true, // Boolean, String, Object
    popover: {}, // Only objects allowed
    // Your custom data object for later access, if needed
    customData: {},
    // We also need some dates to know where to display the attribute
    // We use a single date here, but it could also be an array of dates,
    //  a date range or a complex date pattern.
    dates: new Date(),
    // Think of `order` like `z-index`
    order: 0,
  },
];
</script>
