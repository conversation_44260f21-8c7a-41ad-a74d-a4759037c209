<template>
  <UPopover mode="click" :popper="{ placement: 'bottom-start' }" :ui="{ trigger: '!flex' }">
    <UInput
      color="gray"
      :model-value="formatDate(modelValue)"
      :class="block ? 'w-full' : 'w-40'"
      :disabled="disabled"
    >
      <template #leading>
        <UIcon name="i-heroicons-calendar-days" />
      </template>
    </UInput>
    <template #panel="{ close }">
      <VDatePicker
        v-model="date"
        :attributes="attrs"
        title-position="left"
        locale="ja"
        trim-weeks
        @dayclick="close"
      />
    </template>
  </UPopover>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: Date;
  disabled: boolean | undefined | null;
  block: boolean | undefined;
}>();

const emits = defineEmits(["update:modelValue"]);

const date = computed({
  get: () => props.modelValue,
  set: (val) => emits("update:modelValue", val),
});

const attrs = ref([
  {
    key: "today",
    highlight: { color: "green", fillMode: "solid" },
    dates: new Date(),
  },
]);
</script>
