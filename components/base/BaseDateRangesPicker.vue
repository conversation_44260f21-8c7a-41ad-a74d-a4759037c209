<template>
  <UPopover :popper="{ placement: 'bottom-start' }">
    <UInput
      :model-value="rangeFormat"
      :class="block ? 'w-full' : 'w-40'"
      :disabled="disabled"
    >
      <template #leading>
        <UIcon name="i-heroicons-calendar-days" />
      </template>
    </UInput>
    <template #panel="{ close }">
      <VDatePicker
        v-model.range="range"
        title-position="left"
        trim-weeks
        locale="ja"
        :columns="columns"
        @update:modelValue="close"
      />
    </template>
  </UPopover>
</template>

<script setup lang="ts">
const props = defineProps<{
  modelValue: {
    start: Date | null;
    end: Date | null;
  };
  disabled: boolean | undefined;
  block: boolean | undefined;
}>();

const emits = defineEmits(["update:modelValue"]);

const range = computed({
  get: () => props.modelValue,
  set: (val) => emits("update:modelValue", val),
});

const rangeFormat = computed(() => {
  const start = range.value.start;
  const end = range.value.end;
  if (!start || !end) return "";
  return `${formatDate(start)} - ${formatDate(end)}`;
});

import { useScreens } from "vue-screen-utils";

const { mapCurrent } = useScreens({
  xs: "0px",
  sm: "640px",
  md: "768px",
  lg: "1024px",
});
const columns = mapCurrent({ lg: 2 }, 1);
</script>
