<template>
  <div class="flex flex-inline items-center w-full justify-between space-x-6">
    <div v-for="channel in snsChannels" :key="channel">
      <UCheckbox si>
        <template #label>
          <div>
            <component :is="getSNSIconComponent(channel)" class="h-6 w-6" />
          </div>
        </template>
      </UCheckbox>
    </div>
    <!-- <UCard
      v-for="channel in snsChannels"
      :key="channel"
      class="relative text-center w-full hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
      :ui="{
        body: {
          padding: '!px-2 !py-4',
        },
      }"
      :class="{
        'bg-primary-100 dark:bg-gray-800': modelValue[channel],
      }"
      @click="
        $emit('update:modelValue', {
          ...modelValue,
          [channel]: !modelValue[channel],
        })
      "
    >
      <div class="flex flex-inline justify-center">
        <component :is="getSNSIconComponent(channel)" class="h-12 w-12" />
      </div>
      <div
        class="mt-2 flex items-center justify-center"
        :class="{ 'text-primary-700 font-semibold': modelValue[channel] }"
      >
        <UIcon
          v-if="modelValue[channel]"
          name="i-heroicons-check-circle-solid"
          class="text-xl text-primary-600 mr-1"
        />
        {{ $t(channel) }}
      </div>
    </UCard> -->
  </div>
</template>

<script lang="ts" setup>
import { SNSChannel } from '~/types/enums.d'

const snsChannels = Object.values(SNSChannel)
defineProps({
  modelValue: {
    type: Object as PropType<{
      facebook: false;
      line: false;
      application: false;
    }>,
    default: () => {}
  }
})

defineEmits(['update:modelValue'])
</script>
