<template>
  <div class="flex flex-row space-x-2 w-full justify-between">
    <div v-for="(day, index) in days" class="w-full">
      <div
        @click="onUpdateDayWorkingTime(day)"
        class="border h-full pt-6 pb-3 rounded-xl text-center flex flex-col justify-between space-y-3 cursor-pointer hover:scale-105 hover:border-1 hover:border-primary-300 transition-all duration-200"
        :class="{
          'bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-900': isRegularHoliday(day),
          'bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-900': !isRegularHoliday(day),
        }"
      >
        <div class="text-3xl" :class="[`text-${getDayColor(day)}-500`]">
          {{ $t("day_" + day) }}
        </div>

        <div class="px-2">
          <UBadge
            v-if="isRegularHoliday(day)"
            color="red"
            variant="solid"
            class="w-full"
            size="xs"
            :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
          >
            定休日
          </UBadge>
          <div v-else class="flex flex-col space-y-1">
            <UBadge
              v-for="row in workingDayTimes.find((obj) => obj.day?.key === day)
                ?.day.open"
              color="green"
              variant="solid"
              size="xs"
              class="w-full"
              :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
            >
              {{ row?.startTime }} ~ {{ row?.endTime }}
            </UBadge>
          </div>
        </div>
      </div>
    </div>
  </div>
  <UModal v-model="isOpenModal" :ui="{ width: 'sm:max-w-md' }">
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            営業時間の設定
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="onCloseModal"
          />
        </div>
      </template>

      <div class="flex flex-col space-y-6">
        <div>
          <UFormGroup label="対象曜日">
            <div class="flex flex-row justify-between space-x-2">
              <div
                v-for="day in days"
                class="flex relative border w-full justify-center items-center py-2 rounded-lg cursor-pointer hover:scale-110 hover:border-primary-300 transition-all duration-100"
                :class="{
                  'border-primary-500 text-primary-500':
                    selectedDays.includes(day),
                }"
                @click="onSelectDay(day)"
              >
                <UIcon
                  v-if="selectedDays.includes(day)"
                  name="i-heroicons-check-circle-solid"
                  class="absolute -top-1.5 -right-1.5 text-primary-500"
                />
                {{ $t("day_" + day) }}
              </div>
            </div>
          </UFormGroup>
        </div>
        <div class="flex flex-row space-x-4">
          <div><UToggle v-model="state.isRegularHoliday" /></div>
          <div
            class="text-sm cursor-pointer"
            @click="state.isRegularHoliday = !state.isRegularHoliday"
          >
            定休日
          </div>
        </div>
        <div
          v-for="(part, index) in selectedDayWorkingTimes.day?.openRanges"
          class="flex flex-row items-end space-x-4"
        >
          <div
            class="relative flex flex-1"
            v-if="selectedDayWorkingTimes.day?.openRanges"
          >
            <UFormGroup
              class="w-full"
              :label="`[${index + 1}部] 開始時間 ~ 終了時間`"
            >
              <VueDatePicker
                v-model="selectedDayWorkingTimes.day.openRanges[index]"
                time-picker
                range
                :teleport="true"
                placeholder="営業時間を選択してください"
                menu-class-name="custom-menu"
                select-text="確定"
                cancel-text="閉じる"
                input-class-name="text-center form-input relative block w-full disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none border-0 rounded-md placeholder-gray-400 dark:placeholder-gray-500 text-sm px-2.5 !py-1 shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white ring-0 ring-inset ring-gray-300 dark:ring-gray-700 focus:ring-primary-500 dark:focus:ring-primary-400 ps-9"
                hide-input-icon
                :clearable="false"
              />
            </UFormGroup>
          </div>
          <UButton
            v-if="
              selectedDayWorkingTimes.day?.openRanges.length &&
              selectedDayWorkingTimes.day?.openRanges.length > 1
            "
            icon="i-heroicons-trash"
            size="sm"
            color="red"
            variant="soft"
            label="削除"
            :trailing="false"
            class="mb-1"
            @click="selectedDayWorkingTimes.day?.openRanges?.splice(index, 1)"
          />
        </div>
        <div
          v-if="!state.isRegularHoliday"
          class="flex items-center justify-center"
        >
          <UButton
            icon="i-heroicons-plus"
            size="sm"
            color="primary"
            variant="soft"
            label="営業時間を追加"
            :trailing="false"
            :ui="{
              rounded: 'rounded-full',
            }"
            @click="onAddWorkingTime"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between">
          <div></div>
          <div>
            <UButton
              icon="i-heroicons-check-circle-solid"
              class="px-6"
              size="md"
              color="primary"
              variant="solid"
              label="更新"
              :trailing="false"
              :loading="loadings.updateDay"
              @click="onSaveDayWorkingTimes"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script lang="ts" setup>
  import { useCounselingTermsStore } from "~/stores/app/counseling-terms";
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const counselingTermsStore = useCounselingTermsStore();
  const {
    days,
    workingDayTimes,
    selectedDayWorkingTimes,
    loadings,
    counselingTerms,
  } = storeToRefs(counselingTermsStore);
  const isRegularHoliday = (day: number) => {
    const isHasWorkingDayTime = workingDayTimes.value.find(
      (obj) => obj.day?.key === day,
    )?.day?.isOpen;
    return !isHasWorkingDayTime;
  };

  const isOpenModal = ref(false);
  const state = reactive({
    isRegularHoliday: false,
  });

  const onCloseModal = () => {
    counselingTermsStore.setWorkingDayTimes(counselingTerms.value);
    isOpenModal.value = false;
  };

  const selectedDays = ref<number[]>([]);
  const onUpdateDayWorkingTime = (day: number) => {
    if (props.disabled) return;

    selectedDays.value = [day];
    isOpenModal.value = true;
    const selected = workingDayTimes.value.find((obj) => obj.day?.key === day);
    selectedDayWorkingTimes.value = selected || {
      day: {
        key: day,
        openRanges: [],
        isOpen: false,
      } as any,
    };
    state.isRegularHoliday = isRegularHoliday(day);
  };

  const selectedDayWorkingTimesRange = ref<any>([]);
  watch(
    () => selectedDayWorkingTimes.value,
    (val) => {
      selectedDayWorkingTimesRange.value = val.day?.open?.map((obj) => {
        return [
          {
            hours: parseTimeToObject(obj.startTime).hours,
            minutes: parseTimeToObject(obj.startTime).minutes,
          },
          {
            hours: parseTimeToObject(obj.endTime).hours,
            minutes: parseTimeToObject(obj.endTime).minutes,
          },
        ];
      });
    },
  );

  const onSelectDay = (day: number) => {
    if (selectedDays.value.includes(day)) {
      selectedDays.value = selectedDays.value.filter((d) => d !== day);
    } else {
      selectedDays.value = [...selectedDays.value, day];
    }
  };

  const onAddWorkingTime = () => {
    const lastWorkingTime =
      selectedDayWorkingTimes.value?.day?.openRanges[
        selectedDayWorkingTimes.value?.day?.openRanges.length - 1
      ];

    console.log("🚀 ~ onAddWorkingTime ~ lastWorkingTime:", lastWorkingTime);

    // if lst working time is empty, create new one
    if (!lastWorkingTime) {
      selectedDayWorkingTimes.value.day?.openRanges.push([
        {
          hours: 9,
          minutes: 0,
        },
        {
          hours: 18,
          minutes: 0,
        },
      ]);
      return;
    } else {
      // if last working time is not empty, create new one
      selectedDayWorkingTimes.value.day?.openRanges.push([
        {
          hours: lastWorkingTime[1].hours + 1,
          minutes: lastWorkingTime[1].minutes,
        },
        {
          hours: lastWorkingTime[1].hours + 5,
          minutes: lastWorkingTime[1].minutes,
        },
      ]);
    }
  };

  const toast = useToast();
  const onSaveDayWorkingTimes = async () => {
    const key = selectedDays.value;
    const isOpen = !state.isRegularHoliday;
    const open = selectedDayWorkingTimes.value.day?.openRanges.map((obj) => {
      return {
        startTime: parseTimeToString({
          hours: obj[0].hours,
          minutes: obj[0].minutes,
        }),
        endTime: parseTimeToString({
          hours: obj[1].hours,
          minutes: obj[1].minutes,
        }),
      };
    });
    const result = await counselingTermsStore.updateDay(
      currentCustomer.value.customerId as string,
      key,
      isOpen,
      open,
    );

    if (result) {
      isOpenModal.value = false;
      toast.add({
        title: "更新成功",
        description: "営業時間の設定を更新しました。",
        icon: "i-heroicons-check-circle",
      });
    }
  };

  watchEffect(() => {
    if (state.isRegularHoliday && selectedDayWorkingTimes.value.day) {
      selectedDayWorkingTimes.value.day.openRanges = [];
      selectedDayWorkingTimes.value.day.isOpen = false;
    } else if (selectedDayWorkingTimes.value.day?.openRanges?.length === 0) {
      selectedDayWorkingTimes.value.day.isOpen = true;
      selectedDayWorkingTimes.value.day.openRanges.push([
        {
          hours: 9,
          minutes: 0,
        },
        {
          hours: 18,
          minutes: 0,
        },
      ]);
    }
  });
</script>

<style lang="scss">
  .custom-menu {
    .dp__action_buttons {
      flex: 1;
    }
  }
</style>
