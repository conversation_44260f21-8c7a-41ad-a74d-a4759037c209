<template>
  <div class="flex flex-inline items-center space-x-2">
    <UButton
      v-for="option in props.options"
      :size="props.size"
      :color="option.value === props.modelValue ? 'primary' : 'gray'"
      :variant="option.value === props.modelValue ? 'solid' : 'solid'"
      class="px-4"
      @click="$emit('update:modelValue', option.value)"
    >
      {{ option.label }}
    </UButton>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  size: {
    type: String;
    default: "sm";
  };
  options: {
    type: Array<ButtonGroup>;
    default: () => [];
  };
  modelValue: string;
}>();
</script>
