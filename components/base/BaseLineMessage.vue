<template>
  <div class="pl-2 flex flex-row space-x-1">
    <div>
      <UAvatar
        :ui="{
          rounded: 'rounded-full',
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-sm font-semibold text-gray-700 dark:text-gray-800',
          chip: {
            position: {
              'top-right': '-top-1 -right-1',
            },
          },
        }"
        :src="currentCustomer.basic?.customerImage"
        :alt="currentCustomer.basic?.customerName"
        size="sm"
      />
    </div>
    <div>
      <div class="flex flex-row items-end space-x-0.5">
        <p
          class="lmessage relative bg-slate-100 dark:text-gray-900 mt-1 rounded-xl text-[10px] py-2 px-3 max-w-[210px] break-words whitespace-break-spaces"
        >
          {{
            message || "各自動メッセージをクリックしてここでメッセージを確認"
          }}
        </p>
        <div class="text-[8px] text-gray-700 font-thin pb-0.5">
          {{ currentHourMinute }}
        </div>
      </div>
      <div v-if="survey?.surveyName" class="flex flex-row items-end space-x-0.5">
        <p
          class="lmessage relative bg-slate-100 mt-1 rounded-xl text-[10px] py-2 px-3 max-w-[210px] break-words whitespace-break-spaces"
        >
        <div
        
        class="border-primary-700 bg-slate-100 rounded-lg"
      >
        <div class="font-semibold px-0 pb-2 pt-1 text-center truncate">
          {{ survey?.surveyName }}
        </div>
        <div
          class="text-primary-800 border-t py-0 px-0 flex items-center space-x-1"
        >
          <UButton
          :to="surveyLink"
            target="_blank"
            color="primary"
            variant="ghost"
            icon="i-majesticons-open"
            trailing
            class="w-full"
            size="xs"
            block
            >アンケートを開く</UButton
          >
        </div>
      </div>
        </p>
        <div class="text-[8px] text-gray-700 font-thin pb-0.5">
          {{ currentHourMinute }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import dayjs from "dayjs";
  const appCustomersStore = useAppCustomersStore();
  const { customers, loadings, currentCustomer } =
    storeToRefs(appCustomersStore);
  const props = defineProps({
    message: {
      type: String,
      default: "各自動メッセージをクリックしてここでメッセージを確認",
    },
    survey: {
      type: Object as PropType<any>,
      default: null,
    },
  });
  const currentHourMinute = dayjs().format("HH:mm");

  const surveyLink = computed(() => {
    return (
      window.location.origin +
      "/survey/?sid=" +
      props.survey?.surveyId +
      "&c=" +
      currentCustomer.value.customerId
    );
  });
</script>

<style>
  .lmessage::before {
    content: "";
    position: absolute;
    top: 3px;
    left: -5px;
    height: 10px;
    width: 40px;
    background: inherit;
    clip-path: polygon(0 0%, 50% 30%, 30% 90%);
  }
</style>
