<template>
  <div class="flex items-center justify-center w-full">
    <div
      v-if="imageFile"
      class="relative group flex flex-col items-center justify-center w-full border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
    >
      <img :src="imageFile" :class="innerClass" />
      <div
        class="absolute bg-white group-hover:shadow-lg -top-2.5 -right-2.5 h-6 w-6 flex items-center justify-center border border-gray-400 rounded-full"
      >
        <UIcon
          name="i-carbon-close-filled"
          color="gray"
          class="text-xl hover:shadow-lg text-red-500"
          @click="removeFileInput"
        />
      </div>
    </div>
    <label
      v-else
      for="dropzone-file"
      class="flex flex-col items-center justify-center w-full border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-white dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
      :class="innerClass"
    >
      <div class="flex flex-col items-center justify-center pt-5 pb-6">
        <UAvatar v-if="alt" :alt="alt" size="lg" />

        <UIcon
          v-else
          name="i-heroicons-cloud-arrow-up"
          class="text-4xl text-gray-500 dark:text-gray-400 mb-2"
        />
        <template v-if="!hideText">
          <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
            <span class="font-semibold">
              {{ $t("Click to upload") }}
            </span>
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-400">SVG, PNG, JPG</p>
        </template>
      </div>
      <input
        v-if="!disabled"
        id="dropzone-file"
        :value="fileInput"
        type="file"
        class="hidden"
        @change="handleFileUpload"
      />
    </label>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: String,
      default: "",
    },
    hideText: {
      type: Boolean,
      default: false,
    },
    innerClass: {
      type: String,
      default: "h-32",
    },
    alt: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(["update:modelValue"]);

  const imageFile = ref<string | null>(props.modelValue);
  const fileInput = ref<File | null>(null);
  const handleFileUpload = (e: Event) => {
    const files = (e.target as HTMLInputElement).files as FileList;
    if (files?.length > 0) {
      fileInput.value = files[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        imageFile.value = e.target?.result as string;
      };
      reader.readAsDataURL(files[0]);
    }
    emit("update:modelValue", imageFile || null);
  };

  const removeFileInput = () => {
    imageFile.value = null;
    fileInput.value = null;
  };
</script>
