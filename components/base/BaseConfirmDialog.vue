<template>
  <UModal v-model="confirmDialog.open" prevent-close>
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            {{ confirmDialog.title || $t("Confirmation") }}
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="confirmDialog.open = false"
          />
        </div>
        <div
          v-if="confirmDialog.message"
          class="break-words whitespace-break-spaces"
          :class="confirmDialog.messageClass"
        >
          {{ confirmDialog.message }}
        </div>
        <div
          v-if="confirmDialog.survey"
          class="px-4 flex flex-col space-y-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 border p-4 rounded-lg"
        >
          <div class="font-semibold text-sm">選択しているアンケート:</div>
          <div
            class="relative group border border-primary-500 bg-gray-100 dark:bg-gray-700 dark:border-gray-500 cursor-pointer rounded-lg flex items-center justify-center h-20 w-20"
          >
            <UIcon
              name="i-wpf-survey"
              color="gray"
              class="text-4xl text-primary-500"
            />
          </div>
          <div class="text-sm truncate text-primary-500">
            {{ confirmDialog.survey?.surveyName }}
          </div>
        </div>
        <div class="flex flex-inline items-center justify-end space-x-2">
          <UButton
            class="px-6"
            variant="soft"
            color="gray"
            @click="
              confirmDialog.onCancel || dialogsStore.onCloseConfirmDialog()
            "
          >
            {{ confirmDialog.cancelButtonText || $t("No") }}
          </UButton>
          <UButton
            class="min-w-28 justify-center"
            :color="confirmDialog.confirmButtonColor || 'red'"
            @click="onConfirm"
            autofocus
          >
            {{ confirmDialog.confirmButtonText || $t("Yes") }}
          </UButton>
        </div>
      </div>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useDialogsStore } from "~/stores/dialogs";
  const confirmButton = ref(null);
  const dialogsStore = useDialogsStore();
  const { confirmDialog } = storeToRefs(dialogsStore);

  const onConfirm = () => {
    confirmDialog.value.onConfirm();
    dialogsStore.onCloseConfirmDialog();
  };

  watch(
    () => confirmDialog.value.open,
    (open) => {
      if (!open) {
        confirmDialog.value.messageClass = "";
      } else {
        nextTick(() => {
          confirmButton.value?.button?.focus();
          console.log(
            "🚀 ~ nextTick ~ confirmButton.value:",
            confirmButton.value,
          );
        });
      }
    },
  );
</script>
