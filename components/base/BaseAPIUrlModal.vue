<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              APIのURL設定（DEBUG用）
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="onClose"
            />
          </div>
        </template>
        <div class="space-y-6">
          <UFormGroup label="環境" required name="env">
            <USelect
              v-model="selectedEnv"
              :options="envOptions"
              option-attribute="name"
            />
          </UFormGroup>
          <UFormGroup label="APIのURL" required name="url">
            <UInput v-model="state.url" size="lg" autocomplete="off"> </UInput>
          </UFormGroup>
          <UFormGroup label="SocketのURL" required name="socketUrl">
            <UInput v-model="state.socketUrl" size="lg" autocomplete="off">
            </UInput>
          </UFormGroup>
        </div>

        <template #footer>
          <div class="flex justify-end">
            <UButton
              label="適用"
              type="submit"
              class="px-10"
              :loading="loadings['changePassword']"
            />
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useAuthStore } from "~/stores/auth";
  const toast = useToast();

  const authStore = useAuthStore();
  const { loadings, errors } = storeToRefs(authStore);
  const { object, string } = useYup();
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
  });

  const envOptions = ref([
    {
      name: "菅さん",
      value: "kan-san",
    },
    {
      name: "坂東さん",
      value: "bando-san",
    },
    {
      name: "検証環境",
      value: "dev",
    },
  ]);

  const selectedEnv = ref(localStorage.getItem("ENV") || "kan-san");

  const schema = object({
    //  validate url format
    url: string().required().url(),
    // validate socket url format
    socketUrl: string().required(),
  });
  const runtimeConfig = useRuntimeConfig();
  const emit = defineEmits(["close"]);
  const state = reactive({
    url: runtimeConfig.public.NUXT_API_ADMIN_BASE_URL,
    socketUrl: runtimeConfig.public.NUXT_SOCKET_BASE_URL,
  });

  watch(
    () => selectedEnv.value,
    (val) => {
      localStorage.setItem("ENV", val);
      state.url =
        localStorage.getItem(val + "_API_URL") ||
        runtimeConfig.public.NUXT_API_ADMIN_BASE_URL;
      localStorage.setItem("API_URL", state.url);
      state.socketUrl =
        localStorage.getItem(val + "_SOCKET_URL") ||
        runtimeConfig.public.NUXT_SOCKET_BASE_URL;
      localStorage.setItem("SOCKET_URL", state.socketUrl);
    },
  );

  const resetForm = () => {
    state.url = "";
    state.socketUrl = "";
  };
  async function onSubmit() {
    localStorage.setItem(selectedEnv.value + "_API_URL", state.url);
    localStorage.setItem(selectedEnv.value + "_SOCKET_URL", state.socketUrl);
    localStorage.setItem("API_URL", state.url);
    localStorage.setItem("SOCKET_URL", state.socketUrl);

    window.location.reload();
  }

  const onClose = () => {
    resetForm();
    emit("close");
  };
</script>
