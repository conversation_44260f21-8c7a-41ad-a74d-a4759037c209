<template>
  <div
    class="relative mx-auto border-gray-800 dark:border-gray-600 bg-gray-800 border-[13px] rounded-[2.5rem] h-[90vh] max-h-[700px] min-h-[600px] w-[350px] shadow-xl"
  >
    <div
      class="w-[108px] h-[25px] bg-gray-800 dark:bg-gray-400 top-1.5 rounded-[1rem] left-1/2 -translate-x-1/2 absolute z-50"
    />
    <div
      class="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-400 absolute -left-[16px] top-[124px] rounded-l-lg"
    />
    <div
      class="h-[46px] w-[3px] bg-gray-800 dark:bg-gray-400 absolute -left-[16px] top-[178px] rounded-l-lg"
    />
    <div
      class="h-[64px] w-[3px] bg-gray-800 dark:bg-gray-400 absolute -right-[16px] top-[142px] rounded-r-lg"
    />
    <div
      v-show="loading"
      class="rounded-[2rem] relative overflow-hidden w-[324px] h-full bg-[#06C755]"
    >
      <slot />
    </div>
    <div
      v-show="!loading"
      class="rounded-[2rem] relative overflow-hidden w-[324px] h-full"
      :class="[
        bgColor
          ? bgColor
          : `bg-cover bg-no-repeat bg-[url('~/assets/images/line-bg.jpg')]`,
      ]"
    >
      <div class="h-full relative flex flex-col pt-10 pb-0">
        <div class="h-10 absolute top-0 left-0 w-full">
          <div
            class="flex flex-inline items-center justify-between pl-8 pr-6 py-2.5"
          >
            <div class="text-sm font-semibold">
              {{ now }}
            </div>
            <div class="flex flex-row items-center space-x-2">
              <UIcon name="i-fa-solid-signal" />
              <UIcon name="i-fa-solid-wifi" />
              <UIcon name="i-fa-solid-battery-three-quarters" class="text-lg" />
            </div>
          </div>
        </div>
        <slot />
        <div class="h-6 absolute bottom-0 left-0 w-full">
          <div
            class="w-2/5 h-[5px] bg-gray-800 dark:bg-gray-400 top-2.5 rounded-[1rem] left-1/2 -translate-x-1/2 absolute z-50"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from "dayjs";
  const props = defineProps({
    loading: {
      type: Boolean,
      default: false,
    },
    bgColor: {
      type: String,
      default: null,
    },
  });
  const now = ref(dayjs().format("HH:mm"));

  setInterval(() => {
    now.value = dayjs().format("HH:mm");
  }, 1000);
</script>
