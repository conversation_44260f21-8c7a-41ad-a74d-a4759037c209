<template>
  <UPopover
    mode="click"
    :popper="{ placement: 'bottom-start' }"
    :ui="{ trigger: '!flex' }"
  >
    <UInput
      :color="color || 'gray'"
      :model-value="formatDate(modelValue)"
      :class="block ? 'w-full' : 'w-44'"
      :disabled="disabled"
      :ui="{
        icon: { trailing: { pointer: '' } },
        trailing: { padding: { sm: 'pe-4' } },
      }"
    >
      <template #leading>
        <UIcon name="i-heroicons-calendar-days" />
      </template>
      <template #trailing>
        <UButton
          v-show="modelValue && !disabled"
          color="gray"
          variant="link"
          icon="i-heroicons-x-mark-20-solid"
          :padded="false"
          @click="emits('update:modelValue', null)"
        />
      </template>
    </UInput>
    <template #panel="{ close }">
      <VDatePicker
        v-model="date"
        :attributes="attrs"
        title-position="left"
        locale="ja"
        trim-weeks
        @dayclick="close"
      />
    </template>
  </UPopover>
</template>

<script setup lang="ts">
  const props = defineProps<{
    modelValue: Date;
    disabled: boolean | undefined | null;
    block: boolean | undefined;
    color?: string;
  }>();

  const emits = defineEmits(["update:modelValue"]);

  const date = computed({
    get: () => props.modelValue,
    set: (val) => emits("update:modelValue", val),
  });

  const attrs = ref([
    {
      key: "today",
      highlight: { color: "green", fillMode: "solid" },
      dates: new Date(),
    },
  ]);
</script>
