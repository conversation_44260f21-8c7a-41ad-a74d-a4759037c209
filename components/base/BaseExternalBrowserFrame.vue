<template>
  <div class="h-full relative flex flex-col">
    <div class="bg-gray-50/70 flex-1">
      <slot></slot>
    </div>
    <div
      class="bg-gray-200 pb-5 pt-3 flex flex-row items-center justify-between text-sm border-b border-gray-200"
    >
      <div class="flex flex-row pl-3 items-center">
        <UIcon
          v-for="(icon, index) in icons"
          :name="icon"
          class="text-xl -ml-1 hover:-ml-2 transition-all duration-150"
        />
      </div>
      <div class="bg-white w-fit px-12 text-xs py-1 rounded-lg text-center font-light">
        /app/chat
      </div>
      <div class="flex flex-row items-center space-x-3 pr-3">
        <UIcon name="i-ph-caret-left-bold" class="text-xl text-gray-900" />
        <UIcon name="i-ph-caret-right-bold" class="text-xl text-gray-900" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const icons = [
    "i-logos-chrome",
    "i-logos-firefox",
    "i-logos-safari",
    "i-logos-microsoft-edge",
  ];
</script>
