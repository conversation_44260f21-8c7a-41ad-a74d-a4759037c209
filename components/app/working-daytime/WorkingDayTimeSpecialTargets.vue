<template>
  <div class="flex flex-row justify-between px-0 py-2">
    <div class="flex flex-row space-x-2 items-center">
      <div>特別営業時間の設定</div>

      <div class="inline-flex items-center space-x-1 text-sm font-semibold">
        (
        <div>
          {{ currentCalendarPage?.year }}年{{ currentCalendarPage?.month }}月
        </div>
        <UIcon v-if="numberOfMonths > 1" name="i-heroicons-arrow-right" />
        <div v-if="numberOfMonths > 1">
          {{ calendarEndMonth?.year }}年{{ calendarEndMonth?.month }}月
        </div>
        )
      </div>
    </div>
    <div>
      <UButton
        v-if="specialWorkingDayTimesFiltered.length > 0 && !disabled"
        icon="i-heroicons-plus"
        size="xs"
        color="primary"
        variant="soft"
        label="特別営業時間を追加"
        :trailing="false"
        :ui="{
          rounded: 'rounded-full',
        }"
        @click="onAddSpecialWorkingTime"
      />
    </div>
  </div>
  <div class="flex-1 border rounded-md dark:border-gray-700">
    <div
      v-if="specialWorkingDayTimesFiltered.length === 0"
      class="flex flex-col space-y-2 h-full items-center pt-20"
    >
      <UIcon
        name="i-healthicons-i-schedule-school-date-time"
        class="text-5xl text-gray-400"
      />
      <div class="inline-flex items-center space-x-1">
        <div>
          {{ currentCalendarPage?.year }}年{{ currentCalendarPage?.month }}月
        </div>
        <UIcon v-if="numberOfMonths > 1" name="i-heroicons-arrow-right" />
        <div v-if="numberOfMonths > 1">
          {{ calendarEndMonth?.year }}年{{ calendarEndMonth?.month }}月
        </div>
      </div>
      <div class="text-sm">特別営業時間はありません</div>
      <div>
        <UButton
          v-if="!disabled"
          icon="i-heroicons-plus"
          size="sm"
          color="primary"
          variant="soft"
          label="特別営業時間を追加"
          :trailing="false"
          :ui="{
            rounded: 'rounded-full',
          }"
          @click="onAddSpecialWorkingTime"
        />
      </div>
    </div>
    <div v-else>
      <div v-for="item in specialWorkingDayTimesFiltered">
        <WorkingDayTimeSpecialItem
          :item="item"
          @delete="onDeleteSpecialWorkingDayTime"
          @edit="onEditSpecialWorkingDayTime"
          :disabled="disabled"
        />
      </div>
    </div>
  </div>

  <UModal v-model="isOpenModal" :ui="{ width: 'sm:max-w-md' }">
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            特別営業時間の設定
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="isOpenModal = false"
          />
        </div>
      </template>

      <div class="flex flex-col space-y-6">
        <div class="flex items-center mt-2 space-x-4">
          <URadioGroup
            v-model="state.isDateRangesMode"
            legend="日付選択モード"
            :options="datePickerOptions"
            :ui="{
              container: '!flex !flex-row space-x-4',
            }"
          />
        </div>
        <div>
          <UFormGroup
            :label="state.isDateRangesMode ? '対象日付範囲' : '対象日付'"
          >
            <BaseDateRangesPicker
              v-if="state.isDateRangesMode"
              v-model="range"
              block
            />
            <BaseDatePicker v-else v-model="date" block />
          </UFormGroup>
        </div>
        <div class="flex flex-row space-x-4">
          <div><UToggle v-model="state.isRegularHoliday" /></div>
          <div
            class="text-sm cursor-pointer"
            @click="state.isRegularHoliday = !state.isRegularHoliday"
          >
            特別休業日
          </div>
        </div>
        <div
          v-for="(part, index) in selectedDayWorkingTimesSpecial"
          class="flex flex-row items-end space-x-4"
        >
          <div class="relative flex flex-1">
            <UFormGroup
              class="w-full"
              :label="`[${index + 1}部] 開始時間 ~ 終了時間`"
            >
              <VueDatePicker
                v-model="part.rangeObj"
                time-picker
                range
                :teleport="true"
                placeholder="営業時間を選択してください"
                menu-class-name="custom-menu"
                select-text="確定"
                cancel-text="閉じる"
                input-class-name="text-center form-input relative block w-full disabled:cursor-not-allowed disabled:opacity-75 focus:outline-none border-0 rounded-md placeholder-gray-400 dark:placeholder-gray-500 text-sm px-2.5 !py-1 shadow-sm bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white ring-0 ring-inset ring-gray-300 dark:ring-gray-700 focus:ring-primary-500 dark:focus:ring-primary-400 ps-9"
                hide-input-icon
              />
            </UFormGroup>
          </div>
          <UButton
            v-if="selectedDayWorkingTimesSpecial.length > 1"
            icon="i-heroicons-trash"
            size="sm"
            color="red"
            variant="soft"
            label="削除"
            :trailing="false"
            @click="selectedDayWorkingTimesSpecial.splice(index, 1)"
          />
        </div>
        <div
          v-if="!state.isRegularHoliday"
          class="flex items-center justify-center"
        >
          <UButton
            icon="i-heroicons-plus"
            size="sm"
            color="primary"
            variant="soft"
            label="営業時間を追加"
            :trailing="false"
            :ui="{
              rounded: 'rounded-full',
            }"
            @click="onAddWorkingTime"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex flex-row justify-between">
          <div></div>
          <div>
            <UButton
              icon="i-heroicons-check-circle-solid"
              class="px-6"
              size="md"
              color="primary"
              variant="solid"
              label="保存"
              :trailing="false"
              @click="onSaveDayWorkingTimes"
              :loading="loadings.saveSpecialWorkingDayTimes"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
  import { useCounselingTermsStore } from "~/stores/app/counseling-terms";
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const isOpenModal = ref(false);
  const state = reactive({
    isRegularHoliday: false,
    isDateRangesMode: 0,
  });

  const datePickerOptions = [
    {
      label: "単日の選択",
      value: 0,
    },
    {
      label: "日付範囲の選択",
      value: 1,
    },
  ];
  const counselingTermsStore = useCounselingTermsStore();
  const {
    specialWorkingDayTimes,
    specialWorkingDayTimesFiltered,
    currentCalendarPage,
    selectedDayWorkingTimesSpecial,
    newStartOfWorkingTimesYearMonth,
    calendarEndMonth,
    numberOfMonths,
    calendarDayClicked,
    loadings,
  } = storeToRefs(counselingTermsStore);
  const onAddSpecialWorkingTime = () => {
    isOpenModal.value = true;
  };

  const date = ref(new Date());
  const range = ref({
    start: new Date(),
    end: new Date(),
  });

  const onAddWorkingTime = () => {
    const lastWorkingTime =
      selectedDayWorkingTimesSpecial.value[
        selectedDayWorkingTimesSpecial.value.length - 1
      ];
    if (!lastWorkingTime) {
      selectedDayWorkingTimesSpecial.value = [
        {
          rangeObj: [
            {
              hours: 9,
              minutes: 0,
            },
            {
              hours: 18,
              minutes: 0,
            },
          ],
        },
      ];
      return;
    }
    selectedDayWorkingTimesSpecial.value = [
      ...selectedDayWorkingTimesSpecial.value,
      {
        rangeObj: [
          {
            hours: lastWorkingTime.rangeObj[1]?.hours + 1,
            minutes: lastWorkingTime.rangeObj[1]?.minutes,
          },
          {
            hours: lastWorkingTime.rangeObj[1]?.hours + 5,
            minutes: lastWorkingTime.rangeObj[1]?.minutes,
          },
        ],
      },
    ];
  };

  const onDeleteSpecialWorkingDayTime = async (item: any) => {
    await counselingTermsStore.removeTargetDay(item.counselingTermId);
  };

  const onEditSpecialWorkingDayTime = (item: any) => {
    isOpenModal.value = true;
    selectedDayWorkingTimesSpecial.value = item.term.map((obj: any) => ({
      ...obj,
      rangeObj: [
        parseTimeToObject(obj.startTime),
        parseTimeToObject(obj.endTime),
      ],
    }));
    state.isRegularHoliday = !item.isOpen;
    state.isDateRangesMode = 0;
    date.value = new Date(item.date);
  };
  const toast = useToast();
  const onSaveDayWorkingTimes = async () => {
    let result = false;
    loadings.value["saveSpecialWorkingDayTimes"] = true;
    if (state.isDateRangesMode) {
      newStartOfWorkingTimesYearMonth.value = {
        year: range.value.start.getFullYear(),
        month: range.value.start.getMonth() + 1,
      };
      const fromDate = range.value.start;
      const toDate = range.value.end;
      const dates = [];
      for (
        let _date = fromDate;
        _date <= toDate;
        _date.setDate(_date.getDate() + 1)
      ) {
        dates.push(formatDate(_date, "YYYY-MM-DD"));
        const workingDayTime = {
          date: formatDate(_date, "YYYY-MM-DD"),
          term: selectedDayWorkingTimesSpecial.value.map((obj) => ({
            startTime: parseTimeToString(obj.rangeObj[0]),
            endTime: parseTimeToString(obj.rangeObj[1]),
          })),
          isOpen: !state.isRegularHoliday,
        };

        const isExist = specialWorkingDayTimes.value.find(
          (obj) => obj.date === formatDate(_date, "YYYY-MM-DD"),
        );

        if (isExist) {
          specialWorkingDayTimes.value = specialWorkingDayTimes.value.map(
            (obj) =>
              obj.date === formatDate(_date, "YYYY-MM-DD")
                ? workingDayTime
                : obj,
          );
        } else {
          specialWorkingDayTimes.value.push(workingDayTime);
        }
        console.log(
          "🚀 ~ onSaveDayWorkingTimes ~ workingDayTime:",
          workingDayTime,
        );
      }
      result = await counselingTermsStore.updateTargetDay(
        currentCustomer.value.customerId as string,
        dates,
        !state.isRegularHoliday,
        selectedDayWorkingTimesSpecial.value.map((obj) => ({
          startTime: parseTimeToString(obj.rangeObj[0]),
          endTime: parseTimeToString(obj.rangeObj[1]),
        })),
      );
    } else {
      const workingDayTime = {
        date: formatDate(date.value, "YYYY-MM-DD"),
        term: selectedDayWorkingTimesSpecial.value.map((obj) => ({
          startTime: parseTimeToString(obj.rangeObj[0]),
          endTime: parseTimeToString(obj.rangeObj[1]),
        })),
        isOpen: !state.isRegularHoliday,
      };

      const isExist = specialWorkingDayTimes.value.find(
        (obj) => obj.date === formatDate(date.value, "YYYY-MM-DD"),
      );

      if (isExist) {
        specialWorkingDayTimes.value = specialWorkingDayTimes.value.map((obj) =>
          obj.date === formatDate(date.value, "YYYY-MM-DD")
            ? workingDayTime
            : obj,
        );
      } else {
        specialWorkingDayTimes.value.push(workingDayTime);
      }

      result = await counselingTermsStore.updateTargetDay(
        currentCustomer.value.customerId as string,
        [workingDayTime.date],
        workingDayTime.isOpen,
        workingDayTime.term,
      );
      newStartOfWorkingTimesYearMonth.value = {
        year: date.value.getFullYear(),
        month: date.value.getMonth() + 1,
      };
    }

    if (result) {
      isOpenModal.value = false;
      toast.add({
        title: "更新成功",
        description: "特別営業時間の設定を更新しました。",
        icon: "i-heroicons-check-circle",
      });
    }
    loadings.value["saveSpecialWorkingDayTimes"] = false;
  };

  watch(
    () => calendarDayClicked.value,
    () => {
      if (calendarDayClicked.value) {
        // check if date is in specialWorkingDayTimes
        const isExist = specialWorkingDayTimes.value.find(
          (obj) =>
            obj.date === formatDate(calendarDayClicked.value, "YYYY-MM-DD"),
        );
        if (isExist) {
          onEditSpecialWorkingDayTime(isExist);
        } else {
          isOpenModal.value = true;
          date.value = calendarDayClicked.value;
          state.isRegularHoliday = false;
          state.isDateRangesMode = 0;
          selectedDayWorkingTimesSpecial.value = [
            {
              rangeObj: [
                {
                  hours: 9,
                  minutes: 0,
                },
                {
                  hours: 18,
                  minutes: 0,
                },
              ],
            },
          ];
        }
      }
    },
  );

  watchEffect(() => {
    if (state.isRegularHoliday) {
      selectedDayWorkingTimesSpecial.value = [];
    } else if (selectedDayWorkingTimesSpecial.value.length === 0) {
      selectedDayWorkingTimesSpecial.value = [
        {
          key: 1,
          startTime: "09:00",
          endTime: "18:00",
          rangeObj: [
            {
              hours: 9,
              minutes: 0,
            },
            {
              hours: 18,
              minutes: 0,
            },
          ],
        },
      ];
    }
  });

  watch(
    () => state.isDateRangesMode,
    () => {
      if (state.isDateRangesMode) {
        range.value.start = new Date(date.value);
        range.value.end = new Date(date.value);
      } else {
        date.value = new Date(range.value.start);
      }
    },
  );

  watch(
    () => isOpenModal.value,
    () => {
      if (!isOpenModal.value) {
        state.isDateRangesMode = 0;
      }
    },
  );
</script>
