<template>
  <div class="!sticky top-0">
    <div class="flex flex-row justify-between px-0 py-1.5">
      <div>範囲</div>
      <div>
        <USelect
          size="xs"
          v-model="numberOfMonths"
          :options="viewModes"
          option-attribute="name"
        />
      </div>
    </div>
    <VCalendar
      ref="calendar"
      locale="ja"
      @did-move="onDidMove"
      :attributes="attributes"
      :rows="numberOfMonths"
      :step="1"
      :key="calendarKey"
      @dayclick="onDayClick"
    >
      <template #footer>
        <div class="w-full px-4 pb-3">
          <UButton
            size="xs"
            color="primary"
            variant="solid"
            @click="moveToday"
            block
          >
            今日 {{ formatDate(new Date(), "YYYY年MM月DD日 (dd)") }}
          </UButton>
        </div>
      </template>
    </VCalendar>
  </div>
</template>

<script setup lang="ts">
  import { useCounselingTermsStore } from "~/stores/app/counseling-terms";
  import { storeToRefs } from "pinia";
  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const calendar = ref(null);
  const counselingTermsStore = useCounselingTermsStore();
  const {
    currentCalendarPage,
    attributes,
    calendarKey,
    workingDayTimes,
    specialWorkingDayTimes,
    newStartOfWorkingTimesYearMonth,
    numberOfMonths,
    calendarDayClicked,
  } = storeToRefs(counselingTermsStore);

  const onDidMove = async (pages: any) => {
    currentCalendarPage.value = {
      year: pages[0].year,
      month: pages[0].month,
    };

    counselingTermsStore.filterWorkingDayTimesByYearMonth(
      currentCalendarPage.value.year,
      currentCalendarPage.value.month,
    );
    counselingTermsStore.updateAttribute();
  };

  function moveToday() {
    calendar.value?.move(new Date());
  }

  watch(
    () => numberOfMonths.value,
    () => {
      calendarKey.value = calendarKey.value + 1;
      counselingTermsStore.updateAttribute();
      counselingTermsStore.filterWorkingDayTimesByYearMonth(
        currentCalendarPage.value.year,
        currentCalendarPage.value.month,
      );
    },
  );

  watch(
    () => specialWorkingDayTimes.value,
    () => {
      calendarKey.value = calendarKey.value + 1;
      counselingTermsStore.updateAttribute();
      counselingTermsStore.filterWorkingDayTimesByYearMonth(
        currentCalendarPage.value.year,
        currentCalendarPage.value.month,
      );
    },
    { immediate: true, deep: true },
  );

  watch(
    () => workingDayTimes.value,
    () => {
      calendarKey.value = calendarKey.value + 1;
      counselingTermsStore.updateAttribute();
    },
    { immediate: true, deep: true },
  );

  watch(
    () => newStartOfWorkingTimesYearMonth.value,
    async (show) => {
      await calendar.value?.move(show);
      calendarKey.value = calendarKey.value + 1;
    },
    { immediate: true, deep: true },
  );

  const viewModes = [
    {
      name: "1ヶ月",
      value: 1,
    },
    {
      name: "2ヶ月",
      value: 2,
    },
    {
      name: "3ヶ月",
      value: 3,
    },
    {
      name: "4ヶ月",
      value: 4,
    },
    {
      name: "5ヶ月",
      value: 5,
    },
    {
      name: "6ヶ月",
      value: 6,
    },
    {
      name: "7ヶ月",
      value: 7,
    },
    {
      name: "8ヶ月",
      value: 8,
    },
    {
      name: "9ヶ月",
      value: 9,
    },
    {
      name: "10ヶ月",
      value: 10,
    },
    {
      name: "11ヶ月",
      value: 11,
    },
    {
      name: "12ヶ月",
      value: 12,
    },
  ];

  const onDayClick = (calendarDay: any) => {
    if (props.disabled) return;
    calendarDayClicked.value = calendarDay?.date;
  };
</script>
