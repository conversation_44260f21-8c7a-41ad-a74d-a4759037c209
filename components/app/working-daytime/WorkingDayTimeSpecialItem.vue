<template>
  <div
    class="group/wrap flex flex-col bg-gray-50 dark:bg-gray-800 dark:border-gray-600 hover:bg-gray-100 border-b hover:border-none hover:shadow-md hover:scale-100 transition-all duration-200 cursor-pointer"
  >
    <div class="flex py-2 px-3 flex-row space-x-1 items-center justify-between">
      <div
        @click="onEdit(item)"
        class="text-sm group-hover/wrap:text-primary-500 group-hover/wrap:font-bold hover:underline cursor-pointer"
        :class="[
          `text-${getDayColor(formatDate(new Date(item.date), 'dd'))}-500`,
        ]"
      >
        {{ formatDate(new Date(item.date), "YYYY年MM月DD日 (dd)") }}
      </div>
      <div class="flex space-x-1 items-center">
        <div
          class="flex flex-row flex-wrap gap-1 items-center max-w-[300px] whitespace-nowrap"
        >
          <div v-if="item.term?.length > 0" v-for="term in item.term">
            <UBadge
              color="green"
              variant="solid"
              :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
            >
              {{ `${term.startTime} ~ ${term.endTime}` }}
            </UBadge>
          </div>
        </div>

        <div v-if="!item.isOpen">
          <UBadge
            color="red"
            variant="solid"
            :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
          >
            特別休業日
          </UBadge>
        </div>
        <div
          v-if="!disabled"
          class="hidden group-hover/wrap:inline-flex space-x-1 pl-2"
        >
          <UButton
            size="sm"
            color="red"
            variant="soft"
            icon="i-heroicons-trash"
            :key="item.date"
            v-confirm="{
              title: '特別営業時間の削除',
              message: `「${formatDate(
                new Date(item.date),
                'YYYY年MM月DD日 (dd)',
              )}」の設定を削除してもよろしいですか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: () => {
                $emit('delete', item);
              },
            }"
          />
          <!-- <UButton
            size="sm"
            color="primary"
            variant="soft"
            icon="i-heroicons-pencil-square"
            @click="onEdit(item)"
          /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    item: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(["delete", "edit"]);
  const onEdit = (item: any) => {
    if (!props.disabled) {
      emits("edit", item);
    }
  };
</script>
