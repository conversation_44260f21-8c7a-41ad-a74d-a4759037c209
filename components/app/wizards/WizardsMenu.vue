<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppUIStore } from "~/stores/app/ui";
const appUIStore = useAppUIStore();
const { isSubNavigationMini } = storeToRefs(appUIStore);
const links = [
  {
    name: "app-wizards",
    icon: "i-ri-survey-line",
    label: "ウィザード結果",
    to: "/app/wizards",
    exact: true,
  },
  // {
  //   name: "app-wizards-list",
  //   icon: "i-fluent-form-24-regular",
  //   label: "アンケート一覧",
  //   to: "/app/wizards/list",
  //   disabled: true,
  // },
];

const menuList = computed(() => {
  return links.map((link) => {
    const menu = { ...link };
    if (isSubNavigationMini.value) {
      delete menu.label;
    }
    return menu;
  });
});

const appWizardsStore = useAppWizardsStore();
const isLocalHost = process.env.NODE_ENV === "development";
const onUpdateWizard = async () => {
  const wizardStore = useWizardStore();
  const wizard = await wizardStore.fetchWizard("1850d091-2d39-4f68-bb58-bd7650f3d766");
  appWizardsStore.updateWizard("1850d091-2d39-4f68-bb58-bd7650f3d766", {
    ...wizard,
    // wizardEmailList: [
    //   {
    //     email: "<EMAIL>",
    //     status: "verified"
    //   }
    // ],
    wizard: [
      {
        defaultValue: [],
        values: [],
        description: "安心してご相談ください<br/>ひとりで悩まないで！",
        step: 1,
        id: "titleAndDescriptionTop",
        _id: "titleAndDescriptionTop",
        title: "24時間相談申込受付できます",
        img: "/images/hamamatsu-city-top.jpg",
        type: "titleAndDescription",
        value: [],
        required: false,
      },
      {
        step: 1,
        buttons: [
          {
            icon: "i-entypo-chat",
            next: {
              action: "link",
              value: "web-app",
              mailTempalteId: "1baaf813-83bf-40fc-a1d0-c55c57e35ea4",
            },
            text: "今すぐチャットで相談",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            next: {
              action: "step",
              value: 2,
            },
            text: "相談を申し込む",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup1",
        _id: "buttonGroup1",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            value: "家族",
            img: "/images/family.png",
          },
          {
            value: "友達",
            img: "/images/friend.png",
          },
          {
            value: "勉強",
            img: "/images/study.png",
          },
          {
            value: "いじめ",
            img: "/images/泣いている.png",
          },
          {
            value: "学校を休んでいる",
            img: "/images/体育すわり.png",
          },
          {
            value: "身体",
            img: "/images/health.png",
          },
          {
            value: "その他",
          },
        ],
        step: 2,
        hasOtherOption: true,
        id: "worryReason",
        valueClass: "max-w-sm p-4 lg:w-1/4 sm:w-1/2",
        _id: "worryReason",
        title: "何に悩んでいますか？",
        type: "selectSingle",
        value: [],
        required: true,
      },
      {
        step: 2,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup2",
        _id: "buttonGroup2",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            value: "小学校1年",
          },
          {
            value: "小学校2年",
          },
          {
            value: "小学校3年",
          },
          {
            value: "小学校4年",
          },
          {
            value: "小学校5年",
          },
          {
            value: "小学校6年",
          },
          {
            value: "中学校1年",
          },
          {
            value: "中学校2年",
          },
          {
            value: "中学校3年",
          },
        ],
        step: 3,
        hasOtherOption: true,
        id: "grade",
        valueClass: "max-w-lg p-4 lg:w-1/2 sm:w-1/2",
        _id: "grade",
        title: "学年を教えて下さい",
        type: "selectPulldown",
        value: [],
        required: false,
      },
      {
        defaultValue: [],
        values: [],
        step: 3,
        hasOtherOption: true,
        id: "name",
        valueClass: "max-w-lg p-4 lg:w-1/2 sm:w-1/2",
        _id: "name",
        title: "名前を教えて下さい",
        type: "text",
        required: false,
      },
      {
        step: 3,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            text: "答えたくない(匿名)",
            trailing: true,
            color: "orange",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup3",
        _id: "buttonGroup3",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            next: {
              action: "step",
              value: 6,
            },
            value: "知ってもらうだけでよい",
            img: "/images/just-talk.png",
          },
          {
            next: {
              action: "step",
              value: 5,
            },
            value: "だれかに相談したい",
            img: "/images/want-chat.png",
          },
        ],
        step: 4,
        hasOtherOption: true,
        id: "consultationType",
        valueClass: "max-w-lg p-4 lg:w-1/2 sm:w-1/2",
        _id: "consultationType",
        title: "この相談をどのようにしたいですか？",
        type: "selectSingle",
        value: [],
        required: true,
      },
      {
        step: 4,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "butonGroup4",
        _id: "butonGroup4",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            value: "担任",
          },
          {
            value: "担任以外",
          },
          {
            value: "SC",
          },
          {
            next: {
              action: "step",
              value: 8,
            },
            value: "チャットで相談",
          },
        ],
        step: 5,
        hasOtherOption: true,
        id: "whoWantToConsult",
        valueClass: "max-w-md p-4 lg:w-1/2 sm:w-1/2 w-full",
        _id: "whoWantToConsult",
        title: "誰に相談したいですか？",
        type: "selectSingle",
        value: [],
        required: true,
      },
      {
        step: 5,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup5",
        _id: "buttonGroup5",
        type: "buttons",
      },
      {
        defaultValue: [],
        description: "※自由記述（未回答可）",
        step: 6,
        hasOtherOption: true,
        id: "kindOfConsultation",
        _id: "kindOfConsultation",
        title: "どのような内容ですか？",
        type: "textArea",
        required: false,
      },
      {
        step: 6,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup6",
        _id: "buttonGroup6",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            next: {
              action: "step",
              value: 9,
            },
            value: "送信をやめる",
            img: "/images/中止.png",
          },
          {
            next: {
              action: "send-mail",
              value: 10,
              mailTempalteId: "1baaf813-83bf-40fc-a1d0-c55c57e35ea4",
            },
            value: "送信する",
            img: "/images/送信.png",
          },
        ],
        step: 7,
        hasOtherOption: true,
        id: "confirmSend",
        valueClass: "max-w-lg p-4 lg:w-1/2 sm:w-1/2",
        _id: "confirmSend",
        title: "学校へ送信しますか？",
        type: "selectSingle",
        value: [],
        required: true,
      },
      {
        step: 7,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup7",
        _id: "buttonGroup7",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [
          {
            next: {
              action: "step",
              value: 9,
            },
            value: "しない",
            img: "/images/no-chat.png",
          },
          {
            next: {
              action: "submit",
            },
            value: "する",
            img: "/images/want-chat.png",
          },
        ],
        step: 8,
        hasOtherOption: true,
        id: "confirmChat",
        valueClass: "max-w-lg p-4 lg:w-1/2 sm:w-1/2",
        _id: "confirmChat",
        title: "チャット相談をしますか?",
        type: "selectSingle",
        value: [],
        required: true,
      },
      {
        step: 8,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            isNext: true,
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup8",
        _id: "buttonGroup8",
        type: "buttons",
      },
      {
        defaultValue: [],
        values: [],
        description: "相談窓口の紹介の内容",
        step: 9,
        hasOtherOption: true,
        id: "introductionOfConsultation",
        _id: "introductionOfConsultation",
        title: "相談窓口の紹介",
        type: "titleAndDescription",
        value: [],
        required: false,
      },
      {
        step: 9,
        buttons: [
          {
            icon: "i-heroicons-arrow-left",
            isPrev: true,
            text: "前へ",
            trailing: false,
            color: "gray",
          },
          {
            icon: "i-heroicons-arrow-right",
            next: {
              action: "send-mail",
              value: 10,
              mailTempalteId: "1baaf813-83bf-40fc-a1d0-c55c57e35ea4",
            },
            text: "次へ",
            trailing: true,
            color: "primary",
          },
        ],
        id: "buttonGroup9",
        _id: "buttonGroup9",
        type: "buttons",
      },
      {
        img: "/images/thankyou.png",
        defaultValue: [],
        values: [],
        description: "相談申込を受け付けました",
        step: 10,
        hasOtherOption: true,
        id: "end",
        _id: "end",
        title: "",
        type: "titleAndDescription",
        value: [],
        required: false,
      },
      {
        defaultValue: [],
        values: [],
        description: "学校名",
        step: 0,
        id: "schoolId",
        _id: "schoolId",
        title: "学校名",
        type: "text",
        required: false,
      },
    ],
  });
};
</script>

<template>
  <UVerticalNavigation :links="menuList" :ui="{ size: 'text-sm' }" />
  <UButton
    v-if="isLocalHost"
    class="mt-4 ml-0"
    size="sm"
    block
    variant="soft"
    @click="onUpdateWizard"
    >Update wizard</UButton
  >
</template>
