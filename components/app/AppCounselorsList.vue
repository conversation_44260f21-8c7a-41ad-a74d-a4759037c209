<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/app/counselors";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const counselorsStore = useCounselorsStore();
  const { loadings, counselorsForNavigation, showDetailModal, counselors } =
    storeToRefs(counselorsStore);
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } =
    storeToRefs(appCustomersStore);

  const consultantsForLoading = Array.from({ length: 10 }, (_, i) => ({
    label: "",
  }));

  const onDetail = () => {
    showDetailModal.value = true;
  };
</script>

<template>
  <div class="flex flex-row justify-between items-center">
    <div v-if="!isSubNavigationMini" class="font-semibold text-sm px-3">
      {{ $t("Counselor list") }}
    </div>
    <UButton
      icon="i-tabler-refresh"
      size="xs"
      color="gray"
      square
      variant="ghost"
      :ui="{
        rounded: 'rounded-full',
      }"
      @click="counselorsStore.fetchCounselors(currentCustomer.customerId as string)"
      :class="{
        'mx-auto': isSubNavigationMini,
      }"
    />
  </div>
  <UVerticalNavigation
    v-if="loadings['fetchCounselors']"
    :links="consultantsForLoading"
    :ui="{ size: 'text-sm' }"
  >
    <template #avatar>
      <USkeleton
        class="h-6 w-6"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-800',
        }"
      />
    </template>
    <template #default>
      <USkeleton
        v-if="!isSubNavigationMini"
        class="h-4 w-2/3"
        :ui="{
          background: 'bg-gray-200 dark:bg-gray-800',
        }"
      />
    </template>
  </UVerticalNavigation>
  <UVerticalNavigation
    v-else
    :links="counselorsForNavigation"
    :ui="{ size: 'text-sm' }"
  >
    <template #avatar="{ link }">
      <UTooltip
        :text="link.fullName"
        :popper="{ arrow: true, placement: 'right' }"
        :prevent="!isSubNavigationMini"
      >
        <UAvatar
          v-bind="link.avatar"
          size="xs"
          loading="lazy"
          :ui="{
            rounded: 'rounded-lg',
            background: 'bg-gray-300 dark:bg-gray-400',
            placeholder:
              'text-xs font-semibold text-gray-700 dark:text-gray-800',
            chip: {
              size: {
                xs: 'h-2 w-2',
              },
            },
          }"
        />
      </UTooltip>
    </template>
  </UVerticalNavigation>
  <div
    v-if="!loadings['fetchCounselors'] && !counselors.length"
    class="pt-10 flex flex-col justify-center items-center"
  >
    <UIcon
      name="i-solar-users-group-two-rounded-bold-duotone"
      class="text-5xl text-gray-400"
    />
    <div class="text-center mt-3">
      <div class="text-gray-400 text-sm">カウンセラーが見つかりません</div>
      <div class="text-gray-400 text-sm">管理者に連絡してください</div>
    </div>
  </div>
</template>
