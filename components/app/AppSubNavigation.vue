<template>
  <aside
    class="fixed pt-1 border-r dark:border-gray-700 flex flex-col top-0 z-20 h-screen bg-white dark:bg-gray-900 transition-all duration-200"
    :class="{
      'w-16': isSubNavigationMini,
      'w-60 ': !isSubNavigationMini,
      'left-20': isHideAppNavigation,
      'left-44': !isHideAppNavigation,
      'left-[7.2rem] shadow-[rgba(0,0,15,0.1)_2px_5px_5px_0px]':
        isHideAppSubNavigation && !isHideAppNavigation,
      'left-5 shadow-[rgba(0,0,15,0.1)_2px_5px_5px_0px]':
        isHideAppSubNavigation && isHideAppNavigation,
    }"
  >
    <div
      v-if="!isSubNavigationMini"
      class="py-1 px-2 border-b dark:border-gray-700 font-semibold text-md items-center flex flex-inline justify-between"
    >
      <div class="truncate flex flex-row items-center space-x-1">
        <UTooltip
          :text="$t(getCustomerRole(currentCustomer?.customerId as string) || '')"
          :popper="{ placement: 'right' }"
        >
          <Icon
            class="text-xl"
            :icon="getCounselortRoleIcon(getCustomerRole(currentCustomer?.customerId as string) as string)"
          />
        </UTooltip>

        <div class="truncate">
          {{ currentCustomer?.basic?.customerName }}
        </div>
      </div>
      <UButton
        icon="i-heroicons-arrow-small-left"
        size="sm"
        color="gray"
        square
        variant="ghost"
        @click="isSubNavigationMini = !isSubNavigationMini"
      />
    </div>
    <div v-else class="text-center py-1 border-b dark:border-gray-700">
      <UButton
        icon="i-heroicons-bars-3"
        size="sm"
        color="gray"
        square
        variant="ghost"
        @click="isSubNavigationMini = !isSubNavigationMini"
      />
    </div>
    <div class="overflow-auto hidden-scrollbar overflow-x-hidden">
      <slot />
    </div>
  </aside>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { Icon } from "@iconify/vue";

  const authStore = useAuthStore();
  const { getCustomerRole } = storeToRefs(authStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini, isHideAppNavigation, isHideAppSubNavigation } =
    storeToRefs(appUIStore);

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
</script>
