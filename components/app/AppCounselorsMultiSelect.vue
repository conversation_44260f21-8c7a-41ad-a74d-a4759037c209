<template>
  <USelectMenu
    :options="counselorsForPalette"
    v-model="selectedCounselors"
    value-attribute="id"
    option-attribute="label"
    placeholder="選択してください"
    multiple
  >
    <template #label>
      <template v-if="selectedCounselorsObject.length">
        <div>
          <div class="flex items-center -space-x-2">
            <UAvatar
              v-for="counselor of selectedCounselorsObject"
              size="2xs"
              :src="counselor.profileImage"
              :alt="counselor.fullName"
              :ui="{
                wrapper: 'border',
                rounded: 'rounded-md',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
              }"
              class="hover:scale-125 transition-transform duration-200 hover:shadow-md hover:z-20"
            />
          </div>
        </div>
        <span class="truncate">{{ selectedCounselorsName }}</span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          選択してください
        </span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/app/counselors";
  const counselorsStore = useCounselorsStore();
  const { counselorsForPalette } = storeToRefs(counselorsStore);
  const selected = ref([]);
  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: [],
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const selectedCounselors = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      console.log(val);
      emits("update:modelValue", val);
    },
  });
  const selectedCounselorsObject = computed(() => {
    return counselorsForPalette.value?.filter((counselor) =>
      selectedCounselors.value.includes(counselor.counselorId),
    );
  });

  const selectedCounselorsName = computed(() => {
    return counselorsForPalette.value
      ?.filter((counselor) =>
        selectedCounselors.value.includes(counselor.counselorId),
      )
      .map((counselor) => counselor.fullName)
      .join(", ");
  });
</script>
