<template>
  <UModal
    v-model="showDetailModal"
    :ui="{
      width: 'sm:max-w-5xl',
    }"
    prevent-close
  >
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3
            class="flex flex-row items-center space-x-2 text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            <UAvatar
              v-bind="{
                src: detailCounselor.profileImage,
                alt: detailCounselor.fullName,
              }"
              size="md"
              loading="lazy"
              :ui="{
                rounded: 'rounded-lg',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
                chip: {
                  size: {
                    xs: 'h-2 w-2',
                  },
                },
              }"
            />
            <div class="flex flex-col">
              <div class="flex flex-row items-center space-x-2">
                <div>
                  {{ detailCounselor.fullName }}
                </div>
                <UBadge
                  size="xs"
                  variant="soft"
                  :color="getCounselorRoleColor(detailCounselor.role)"
                  :ui="{
                    rounded: 'rounded-md',
                    size: {
                      xs: 'px-4',
                    },
                  }"
                >
                  {{ $t(detailCounselor.role) }}
                </UBadge>
              </div>
              <div class="text-xs font-light">
                {{ detailCounselor.email }}
              </div>
            </div>
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="showDetailModal = false"
          />
        </div>
      </template>
      <div class="flex flex-col space-y-4">
        <div>
          <div class="flex flex-row space-x-4">
            <UFormGroup label="組織名" class="w-full">
              <UInput
                icon="i-octicon-organization-24"
                size="md"
                :ui="{ icon: { trailing: { pointer: '' } } }"
                v-model="detailCounselor.organizationName"
                disabled
                color="gray"
              >
              </UInput>
            </UFormGroup>
          </div>
        </div>
        <UFormGroup label="対応中のケース一覧" class="w-full">
          <div class="">
            <div
              v-if="loadings['fetchCounselor']"
              class="flex flex-col p-6 justify-center items-center space-y-4"
            >
              <Icon icon="eos-icons:loading" class="text-5xl text-gray-500" />
              <div class="text-sm text-gray-500">データを読み込んでいます</div>
            </div>
            <UTabs
              v-else
              orientation="vertical"
              v-model="detailCounselor.selectedCustomer"
              :items="detailCounselor?.customers"
              :ui="{
                wrapper: 'flex items-start gap-4 space-y-0',
                list: {
                  base: 'border',
                  width: 'w-1/3',
                  tab: {
                    size: 'text-xs',
                    base: 'justify-start',
                  },
                },
              }"
            >
              <template #default="{ item, index, selected }">
                <div class="flex flex-row justify-between w-full items-center">
                  <div class="flex items-center gap-2 relative truncate">
                    <UAvatar
                      :ui="{
                        rounded: 'rounded-md',
                        background: 'bg-gray-300 dark:bg-gray-400',
                        placeholder:
                          'text-md font-semibold text-gray-700 dark:text-gray-800',
                      }"
                      :src="item.basic?.customerImage"
                      :alt="item.basic?.customerName"
                      size="xs"
                    />
                    <span class="truncate">{{ item.basic?.customerName }}</span>
                  </div>
                  <UBadge
                    v-if="numberOfCasesByCustomer(item?.customerId)"
                    size="xs"
                    color="gray"
                    variant="solid"
                  >
                    {{ numberOfCasesByCustomer(item?.customerId) }}</UBadge
                  >
                </div>
              </template>
              <template #item="{ item }">
                <!-- Table -->
                <div class="">
                  <UTable
                    :rows="casesOfDetailCounselorByCustomer"
                    :columns="columns"
                    sort-asc-icon="i-heroicons-arrow-up"
                    sort-desc-icon="i-heroicons-arrow-down"
                    :ui="{
                      wrapper: 'overflow-hidden rounded-lg border',
                      tr: {
                        base: 'group hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer',
                      },
                      td: { base: 'truncate' },
                      th: {
                        size: 'text-xs',
                        padding: 'px-0 px-2',
                      },
                    }"
                  >
                    <template #status-data="{ row }">
                      <UButton
                        size="2xs"
                        block
                        :color="getCaseStatusColor(row.status)"
                        trailing-icon="i-heroicons-chevron-down-20-solid"
                      >
                        {{ $t(`caseStatus.${row.status}`) }}
                      </UButton>
                    </template>
                    <template #elapsedTimeBeforeStart-data="{ row }">
                      <div>{{ row.elapsedTimeBeforeStart }}</div>
                    </template>
                    <template #elapsedTimeInProgress-data="{ row }">
                      <div>{{ row.elapsedTimeInProgress }}</div>
                    </template>
                    <template #latestPostTime-data="{ row }">
                      <div>{{ fromNow(new Date(row.latestPostTime)) }}</div>
                    </template>
                    <template #clientName-data="{ row }">
                      <div class="flex items-center space-x-2">
                        <div class="border-r pr-2">
                          <component
                            :is="getSNSIconComponent(row.channel)"
                            class="h-5 w-5"
                          />
                        </div>
                        <UAvatar
                          size="xs"
                          :src="row.counseleeImage"
                          :alt="row.counseleeName"
                        />
                        <div>
                          <ULink
                            inactive-class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                          >
                            {{ row.counseleeName }}
                          </ULink>
                        </div>
                      </div>
                    </template>
                    <template #count-data="{ row }">
                      <div>{{ row.count }} 回目</div>
                    </template>
                    <template #risk-data="{ row }">
                      <div
                        class="flex justify-center items-center"
                        :class="`text-${
                          getCasePriorityColorIcon(row.risk).color
                        }-500`"
                      >
                        {{ $t(`casePriority.${row.risk}`) }}
                      </div>
                    </template>
                    <template #preview-data="{ row }">
                      <UButton
                        size="2xs"
                        block
                        variant="soft"
                        @click="onChatWithUser(row)"
                      >
                        相談する
                      </UButton> </template
                    >Ï
                    <template #empty-state>
                      <div
                        class="flex flex-col items-center justify-center py-6 gap-3"
                      >
                        <Icon
                          icon="solar:case-round-bold-duotone"
                          class="text-gray-400 text-3xl"
                        />
                        <span class="text-sm text-gray-400">
                          対応中のケースはありません
                        </span>
                      </div>
                    </template>
                  </UTable>
                </div>
              </template>
            </UTabs>
          </div>
        </UFormGroup>
      </div>
      <!-- <template #footer>
        <div class="flex justify-end">
          <UButton
            color="white"
            label="閉じる"
            type="submit"
            class="px-10"
            @click="showDetailModal = false"
          />
        </div>
      </template> -->
    </UCard>
  </UModal>
</template>

<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/app/counselors";
  import type { TableColumn, Case } from "~/types";
  import { Icon } from "@iconify/vue";
  import { useCasesStore } from "~/stores/app/cases";
  const toast = useToast();
  const casesStore = useCasesStore();
  const counselorsStore = useCounselorsStore();
  const { isOpenCaseDetail, caseDetail } = storeToRefs(casesStore);
  const {
    loadings,
    showDetailModal,
    detailCounselor,
    casesOfDetailCounselorByCustomer,
    numberOfCasesByCustomer,
  } = storeToRefs(counselorsStore);
  const { t } = useI18n();

  watch(
    () => showDetailModal.value,
    (value) => {
      if (value) {
        counselorsStore.fetchCounselor(detailCounselor.value?.counselorId);
      }
    },
  );

  const columns: TableColumn[] = [
    {
      key: "clientName",
      label: t("Counselee"),
      sortable: true,
      class: "text-left",
    },
    {
      key: "count",
      label: t("Times"),
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "risk",
      label: t("Priority"),
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "elapsedTimeInProgress",
      label: t("In progress - Elapse time"),
      sortable: true,
      class: "text-center w-24",
    },
    {
      key: "latestPostTime",
      label: t("Latest message"),
      sortable: true,
      class: "text-left",
    },
    {
      key: "preview",
      label: t("#"),
      class: "text-center w-0",
    },
  ];

  const onChatWithUser = (row: Case) => {
    // showDetailModal.value = false;
    // navigateTo(`/app/chats?caseId=${row.caseId}`);
    caseDetail.value = row;
    isOpenCaseDetail.value = true;
  };
</script>
