<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import type { TableColumn, App<PERSON>ou<PERSON>lor, Case } from "~/types";
  import { useCasesStore } from "~/stores/app/cases";
  import { CaseStatus } from "~/types/enums.d";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { Icon } from "@iconify/vue";
  import { useAuthStore } from "~/stores/auth";
  const userPermissions = usePermissions();
  const toast = useToast();
  const casesStore = useCasesStore();
  const isOpenCounselorsPalette = ref(false);
  const appCustomersStore = useAppCustomersStore();
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
  const { currentCustomer } = storeToRefs(appCustomersStore);
  let autoIntervals: any[] = [];
  const {
    cases,
    loadings,
    pagination,
    pageFrom,
    pageTo,
    caseDetail,
    updateAssigneeLoading,
    totalCasesCount,
    sortConditions,
    searchConditions,
    isOpenCaseDetail
  } = storeToRefs(casesStore);
  const { t } = useI18n();
  const columns: TableColumn[] = [
    {
      key: "createdAt",
      label: t("Registration order"),
      sortable: true,
      class: "text-center w-24",
    },
    {
      key: "status",
      label: t("Status"),
      sortable: true,
      class: "text-center",
    },
    {
      key: "assigneeName",
      label: t("Assignee"),
      sortable: true,
    },
    {
      key: "elapsedTimeBeforeStart",
      label: t("Unsupport - Elapse time"),
      sortable: true,
      class: "text-center min-w-24",
    },
    {
      key: "elapsedTimeInProgress",
      label: t("In progress - Elapse time"),
      sortable: true,
      class: "text-center w-24",
    },
    {
      key: "latestPostTime",
      label: t("Latest message"),
      sortable: true,
    },
    {
      key: "clientName",
      label: t("Counselee"),
      sortable: true,
      class: "text-center",
    },
    // {
    //   key: "userId",
    //   label: t("Counselee ID"),
    //   sortable: true,
    //   class: "text-center w-fit",
    // },
    {
      key: "count",
      label: t("Times"),
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "risk",
      label: t("Priority"),
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "preview",
      label: t("#"),
      class: "text-center w-0",
    },
  ];

  const selectedColumns = ref(columns);
  const columnsTable = computed(() =>
    columns.filter((column) => selectedColumns.value.includes(column)),
  );

  const onChangeCounselor = (_case: Case) => {
    const hasPermission = hasPermissionToChangeCounselorInCharge(_case);
    if (hasPermission) {
      caseDetail.value = _case;
      isOpenCounselorsPalette.value = true;
      nextStatus.value = caseDetail.value.status;
    }
  };

  const hasPermissionToChangeCounselorInCharge = (_case: Case) => {
    const isMyOwnCase = _case.counselorInChargeId === user.value?.counselorId;
    const hasPermission =
      (isMyOwnCase &&
        userPermissions.value.includes(
          "update:case-counselor-i-am-in-charge",
        )) ||
      (!isMyOwnCase &&
        userPermissions.value.includes(
          "update:case-counselor-i-am-not-in-charge",
        ));

    return hasPermission;
  };

  const onSelectCounselor = async (counselor: AppCounselor) => {
    isOpenCounselorsPalette.value = false;
    let result;

    if (caseDetail.value.status !== nextStatus.value) {
      result = await casesStore.updateCounselorAndStatus(
        caseDetail.value,
        counselor,
        nextStatus.value,
      );
    } else {
      result = await casesStore.updateCounselorInCharge(
        caseDetail.value,
        counselor,
      );
    }

    if (result) {
      toast.add({
        title: t("Success"),
        description: "データを更新しました。",
        icon: "i-heroicons-check-badge",
      });
    } else {
      toast.add({
        title: "処理失敗",
        description: "データの更新ができませんでした。",
        color: "red",
        icon: "i-heroicons-exclamation-circle",
      });
    }
  };

  const nextStatus = ref();

  const onOpenCaseDetail = (_case: Case) => {
    isOpenCaseDetail.value = true;
    caseDetail.value = _case;
  };

  // Filters
  const caseStatus = Object.values(CaseStatus).map((status) => ({
    label: t(`caseStatus.${status}`),
    value: status,
  }));

  const selectedStatus = computed(() => {
    return caseStatus.filter((status) =>
      searchConditions.value.caseStatus?.includes(status.value),
    );
  });

  const resetFilters = () => {
    casesStore.resetSearchConditions();
    casesStore.fetchCases(
      currentCustomer.value.customerId as string,
      "resetFilters",
    );
  };

  const onChatWithUser = (row: Case) => {
    navigateTo(`/app/chats?caseId=${row.caseId}`);
  };

  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const onFilterByStatus = (status: CaseStatus[]) => {
    searchConditions.value.caseStatus = status;
    casesStore.fetchCases(
      currentCustomer.value.customerId as string,
      "onFilterByStatus",
    );
  };

  const coppiedCases = ref({} as { [key: string]: boolean });
  const onCopyCaseId = (row: any) => {
    navigator.clipboard.writeText(row.caseNo);

    coppiedCases.value[row.caseNo] = true;
    setTimeout(() => {
      coppiedCases.value[row.caseNo] = false;
    }, 1000);
  };
  const runtimeConfig = useRuntimeConfig();
  // auto refresh every 20 seconds
  onMounted(() => {
    autoIntervals.push(
      setInterval(() => {
        casesStore.fetchCases(
          currentCustomer.value.customerId as string,
          false,
        );
      }, runtimeConfig.public.autoRefreshs.cases),
    );
  });

  onBeforeUnmount(() => {
    autoIntervals.forEach((interval) => {
      clearInterval(interval);
    });
  });
</script>

<template>
  <UCard
    id="dashboard-table"
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: 'px-4 py-5' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700',
      },
      footer: { padding: 'p-4' },
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="font-semibold text-xl text-gray-900 dark:text-white leading-tight"
        >
          ケース一覧
        </h2>
        <USelectMenu
          :modelValue="searchConditions.caseStatus"
          :options="caseStatus"
          @change="onFilterByStatus"
          multiple
          value-attribute="value"
          option-attribute="label"
          placeholder="状態"
          class="w-44"
        >
          <template #label>
            <template v-if="selectedStatus.length">
              <span class="flex items-center -space-x-1">
                <span
                  v-for="label of selectedStatus"
                  :key="label.value"
                  class="flex-shrink-0 w-3 h-3 mt-px rounded-full"
                  :class="`bg-${getCaseStatusColor(label.value)}-500`"
                />
              </span>
              <span>{{ selectedStatus.length }}つの状態</span>
            </template>
            <template v-else>
              <span class="text-gray-500 dark:text-gray-400 truncate">
                {{ $t("Select status") }}
              </span>
            </template>
          </template>
          <template #option="{ option: status }">
            <span
              :class="[
                `bg-${getCaseStatusColor(status.value)}-500`,
                'inline-block h-3 w-3 flex-shrink-0 rounded-full',
              ]"
              aria-hidden="true"
            />
            <span class="truncate">{{ status.label }}</span>
          </template>
        </USelectMenu>
      </div>
    </template>

    <!-- Filters -->

    <!-- Header and Action buttons -->
    <div class="flex justify-between items-center w-full px-6 py-3">
      <div class="flex items-center gap-1.5">
        <span class="text-sm leading-5">1ページあたりの表示数</span>

        <USelect
          v-model="pagination.pageRangeDisplayed"
          :options="[3, 5, 10, 20, 30, 40]"
          class="me-2 w-20"
          size="xs"
        />
      </div>

      <div class="flex gap-1.5 items-center">
        <USelectMenu v-model="selectedColumns" :options="columns" multiple>
          <UButton
            class="w-40"
            icon="i-heroicons-view-columns"
            color="gray"
            size="xs"
          >
            表示項目
          </UButton>
        </USelectMenu>

        <UButton
          icon="i-heroicons-funnel"
          color="gray"
          size="xs"
          @click="resetFilters"
        >
          リセット
        </UButton>
        <UButton
          size="xs"
          variant="solid"
          icon="i-heroicons-arrow-path-solid"
          color="gray"
          @click="casesStore.fetchCases(currentCustomer.customerId as string)"
        />
      </div>
    </div>
    <!-- Table -->
    <UTable
      :rows="cases"
      :columns="columnsTable"
      :loading="loadings['fetchCases']"
      v-model:sort="sort"
      sort-mode="manual"
      sort-asc-icon="i-heroicons-arrow-up"
      sort-desc-icon="i-heroicons-arrow-down"
      class="w-full"
      :ui="{
        tr: {
          base: 'group hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer',
        },
        td: { base: 'truncate' },
        th: {
          size: 'text-xs',
          padding: 'px-0 pl-2  pt-4',
        },
      }"
    >
      <template #createdAt-data="{ row }">
        <div class="flex flex-row space-x-2">
          <UTooltip
            :text="row.caseNo"
            :popper="{ placement: 'top' }"
            :ui="{ base: 'h-10', container: 'z-50' }"
          >
            <template #text>
              <div>
                <div class="text-center">{{ row.caseNo }}</div>
                <div class="text-xs text-gray-500 text-center">
                  (ボタンをクリックしてケースIDがコピーされます)
                </div>
              </div>
            </template>
            <UButton
              size="2xs"
              variant="outline"
              class="w-12 justify-center"
              color="black"
              @click="onCopyCaseId(row)"
              :ui="{
                padding: {
                  '2xs': '!py-0',
                },
              }"
            >
              <UIcon
                name="i-lets-icons-check-fill"
                v-if="coppiedCases[row.caseNo]"
                class="text-green-500 text-xl"
              />
              <span v-else> #No. </span>
            </UButton>
          </UTooltip>
          <UButton
            size="2xs"
            class="px-4"
            variant="outline"
            @click="onOpenCaseDetail(row)"
          >
            表示
          </UButton>
        </div>
      </template>
      <template #status-data="{ row }">
        <CaseStatusChange :case="row" />
      </template>
      <template #assigneeName-data="{ row }">
        <div>
          <Icon
            v-if="updateAssigneeLoading[row.caseId]"
            icon="eos-icons:loading"
            class="text-xl w-14 text-primary-500"
          />
          <div v-else-if="row.counselorInChargeName" class="group/assigneeName">
            <div
              @click="onChangeCounselor(row)"
              class="flex items-center space-x-2 group-hover/assigneeName:hover:scale-110 transition-all duration-200"
            >
              <UAvatar
                size="xs"
                :src="row.counselorInChargeImage"
                :alt="row.counselorInChargeName"
                :ui="{
                  rounded: 'rounded-md',
                  background: 'bg-gray-300 dark:bg-gray-400',
                  placeholder:
                    'text-xs font-semibold text-gray-700 dark:text-gray-800',
                }"
              />
              <div class="flex flex-col -space-y-1">
                <ULink
                  :inactive-class="'text-primary-500 hover:text-primary-700 dark:text-primary-300 dark:hover:text-primary-500'"
                >
                  {{ row.counselorInChargeName }}
                </ULink>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #elapsedTimeBeforeStart-data="{ row }">
        <div class="text-sm">{{ row.elapsedTimeBeforeStart }}</div>
      </template>
      <template #elapsedTimeInProgress-data="{ row }">
        <div class="text-sm">{{ row.elapsedTimeInProgress }}</div>
      </template>
      <template #latestPostTime-data="{ row }">
        <div class="text-sm">
          {{ row.latestPostTime ? fromNow(new Date(row.latestPostTime)) : "" }}
        </div>
      </template>
      <template #clientName-data="{ row }">
        <div class="flex items-center space-x-2">
          <div class="border-r pr-2">
            <UChip
              size="md"
              position="bottom-right"
              inset
              color="red"
              :ui="{ base: '-mx-0.5 -my-0.5 rounded-full' }"
              :show="row.isBlock || false"
              text="ー"
            >
              <component
                :is="getSNSIconComponent(row.channel)"
                class="h-5 w-5"
              />
            </UChip>
          </div>
          <UAvatar
            v-if="row.counseleeImage"
            size="xs"
            :src="row.counseleeImage"
            :alt="row.counseleeName"
          />
          <div class="flex flex-col -space-y-1">
            <ULink
              inactive-class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
            >
              {{ row.counseleeName }}
            </ULink>
            <div
              v-if="row.isBlock"
              class="text-[10px] text-red-500 dark:text-red-300"
            >
              ブロックされています
            </div>
          </div>
        </div>
      </template>
      <template #count-data="{ row }">
        <div :class="getHasAlertWork(row.hasAlertWork)">
          {{ row.count }} 回目
        </div>
      </template>
      <template #risk-data="{ row }">
        <div
          class="flex justify-center items-center"
          :class="`text-${getCasePriorityColorIcon(row.risk).color}-500`"
        >
          {{ $t(`casePriority.${row.risk}`) }}
        </div>
      </template>
      <template #preview-data="{ row }">
        <UButton
          v-if="canUpdateCase(row.status, user as any, row.counselorInChargeId, userPermissions)"
          size="2xs"
          block
          variant="solid"
          class="px-3"
          @click="onChatWithUser(row)"
        >
          相談する
        </UButton>
        <div v-else></div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div>
          <span class="text-sm leading-5">
            <span class="font-medium">{{ totalCasesCount }}</span> 件中
            <span class="font-medium">{{ pageFrom }}</span> ～
            <span class="font-medium">{{ pageTo }}</span> 件の結果を表示
          </span>
        </div>

        <UPagination
          v-model="pagination.page"
          :page-count="pagination.pageRangeDisplayed"
          :total="totalCasesCount"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline',
              },
            },
          }"
        />
      </div>
    </template>
  </UCard>
  <AppCounselorsPalette
    :is-open="isOpenCounselorsPalette"
    @close="isOpenCounselorsPalette = false"
    @select="onSelectCounselor"
    actionLabel="に担当者を変更します"
  />
  <DashboardCaseDetail
    :is-open="isOpenCaseDetail"
    :case="caseDetail"
    @close="isOpenCaseDetail = false"
  />
</template>
