<template>
  <div class="grid grid-cols-12">
    <DashboardCustomerSettingCard
      class="col-span-3 mr-4"
      label="対応期間"
      :value="caseStatistics.openCount"
      color="zinc"
      icon="i-mdi-calendar-multiple-check"
      :showMore="false"
      :loading="loadings['fetchCaseStatistics']"
    />
    <div class="col-span-9 grid grid-cols-4 space-x-4">
      <DashboardStatisticsCard
        label="友だち登録数"
        :value="caseStatistics.counseleesCount"
        color="primary"
        icon="i-heroicons-user-group-solid"
        :loading="loadings['fetchCaseStatistics']"
        :showMore="false"
      />
      <DashboardStatisticsCard
        label="未対応"
        :value="caseStatistics.openCount"
        color="red"
        icon="i-solar-chat-unread-bold"
        :loading="loadings['fetchCaseStatistics']"
        @click="searchByStatus(['open'])"
      />
      <DashboardStatisticsCard
        label="対応中"
        :value="caseStatistics.inProgressCount"
        color="cyan"
        icon="i-heroicons-chat-bubble-left-right-solid"
        :loading="loadings['fetchCaseStatistics']"
        @click="searchByStatus(['in_progress'])"
      />
      <DashboardStatisticsCard
        label="待機中"
        :value="caseStatistics.waitingCount"
        color="purple"
        icon="i-medical-icon-i-waiting-area"
        :loading="loadings['fetchCaseStatistics']"
        @click="searchByStatus(['waiting'])"
      />
    </div>
    <!-- <DashboardTodayCard
      class="col-span-2"
      label="対応時間帯"
      :value="caseStatistics.inProgressCount"
      color="orange"
      icon="i-heroicons-chat-bubble-left-right-solid"
      :showMore="false"
      :loading="loadings['fetchCaseStatistics']"
    /> -->
    <div class="col-span-12 mt-3 pl-4">
      <div class="text-sm text-left mb-1">今日の対応時間帯</div>
      <div class="mt-1 flex flex-wrap gap-2 justify-start">
        <UBadge
          v-if="counselingTermsLoadings['fetchTodayCounselingTerm']"
          color="white"
          variant="solid"
          size="sm"
          class="flex grow max-w-[120px]"
          :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
        >
          <Icon icon="eos-icons:loading" class="text-lg text-primary-500" />
        </UBadge>
        <template v-else>
          <UBadge
            v-if="todayCounselingTerm.length"
            v-for="term in todayCounselingTerm"
            color="green"
            variant="solid"
            size="sm"
            class="flex grow max-w-[120px]"
            :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
          >
            {{ term.startTime }} ~ {{ term.endTime }}
          </UBadge>
          <UBadge
            v-else
            color="red"
            variant="solid"
            size="sm"
            class="flex grow max-w-[120px]"
            :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
          >
            今日は休業日です
          </UBadge>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCasesStore } from "~/stores/app/cases";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { useCounselingTermsStore } from "~/stores/app/counseling-terms";
  import { Icon } from "@iconify/vue";
  const counselingTermsStore = useCounselingTermsStore();
  const { todayCounselingTerm, loadings: counselingTermsLoadings } =
    storeToRefs(counselingTermsStore);
  const casesStore = useCasesStore();
  const { caseStatistics, loadings, searchConditions } =
    storeToRefs(casesStore);

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
  const searchByStatus = (status: string[]) => {
    const dashboardTableEl = document.getElementById("dashboard-table");
    if (dashboardTableEl) {
      dashboardTableEl.scrollIntoView({ behavior: "smooth" });
      // scroll to dashboard table
    }
    casesStore.resetSearchConditions();
    searchConditions.value.caseStatus = status;
    casesStore.fetchCases(
      currentCustomer.value.customerId as string,
      "searchByStatus",
    );
  };

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        counselingTermsStore.fetchTodayCounselingTerm(
          currentCustomer.value.customerId as string,
        );
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>
