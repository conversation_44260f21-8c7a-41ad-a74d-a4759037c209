<template>
  <UCard
    :ui="{ body: { base: 'h-full', padding: '!px-4 !py-4' } }"
    class="group"
  >
    <div class="flex flex-col h-full">
      <div class="flex flex-inline items-start justify-between">
        <div>
          <div class="text-xs text-left mb-1">対応期間</div>
          <div
            class="flex flex-col space-y-0.5 text-xs justify-left items-center"
          >
            <div>
              {{ formatDate(new Date(currentCustomer.startDate)) }} から
            </div>
            <div>
              {{ formatDate(new Date(currentCustomer.endDate)) }} まで
            </div>
          </div>
        </div>
        <div class="flex flex-col">
          <div class="text-left text-xs">回線数</div>
          <div class="text-left text-4xl">
            <number
              :from="0"
              :to="currentCustomer.contractedLines"
              :duration="1"
              :delay="0"
            />
          </div>
        </div>
      </div>
      <!-- <div class="mt-auto pt-2">
        <div class="text-xs text-left mb-1">対応時間帯</div>
        <div class="mt-1 flex flex-wrap gap-2 justify-start">
          <UBadge
            color="green"
            variant="solid"
            size="sm"
            class="flex grow"
            :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
          >
            09:00 ~ 18:00
          </UBadge>
        </div>
      </div> -->
    </div>
  </UCard>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
</script>
