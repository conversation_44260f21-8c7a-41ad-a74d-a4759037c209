<template>
  <USlideover
    :model-value="props.isOpen"
    @close="emit('close')"
    :ui="{
      width: 'max-w-xl',
    }"
    prevent-close
  >
    <UCard
      class="flex flex-col flex-1"
      :ui="{
        body: { base: 'flex-1' },
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        base: 'bg-red-400',
      }"
    >
      <UButton
        color="white"
        variant="solid"
        icon="i-heroicons-x-mark-20-solid"
        class="absolute top-3 mt-0.5 -left-14"
        size="xl"
        @click="emit('close')"
      />
      <ChatHeader
        :user="counselee"
        :title="`${caseDetail.count}回目`"
        class="!px-0 !pt-0 mb-2"
      >
        <template #name>
          <UIcon
            v-if="caseDetail.counselorInChargeId"
            name="i-heroicons-chat-bubble-left-right"
            class="w-5 h-5 flex-shrink-0"
          />
          <div class="flex text-primary-500 items-baseline space-x-1">
            <div>
              {{ caseDetail.counselorInChargeName }}
            </div>
          </div>
        </template>
        <template #title>
          <CaseStatusChange :case="caseDetail" :key="caseDetail.status" />
        </template>
      </ChatHeader>
      <UTabs v-model="tabSelected" :items="tabs" class="w-full">
        <template #default="{ item }">
          <div class="flex items-center gap-2 relative truncate">
            <UIcon :name="item.icon" class="w-4 h-4 flex-shrink-0" />

            <span class="truncate">{{ item.label }}</span>
          </div>
        </template>
      </UTabs>
      <div class="relative h-[calc(100vh-117px)] overflow-y-auto overflow-x-hidden">
        <div
          v-if="tabSelectedObject?.key === 'case-chats'"
          class="border dark:border-gray-800 rounded-lg shadow-sm bg-gray-100 h-full dark:bg-gray-900"
        >
          <ChatMessageList
            class="overflow-scroll pb-36 pt-3 px-3 h-full absolute top-0 w-full"
            :messages="caseDetail.chat"
            :userAvatar="caseDetail.counseleeImage"
            :surveyResults="caseDetail.surveyResults"
            @open-survey-result="onOpenSurveyResult"
          />
          <div class="absolute bottom-0 w-full px-3 pb-3">
            <ChatMessageBox
              v-if="!isReadOnly"
              class=""
              v-model="caseDetail.messageText"
              @send="onSend"
              :loading="loadings.sendMessage[caseDetail.caseId]"
              @select:survey="caseDetail.selectedSurvey = $event"
              :disabled="
                caseDetail.messageText?.trim().length === 0 && !caseDetail.selectedSurvey
              "
            />
          </div>
        </div>
        <div
          v-else-if="tabSelectedObject?.key === 'chat-information'"
          class="min-h-[calc(100vh-133px)] max-h-[calc(100vh-333px)] overflow-y-auto p-1"
        >
          <CaseInformation
            :case="caseDetail"
            :readonly="isReadOnly && !userPermissions.includes('update:app-cases')"
            :maxTags="20"
          />
        </div>
        <div v-if="tabSelectedObject?.key === 'survey-results'">
          <CaseSurveyResults :case="caseDetail" />
        </div>
        <div v-if="tabSelectedObject?.key === 'wizard-results'">
          <CaseWizardResults :case="caseDetail" />
        </div>
        <UCard
          v-if="tabSelectedObject?.key === 'chat-histories'"
          :ui="{
            wrapper: 'h-full',
            body: {
              padding: '!py-2 !px-2',
            },
          }"
        >
          <ChatHistories :case="caseDetail" chat-message-list-class="min-h-[calc(100vh-200px)] max-h-[calc(100vh-200px)]"/>
        </UCard>
      </div>
    </UCard>
  </USlideover>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useCasesStore } from "~/stores/app/cases";
import { useChatsStore } from "~/stores/app/chats";
import type { Case } from "~/types";
import { useAuthStore } from "~/stores/auth";
const userPermissions = usePermissions();
const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const casesStore = useCasesStore();
const { loadings, caseDetail } = storeToRefs(casesStore);
const tabSelected = computed({
  get() {
    return caseDetail.value?.chatExtendActiveTab || 0;
  },
  set(value) {
    caseDetail.value.chatExtendActiveTab = value;
  },
});
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
  case: {
    type: Object as PropType<Case>,
    default: () => ({}),
  },
});
const emit = defineEmits(["close", "select"]);

const counselee = computed(() => {
  return {
    userId: caseDetail.value.counseleeId,
    userName: caseDetail.value.counseleeName,
    userAvatar: caseDetail.value.counseleeImage,
    snsChannel: caseDetail.value.channel,
    isBlock: caseDetail.value.isBlock,
  };
});

const isReadOnly = computed(() => {
  return !canUpdateCase(
    caseDetail.value.status,
    user.value as any,
    caseDetail.value.counselorInChargeId,
    userPermissions.value
  );
});

const tabs = computed(() => {
  const _tabs = [
    {
      key: "case-chats",
      label: "メッセージ",
      icon: "i-heroicons-chat-bubble-left-right",
    },
    {
      key: "chat-information",
      label: "ケースの情報",
      icon: "i-heroicons-information-circle",
    },
    {
      key: "chat-histories",
      label: "過去履歴",
      icon: "i-heroicons-clock",
    },
  ];
  if (caseDetail.value?.surveyResults?.length) {
    _tabs.push({
      key: "survey-results",
      label: "アンケート結果",
      icon: "i-wpf-survey",
    });
  }

  if (caseDetail.value.wizardResults?.length) {
    _tabs.push({
      key: "wizard-results",
      label: "ウィザード結果",
      icon: "i-clarity-flow-chart-line",
    });
  }
  return _tabs;
});

const tabSelectedObject = computed(() => {
  return tabs.value[tabSelected.value];
});

const onSend = (message: string, survey: any) => {
  if (message.trim().length === 0 && !survey) {
    return;
  }
  casesStore.sendMessage(caseDetail.value.caseId, { text: message }, survey);
};

watch(
  () => props.isOpen,
  (value) => {
    if (value) {
      casesStore.fetchCaseDetail(caseDetail.value.caseId);
    }
  }
);

const onOpenSurveyResult = (surveyResult: any) => {
  caseDetail.value.currentSurveyResult = surveyResult;
  caseDetail.value.chatExtendActiveTab = 2;
};
</script>
