<template>
  <UCard :ui="{ body: { padding: '!px-4 !py-4' } }" class="group">
    <div class="flex flex-row items-center space-x-2 justify-center">
      <div
        class="flex flex-row items-center space-x-2 justify-center pr-3 mr-1 border-r border-dashed"
      >
        <BaseNumberCard
          :number="now.format('ddd')"
          label="曜日"
          color="green"
        />
        <BaseNumberCard :number="now.format('MM')" label="月" />
        <BaseNumberCard :number="now.format('DD')" label="日" />
      </div>
      <BaseNumberCard :number="now.format('HH')" label="時" />
      <BaseNumberCard :number="now.format('mm')" label="分" />
      <BaseNumberCard :number="now.format('ss')" label="秒" />
    </div>
    <div class="mt-2">
      <div class="text-xs text-center">対応時間帯</div>
      <div class="mt-1 flex flex-wrap gap-2 justify-center">
        <UBadge
          color="green"
          variant="solid"
          size="md"
          class="flex grow px-3 max-w-[150px]"
          :ui="{ rounded: 'rounded-full', base: 'justify-center' }"
        >
          09:00 ~ 18:00
        </UBadge>
      </div>
    </div>
  </UCard>
</template>
<script setup lang="ts">
  import dayjs from "dayjs";

  const today = dayjs();
  const now = ref(dayjs());
  setInterval(() => {
    now.value = dayjs();
  }, 1000);

  const isWorking = ref(true);

  const props = defineProps({
    icon: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    value: {
      type: Number,
      default: 0,
    },
    to: {
      type: String,
      default: "/app",
    },
    color: {
      type: String,
      default: "primary",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showMore: {
      type: Boolean,
      default: true,
    },
  });
</script>
