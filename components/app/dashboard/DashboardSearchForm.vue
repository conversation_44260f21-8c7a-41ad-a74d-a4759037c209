<template>
  <div>
    <div class="w-2/3 mx-auto flex flex-inline space-x-4">
      <UInput
        color="white"
        class="flex-1"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="md"
        :trailing="false"
        placeholder="キーワード(タイトル・相談者名・UID)..."
        v-model="searchConditions.freeKeyword"
      />
      <UButton
        class="px-8"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="sm"
        color="primary"
        variant="solid"
        label="検索"
        :trailing="false"
        @click="casesStore.fetchCases(currentCustomer.customerId as string)"
      />
    </div>
    <UAccordion :items="items" variant="link">
      <template #default="{ item, open }">
        <UButton
          color="gray"
          variant="link"
          class="dark:border-gray-700 w-fit"
          :ui="{ rounded: 'rounded-none', padding: { sm: 'p-3' } }"
        >
          <template #leading>
            <UIcon
              :name="item.icon"
              class="w-3 h-3 text-gray-500 dark:text-gray-900 transition-transform duration-200"
              :class="{ 'rotate-90': open }"
            />
          </template>
          <span class="truncate">{{ item.label }}</span>
        </UButton>
      </template>

      <template #getting-started>
        <UCard :ui="{ base: 'overflow-visible' }">
          <div class="grid grid-cols-4 space-x-4 mt-4">
            <!-- <UFormGroup label="ケースID">
              <UInput placeholder="" v-model="searchConditions.caseId" />
            </UFormGroup> -->
            <!-- <UFormGroup label="相談者ID">
              <UInput
                placeholder=""
                v-model="searchConditions.counselorInChargeId"
              />
            </UFormGroup> -->

            <UFormGroup class="col-span-2" label="担当者">
              <AppCounselorsMultiSelect
                v-model="searchConditions.counselorInChargeId"
              />
            </UFormGroup>
            <UFormGroup label="この日から">
              <BaseDatePicker v-model="startDate" block color="white" />
            </UFormGroup>
            <UFormGroup label="この日まで">
              <BaseDatePicker v-model="endDate" block color="white" />
            </UFormGroup>
          </div>
          <div class="grid grid-cols-4 space-x-4 mt-2">
            <!-- <UFormGroup label="ケース相談分類">
              <USelectMenu
                :options="caseAttributesOption"
                multiple
                placeholder="開発中。。。"
              />
            </UFormGroup> -->
            <UFormGroup class="mt-2 col-span-3" label="その他">
              <div class="flex flex-inline justify-stretch space-x-4 mt-2">
                <UCheckbox
                  color="primary"
                  label="担当ケースのみ"
                  v-model="searchConditions.onlyCaseInCharge"
                />
                <UCheckbox
                  color="primary"
                  label="開始前を含む"
                  v-model="searchConditions.includeBeforeStart"
                />
                <UCheckbox
                  color="primary"
                  label="対応済を含む"
                  v-model="searchConditions.includeResolved"
                />
                <UCheckbox
                  color="primary"
                  label="無効を含む"
                  v-model="searchConditions.includeCancelled"
                />
              </div>
            </UFormGroup>
          </div>
          <template #footer>
            <div class="flex flex-inline justify-center space-x-4">
              <UButton
                class="px-5"
                icon="i-heroicons-arrow-uturn-left-solid"
                size="sm"
                color="white"
                variant="solid"
                label="リセット"
                :trailing="false"
                @click="casesStore.resetSearchConditions()"
              />
              <UButton
                class="px-8"
                icon="i-heroicons-magnifying-glass-20-solid"
                size="sm"
                color="primary"
                variant="solid"
                label="検索"
                :trailing="false"
                @click="
                  casesStore.fetchCases(currentCustomer.customerId as string)
                "
              />
            </div>
          </template>
        </UCard>
      </template>
    </UAccordion>
  </div>
</template>
<script setup lang="ts">
  import { useCasesStore } from "~/stores/app/cases";
  import { useCounselorsStore } from "~/stores/app/counselors";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { storeToRefs } from "pinia";
  import { cloneDeep } from "lodash";
  import type { CaseStatus } from "~/types/enums";
  import type { CaseSearchConditions } from "~/types";

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
  const counselorsStore = useCounselorsStore();
  const { counselorsForPalette } = storeToRefs(counselorsStore);
  const casesStore = useCasesStore();
  const { pagination, searchConditions, sortConditions } =
    storeToRefs(casesStore);
  const router = useRouter();
  const route = useRoute();
  const date = ref(new Date());
  const attrs = ref([
    {
      key: "today",
      highlight: { color: "green", fillMode: "solid" },
      dates: new Date(),
    },
  ]);
  const items = [
    {
      label: "高度な検索",
      icon: "i-heroicons-play-solid",
      defaultOpen: false,
      slot: "getting-started",
    },
  ];

  const caseAttributesOption = ["Wade Cooper"];

  const startDate = computed({
    get: () => {
      return searchConditions.value.startDate
        ? new Date(searchConditions.value.startDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.startDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  const endDate = computed({
    get: () => {
      return searchConditions.value.endDate
        ? new Date(searchConditions.value.endDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.endDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  onMounted(() => {
    const query = route.query;
    let _searchConditions, _pagination, _sortConditions;
    if (query) {
      _searchConditions = cloneDeep({
        ...searchConditions.value,
        ...query,
      });

      //check if caseStatus is not array
      if (query.caseStatus && !Array.isArray(query.caseStatus)) {
        _searchConditions.caseStatus = [query.caseStatus as CaseStatus];
      }

      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      casesStore.updateConditions(
        _searchConditions,
        _pagination,
        _sortConditions,
      );
    }

    // nextTick(() => {
    //   casesStore.fetchCases(currentCustomer.value.customerId as string);
    // });
  });

  // watch page and pageRangeDisplayed
  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        casesStore.fetchCases(
          currentCustomer.value.customerId as string,
          "pagination.value",
        );
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        casesStore.fetchCases(
          currentCustomer.value.customerId as string,
          "sortConditions.value",
        );
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        casesStore.fetchCases(
          currentCustomer.value.customerId as string,
          "currentCustomer.value",
        );
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>
