<template>
  <UCard :ui="{ body: { padding: '!px-4 !py-4 h-full' } }" class="group">
    <div class="flex flex-col h-full">
      <div class="flex flex-inline items-center justify-between">
        <div class="flex flex-col">
          <div class="text-left text-xs">
            {{ props.label }}
          </div>
          <USkeleton v-if="loading" class="mt-1 h-8 w-[60px]" />
          <div v-else class="text-left text-4xl">
            <number :from="0" :to="props.value" :duration="1" :delay="0" />
          </div>
        </div>
        <UAvatar
          class="group-hover:scale-110 transition ease-in-out duration-300"
          :icon="props.icon"
          size="lg"
          :ui="{
            background: `bg-${props.color}-400 dark:bg-${props.color}-900`,
            rounded: 'rounded-xl',
            icon: { base: 'text-white dark:text-gray-50' },
          }"
        />
      </div>
      <USkeleton v-if="loading" class="mt-1 h-4 w-[80px]" />
      <UButton
        v-else-if="props.showMore"
        size="2xs"
        :color="props.color"
        variant="ghost"
        label="もっと見る"
        icon="i-heroicons-arrow-right-20-solid"
        :trailing="true"
        :ui="{
          padding: { '2xs': '!py-0 !px-0' },
        }"
        class="mt-auto w-fit px-4 mt-2"
      />
    </div>
  </UCard>
</template>
<script setup lang="ts">
  const props = defineProps({
    icon: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    value: {
      type: Number,
      default: 0,
    },
    to: {
      type: String,
      default: "/app",
    },
    color: {
      type: String,
      default: "primary",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    showMore: {
      type: Boolean,
      default: true,
    },
  });
</script>
