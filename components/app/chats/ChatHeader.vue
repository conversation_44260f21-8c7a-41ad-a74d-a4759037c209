<template>
  <div
    class="py-2.5 px-3 border-b dark:border-gray-700 font-semibold flex flex-inline items-center justify-between"
  >
    <div class="flex items-center space-x-2">
      <div class="pr-2 border-r dark:border-gray-600">
        <UChip
          size="lg"
          position="bottom-right"
          inset
          color="red"
          :ui="{ base: '-mx-1 -my-1 rounded-full' }"
          :show="user?.isBlock || false"
          text="ー"
        >
          <component
            :is="getSNSIconComponent(user.snsChannel)"
            class="h-6 w-6"
          />
        </UChip>
      </div>
      <UAvatar
        v-bind="{
          src: user?.userAvatar,
          alt: user?.userName,
        }"
        size="xs"
        loading="lazy"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-xs font-semibold text-gray-700 dark:text-gray-800',
        }"
      />
      <div class="flex flex-col -space-y-1">
        <div class="truncate">
          {{ user?.userName }}
        </div>
        <div v-if="user?.isBlock" class="text-[10px] text-red-500 dark:text-red-300 font-normal">
          ブロックされています
        </div>
      </div>
      <slot name="name" />
    </div>
    <div class="flex items-center">
      <slot name="title"></slot>
      <div class="text-sm font-normal whitespace-nowrap border-l dark:border-gray-600 pl-1 ml-2">
        <div>
          {{ title }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    user: {
      type: Object as PropType<any>,
      required: true,
    },
    title: {
      type: String as PropType<string>,
      default: "",
    },
  });
</script>
