<template>
  <!-- <div class="flex items-center justify-between px-4 py-2">
    <div class="flex items-center space-x-2">
      <UAvatar
        v-bind="{
          src: currentChattingUser?.userAvatar,
          alt: currentChattingUser?.userName,
          chipColor: 'green',
          chipPosition: 'bottom-right',
        }"
        size="xs"
        loading="lazy"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-xs font-semibold text-gray-700 dark:text-gray-800',
          chip: {
            size: {
              xs: 'h-2 w-2',
            },
          },
        }"
      />
      <div>
        {{ currentChattingUser?.userName }}
      </div>
    </div>
    <div class="font-light">
      男性 / 30代
    </div>
  </div> -->
  <div class="flex px-4 py-2 bg-gray-200 h-full">
    <UTabs
      v-model="selected"
      :items="tabs"
      class="w-full"
      :ui="{ container: 'flex-1', base: 'flex-1' }"
    >
      <template #default="{ item }">
        <div class="flex items-center gap-2 relative truncate">
          <UIcon :name="item.icon" class="w-4 h-4 flex-shrink-0" />
          <span class="truncate">{{ item.label }}</span>
        </div>
      </template>
      <template #item="{ item }">
        <div
          v-if="item.key === 'case-information'"
        >
          <CaseInformation :case="currentChattingUser" />
        </div>
        <UCard
          v-if="item.key === 'chat-histories'"
          :ui="{
            wrapper: 'h-full',
            body: {
              padding: '!py-2 !px-2',
            },
          }"
        >
          <ChatHistories />
        </UCard>
      </template>
    </UTabs>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useChatsStore } from '~/stores/app/chats'
const chatsStore = useChatsStore()
const { chattingUser, currentChattingUser } = storeToRefs(chatsStore)

const tabs = [
  {
    key: 'case-information',
    label: 'ケースの情報',
    icon: 'i-heroicons-information-circle',
    content: 'This is the content shown for Tab1'
  },
  {
    key: 'chat-histories',
    label: '過去履歴',
    icon: 'i-heroicons-clock',
    content: 'And, this is the content for Tab2'
  }
]

const selected = computed({
  get () {
    return chattingUser.value?.extendTabIndex || 0
  },
  set (value) {
    chatsStore.setExtendTabIndex(currentChattingUser.value?.userId, value)
  }
})
</script>
