<template>
  <div
    v-if="loadings.fetchCaseChatHistory"
    class="flex flex-col items-center justify-center space-y-1 py-6"
  >
    <Icon icon="eos-icons:loading" class="text-primary-500 text-4xl" />
    <div class="text-gray-500 text-sm">チャット履歴を読み込み中</div>
  </div>
  <div v-else-if="props.case.histories?.length" class="">
    <div v-if="props.case.currentHistory" class="flex flex-col">
      <div class="pl-2 pr-3 py-2 flex items-center justify-between">
        <div class="flex items-center font-semibold space-x-2">
          <UButton
            icon="i-heroicons-arrow-left"
            size="sm"
            color="primary"
            square
            :padded="false"
            variant="link"
            @click="props.case.currentHistory = null"
          />
          <UBadge color="gray" variant="solid"
            >{{ props.case.currentHistory?.count }}回目</UBadge
          >
          <div v-if="props.case.currentHistory?.createdAt" class="text-sm">
            {{
              formatDate(
                new Date(props.case.currentHistory?.createdAt),
                "YYYY年MM月DD日 HH:mm",
              )
            }}
          </div>
        </div>
        <div class="flex-1 flex space-x-1 justify-end relative truncate">
          <UAvatar
            size="2xs"
            :src="props.case.currentHistory.counselorInChargeImage"
            :alt="props.case.currentHistory.counselorInChargeName"
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-300 dark:bg-gray-400',
              placeholder:
                'text-xs font-semibold text-gray-700 dark:text-gray-800',
            }"
          />
          <div class="text-sm">
            {{ props.case.currentHistory.counselorInChargeName }}
          </div>
        </div>
      </div>
      <UDivider />

      <ChatMessageList
        class="overflow-scroll py-4 min-h-[calc(100vh-133px)] max-h-[calc(100vh-133px)]"
        :class="chatMessageListClass"
        :messages="props.case.currentHistory.chat"
      />
    </div>
    <UVerticalNavigation
      v-else
      :links="props.case.histories"
      class="w-full"
      :ui="{
        label:
          'relative truncate text-gray-900 font-semibold dark:text-white text-left',
      }"
    >
      <template #default="{ link }">
        <div
          class="flex items-center space-x-2 relative"
          @click="onSelectHistory(link)"
        >
          <UBadge color="gray" variant="solid">{{ link.count }}回目</UBadge>
          <div class="text-sm hover:text-primary">
            {{ formatDate(new Date(link.createdAt), "YYYY年MM月DD日 HH:mm") }}
          </div>
        </div>
      </template>
      <template #badge="{ link }">
        <div class="flex-1 flex space-x-1 justify-end relative truncate">
          <UAvatar
            size="2xs"
            :src="link.counselorInChargeImage"
            :alt="link.counselorInChargeName"
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-300 dark:bg-gray-400',
              placeholder:
                'text-xs font-semibold text-gray-700 dark:text-gray-800',
            }"
          />
          <div class="text-sm">
            {{ link.counselorInChargeName }}
          </div>
        </div>
      </template>
    </UVerticalNavigation>
  </div>
  <div v-else class="flex flex-col items-center justify-center space-y-1 py-6">
    <Icon icon="fluent:chat-off-20-filled" class="text-gray-300 text-4xl" />
    <div class="text-gray-500 text-sm">チャット履歴がありません</div>
  </div>
</template>

<script setup lang="ts">
  import type { Case } from "~/types";
  import { storeToRefs } from "pinia";
  import { useCasesStore } from "~/stores/app/cases";
  import { Icon } from "@iconify/vue";
  const props = defineProps<{
    case: Case;
    chatMessageListClass?: string;
  }>();
  const casesStore = useCasesStore();
  const { loadings } = storeToRefs(casesStore);
  onMounted(() => {
    if (!props.case.histories) {
      casesStore.fetchCaseChatHistory(props.case.caseId);
    }
  });

  watch(
    () => props.case.caseId,
    () => {
      if (!props.case.histories) {
        casesStore.fetchCaseChatHistory(props.case.caseId);
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  const onSelectHistory = (history: any) => {
    props.case.currentHistory = history;
  };
</script>
