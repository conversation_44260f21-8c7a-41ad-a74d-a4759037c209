<template>
  <div class="flex flex-col">
    <div>
      <UAvatar
        v-bind="{
          alt: data.senderName,
          chipColor: 'green',
          chipPosition: 'bottom-right',
        }"
        size="xs"
        loading="lazy"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder:
            'text-xs font-semibold text-gray-700 dark:text-gray-800',
          chip: {
            size: {
              xs: 'h-2 w-2',
            },
          },
        }"
      />
    </div>
    <div class="flex flex-row" :class="[{ 'justify-end': isCounselorMessage }]">
      
      <div
        class="w-fit max-w-[87%] px-2.5 py-2.5 rounded-xl text-sm break-words whitespace-break-spaces"
        :class="[
          isCounselorMessage
            ? 'bg-primary-300 dark:bg-primary-800 rounded-br-none'
            : 'bg-gray-200 dark:bg-gray-800 rounded-bl-none',
          {
            '': true,
          },
        ]"
      >
        {{ data.content?.text }}
      </div>
    </div>
    <div
      class="flex px-2 pt-1 text-xs font-light"
      :class="[{ 'justify-end': isCounselorMessage }]"
    >
      <!-- <span> {{ fromNow(new Date(data.createdAt || "")) }}</span> -->
      <span>
        {{ formatDateForMessage(new Date(data.createdAt || "")) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { CaseChatMessage } from "~/types";
  const props = defineProps({
    data: {
      type: Object as PropType<CaseChatMessage>,
      required: true,
    },
  });

  const isCounselorMessage = computed(() => {
    return props.data?.sender === "counselor";
  });
</script>
