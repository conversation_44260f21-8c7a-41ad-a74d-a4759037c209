<template>
  <div
    class="group flex flex-row items-end space-x-2"
    :class="{
      'justify-start flex-row-reverse space-x-reverse': isRightMessage,
    }"
  >
    <div v-if="showAvatar">
      <UIcon
        name="i-fluent-emoji-robot"
        v-if="data.sender === 'chatBot'"
        class="text-3xl ml-0.5 shadow-lg group-hover:shadow-xl group-hover:scale-125 transition-all duration-300"
      />
      <UAvatar
        v-else
        v-bind="{
          alt: data.senderName,
          src: data.senderAvatar,
        }"
        size="sm"
        loading="lazy"
        class="group-hover:shadow-xl group-hover:scale-125 transition-all duration-300"
        :ui="{
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-xs font-semibold text-gray-700 dark:text-gray-800',
        }"
      />
    </div>
    <div class="max-w-[77%] flex flex-col" :class="[{ 'items-end': isRightMessage }]">
      <div
        v-if="isRightMessage"
        class="text-[10px] px-2 font-semibold text-right cursor-pointer"
        :class="{
          'text-primary-500': data.senderId !== user?.counselorId,
          'text-gray-500': data.senderId === user?.counselorId,
          'text-primary-600': isChatBotMessage,
        }"
      >
        <span v-if="isChatBotMessage">
          {{ $t("chatBot") }}
        </span>
        <UTooltip v-else :text="data.senderId" :popper="{ placement: 'top' }">
          {{
            isRightMessage && data.senderId !== user?.counselorId
              ? data.senderName
              : "自分"
          }}
        </UTooltip>
      </div>

      <div
        class="w-fit px-2.5 py-2.5 rounded-xl text-sm break-words whitespace-break-spaces"
        :class="[
          isRightMessage
            ? 'bg-primary-900/80 dark:bg-gray-950 rounded-br-none text-gray-100'
            : 'bg-gray-200 dark:bg-gray-700 rounded-bl-none',
          {
            'bg-primary-900 dark:bg-gray-800 text-white': isChatBotMessage,
          },
        ]"
      >
        <div class="break-all whitespace-break-spaces">
          <a
            class="underline"
            :class="{
              'text-primary-700': !isRightMessage,
            }"
            v-if="isALink(data.content?.text)"
            :href="data.content?.text"
            target="_blank"
          >
            {{ data.content?.text }}
          </a>
          <span v-else>{{ data.content?.text }}</span>
        </div>
        <div
          v-if="data.survey"
          class="mt-2 border-primary-700 bg-gray-100 dark:bg-gray-900 rounded-lg"
        >
          <div
            class="font-semibold px-4 py-3 text-center truncate text-gray-700 dark:text-gray-200"
          >
            {{ data.survey?.surveyName }}
          </div>
          <div
            class="text-primary-800 border-t dark:border-gray-700 py-1 px-4 flex items-center space-x-1"
          >
            <UButton
              :to="surveyLink"
              target="_blank"
              color="primary"
              variant="ghost"
              icon="i-majesticons-open"
              trailing
              class="w-full"
              block
              >アンケートを開く</UButton
            >
          </div>
          <div v-if="surveyResultsList.length" class="border-t pb-2">
            <div
              class="flex space-x-1 items-center py-2 justify-center text-green-600"
              @click="openSurveyResultsList = !openSurveyResultsList"
            >
              <UIcon
                name="i-icon-park-solid-down-c"
                class="w-4 h-4 transition-all duration-200"
                :class="{
                  'transform -rotate-180': openSurveyResultsList,
                }"
              />
              <div class="text-sm hover:cursor-pointer hover:underline">
                回答されました !
              </div>
            </div>
            <div
              v-if="openSurveyResultsList"
              v-for="(surveyResult, index) in surveyResultsList"
              class="flex space-x-1 justify-center items-center py-1 text-gray-500 cursor-pointer"
              @click="onOpenSurveyResult(surveyResult)"
            >
              <div class="text-xs">[{{ index + 1 }}回目]</div>

              <div class="text-xs hover:underline hover:text-primary-500">
                {{ formatDate(new Date(surveyResult.createdAt), "YYYY年MM月DD日 HH:mm") }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="flex px-2 pt-1 font-light text-[9px]"
        :class="[{ 'justify-end': isRightMessage }]"
      >
        <div class="flex space-x-1 items-center" v-if="data.isSending">
          <Icon icon="eos-icons:loading" class="text-sm" />
          <span> 送信中</span>
        </div>
        <div class="flex space-x-1 items-center text-red-500" v-else-if="data.error">
          <Icon icon="bx:error" class="text-sm" />
          <span> {{ data.error }}</span>
        </div>
        <span v-else>
          {{ formatDateForMessage(new Date(data.createdAt || "")) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { CaseChatMessage } from "~/types";
import { useAuthStore } from "~/stores/auth";
import { Icon } from "@iconify/vue";
import { useAppCustomersStore } from "~/stores/app/customers";
import { orderBy } from "lodash";
const emits = defineEmits(["open-survey-result"]);
const appCustomersStore = useAppCustomersStore();
const { currentCustomer } = storeToRefs(appCustomersStore);
const authStore = useAuthStore();
const { user } = storeToRefs(authStore);

const props = defineProps({
  data: {
    type: Object as PropType<CaseChatMessage>,
    required: true,
  },
  surveyResults: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
});

const isRightMessage = computed(() => {
  return ["counselor", "chatBot"].includes(props.data?.sender);
});

const isChatBotMessage = computed(() => {
  return props.data?.sender === "chatBot";
});

const showAvatar = computed(() => {
  return true;
});

const surveyLink = computed(() => {
  return (
    window.location.origin +
    "/survey/?sid=" +
    props.data.survey?.surveyId +
    "&c=" +
    currentCustomer.value.customerId
  );
});
const openSurveyResultsList = ref(false);
const surveyResultsList = computed(() => {
  // orderBy createdAt desc
  return orderBy(
    props.surveyResults?.filter(
      (result) => result.surveyId === props.data.survey?.surveyId
    ),
    "createdAt",
    "asc"
  );
});

const onOpenSurveyResult = (surveyResult: any) => {
  emits("open-survey-result", surveyResult);
};
</script>
