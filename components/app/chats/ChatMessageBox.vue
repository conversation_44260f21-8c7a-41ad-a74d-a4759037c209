<template>
  <div>
    <UCard :ui="{ body: { padding: '!px-0 !pt-1 !pb-0' } }" class="relative">
      <UTextarea
        ref="inputBox"
        v-model="messageText"
        autoresize
        variant="none"
        placeholder="メッセージ..."
        rows="1"
        class="pl-1.5 pr-1 max-h-[300px] overflow-auto mb-12"
        :class="[
          {
            '!mb-32': selectedSurvey?.surveyId && hasSelectSurvey,
          },
        ]"
        :ui="{
          padding: {
            sm: 'pb-0',
          },
        }"
      />

      <div class="absolute bottom-0 w-full">
        <div
          v-if="selectedSurvey?.surveyId && hasSelectSurvey"
          class="px-4 flex flex-col text-left space-y-1"
        >
          <div
            class="relative group border bg-gray-100 dark:bg-gray-800 dark:border-gray-600 cursor-pointer rounded-lg flex items-center justify-center h-14 w-14 hover:shadow-md"
          >
            <div
              class="absolute hidden group-hover:shadow-lg -top-2.5 -right-2.5 h-6 w-6 group-hover:flex items-center justify-center border border-gray-400 rounded-full"
            >
              <UIcon
                name="i-carbon-close-filled"
                color="gray"
                class="text-xl hover:shadow-lg text-red-500"
                @click="selectedSurvey = null"
              />
            </div>

            <UIcon name="i-wpf-survey" color="gray" class="text-2xl" />
          </div>
          <div class="text-[10px] truncate">
            {{ selectedSurvey?.surveyName }}
          </div>
        </div>
        <div class="flex flex-inline justify-between px-1 pb-1">
          <div class="flex flex-inline">
            <UTooltip text="定型文を選択" :shortcuts="['⌘', 'E']">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-icon-park-outline-text-message"
                color="gray"
                :label="`定型文`"
                :trailing="false"
                @click="isOpenSampleMessagesPalette = true"
              />
            </UTooltip>
            <UTooltip
              v-if="hasSelectSurvey"
              text="アンケートを選択"
              :shortcuts="['⌘', 'S']"
            >
              <UButton
                size="xs"
                variant="ghost"
                icon="i-wpf-survey"
                color="gray"
                label="アンケート"
                :trailing="false"
                @click="isOpenSurveysPalette = true"
              />
            </UTooltip>

            <UDivider
              color="gray"
              orientation="vertical"
              :ui="{ wrapper: { base: 'w-fit mx-1 py-2' } }"
            />
          </div>
          <div v-if="hasSendButton">
            <UTooltip text="メッセージを送信" :shortcuts="['⌘', 'Enter']">
              <UButton
                size="md"
                variant="ghost"
                icon="i-heroicons-paper-airplane"
                color="primary"
                :label="$t('Send')"
                :trailing="false"
                :loading="loading"
                :disabled="disabled"
                @click="onSend"
              />
            </UTooltip>
          </div>
        </div>
      </div>
    </UCard>
    <ChatSampleMessagesPalette
      :is-open="isOpenSampleMessagesPalette"
      @close="isOpenSampleMessagesPalette = false"
      @select="onSelectSampleMessage"
    />
    <AppSurveysPalette
      :is-open="isOpenSurveysPalette"
      @close="isOpenSurveysPalette = false"
      @select="onSelectSurvey"
    />
  </div>
</template>

<script setup lang="ts">
  import { useDialogsStore } from "~/stores/dialogs";

  const { metaSymbol } = useShortcuts();
  const inputBox = ref<{ textarea: HTMLTextAreaElement } | null>(null);

  const dialogsStore = useDialogsStore();
  import type { SampleMessage } from "~/types";

  const props = defineProps({
    modelValue: {
      type: String,
      default: "",
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    hasSendButton: {
      type: Boolean,
      default: true,
    },
    hasSelectSurvey: {
      type: Boolean,
      default: true,
    },
    survey: {
      type: Object as PropType<any>,
      default: null,
    },
  });
  const emits = defineEmits(["send", "update:modelValue", "select:survey"]);
  const onSend = () => {
    if (
      !selectedSurvey &&
      (!messageText.value || messageText.value.trim().length === 0)
    ) {
      return;
    }

    dialogsStore.onOpenConfirmDialog({
      title: "メッセージを送信しますか？",
      message: messageText.value,
      confirmButtonText: "送信",
      cancelButtonText: "キャンセル",
      messageClass: "p-4 bg-gray-50 rounded-lg border text-sm bg-white dark:bg-gray-800 dark:border-gray-700",
      survey: selectedSurvey,
      onConfirm: () => {
        emits("send", messageText.value, selectedSurvey.value);
        messageText.value = "";
        selectedSurvey.value = null;
        inputBox.value?.textarea?.focus();
      },
    });
  };

  const messageText = computed({
    get: () => props.modelValue || "",
    set: (value) => {
      emits("update:modelValue", value);
    },
  });

  defineShortcuts({
    meta_enter: {
      usingInput: true,
      handler: () => {
        if (props.hasSendButton) {
          onSend();
        }
      },
    },
    meta_e: {
      usingInput: true,
      handler: () => {
        isOpenSampleMessagesPalette.value = true;
      },
    },

    meta_s: {
      usingInput: true,
      handler: () => {
        if (props.hasSelectSurvey) {
          isOpenSurveysPalette.value = true;
        }
      },
    },
  });

  const isOpenSampleMessagesPalette = ref(false);
  const onSelectSampleMessage = (sample: SampleMessage) => {
    emits("update:modelValue", sample.text);
    isOpenSampleMessagesPalette.value = false;
  };

  const selectedSurvey = ref<any>(null);
  const isOpenSurveysPalette = ref(false);
  const onSelectSurvey = (survey: any) => {
    selectedSurvey.value = survey;
    isOpenSurveysPalette.value = false;
  };

  watch(
    () => selectedSurvey.value,
    (newValue) => {
      emits("select:survey", newValue);
    },
  );

  watch(
    () => props.survey,
    (newValue) => {
      selectedSurvey.value = newValue;
    },
    {
      immediate: true,
    },
  );

  onMounted(() => {
    inputBox.value?.textarea?.focus();
  });
</script>
