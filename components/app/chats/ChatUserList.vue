<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useChatsStore } from "~/stores/app/chats";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useCasesStore } from "~/stores/app/cases";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);

  const route = useRoute();
  const casesStore = useCasesStore();
  const { handlingCasesForNavigation, loadings, caseDetail, handlingCases } =
    storeToRefs(casesStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const chatsStore = useChatsStore();

  const dataForLoading = Array.from({ length: 10 }, (_, i) => ({
    label: "",
  }));

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        const { caseId } = route.query;
        if (caseId) {
          casesStore.fetchCaseDetail(caseId as string);
        }
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
  watch(
    () => route.query.caseId,
    (caseId) => {
      const isExists = handlingCases.value.find(
        (handlingCase) => handlingCase.caseId === caseId,
      );
      if (caseId) {
        if (!isExists) {
          casesStore.fetchCaseDetail(caseId as string);
        } else {
          caseDetail.value = isExists;
        }

        casesStore.syncChat(caseId as string);
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<template>
  <UVerticalNavigation
    v-if="loadings['fetchHandlingCases']"
    :links="dataForLoading"
    :ui="{ size: 'text-sm' }"
  >
    <template #avatar>
      <USkeleton
        class="h-6 w-6"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-800',
        }"
      />
    </template>
    <template #default>
      <USkeleton
        v-if="!isSubNavigationMini"
        class="h-4 w-full"
        :ui="{
          background: 'bg-gray-200 dark:bg-gray-800',
        }"
      />
    </template>
  </UVerticalNavigation>
  <template v-else>
    <UVerticalNavigation
      v-if="handlingCasesForNavigation.length"
      :links="handlingCasesForNavigation"
      :ui="{ size: 'text-xs', active: 'bg-primary-300' }"
    >
      <template #badge="{ link }">
        <div class="flex justify-end items-end flex-1">
          <UChip
            :show="link.unReadMessages?.length > 0"
            color="red"
            :text="link.unReadMessages?.length"
            size="md"
          >
            <UBadge size="xs" color="white" variant="solid">{{
              link.badge
            }}</UBadge>
          </UChip>
        </div>
      </template>
      <template #avatar="{ link }">
        <UTooltip
          :text="link.label"
          :popper="{ arrow: true, placement: 'right' }"
          :prevent="!isSubNavigationMini"
        >
          <UChip
            size="md"
            position="bottom-right"
            inset
            :ui="{ base: '-mx-1 -my-1 rounded-none ring-0', background: '' }"
          >
            <UAvatar
              v-bind="link.avatar"
              size="xs"
              loading="lazy"
              :ui="{
                rounded: 'rounded-lg',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
                chip: {
                  size: {
                    xs: 'h-2 w-2',
                  },
                },
              }"
            />

            <template #content>
              <span class="relative flex h-3 w-3">
                <span
                  v-if="link.unReadMessages?.length"
                  class="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"
                ></span>
                <component
                  :is="getSNSIconComponent(link.channel)"
                  class="h-3 w-3"
                />
              </span>
            </template>
          </UChip>
        </UTooltip>
      </template>
    </UVerticalNavigation>
    <div v-else>
      <div
        v-if="isSubNavigationMini"
        class="h-full flex flex-col justify-center items-center mt-2"
      >
        <UButton
          icon="i-heroicons-plus"
          size="sm"
          color="primary"
          square
          variant="soft"
          @click="navigateTo('/app')"
        />
      </div>
      <div
        v-else
        class="h-full flex flex-col justify-center items-center pt-10"
      >
        <UAvatar icon="i-heroicons-users" size="3xl" />
        <div class="text-xs font-thin mt-2">相談中のユーザーはいません</div>
        <div class="text-xs font-thin mt-2">
          <UButton
            icon="i-heroicons-plus"
            size="xs"
            color="primary"
            square
            label="相談をはじめる"
            @click="navigateTo('/app')"
          />
        </div>
      </div>
    </div>
  </template>
</template>
