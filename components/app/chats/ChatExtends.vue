<template>
  <div class="flex px-4 py-2 bg-gray-200 dark:bg-gray-950 h-full">
    <UTabs
      :key="tabs.length"
      v-model="selected"
      :items="tabs"
      class="w-full overflow-y-auto"
      :ui="{
        container: 'flex-1',
        base: 'flex-1',
      }"
    >
      <template #default="{ item }">
        <div class="flex items-center gap-2 relative truncate">
          <UIcon :name="item.icon" class="w-4 h-4 flex-shrink-0" />
          <span class="truncate">{{ item.label }}</span>
        </div>
      </template>
      <template #item="{ item }">
        <div v-if="item.key === 'case-information'">
          <CaseInformation :case="caseDetail" :maxTags="20" />
        </div>
        <UCard
          v-if="item.key === 'chat-histories'"
          :ui="{
            wrapper: 'h-full',
            body: {
              padding: '!py-2 !px-2',
            },
          }"
        >
          <ChatHistories :case="caseDetail" />
        </UCard>
        <div v-if="item.key === 'survey-results'">
          <CaseSurveyResults :case="caseDetail" />
        </div>
        <div v-if="item.key === 'wizard-results'">
          <CaseWizardResults :case="caseDetail" />
        </div>
      </template>
    </UTabs>
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useChatsStore } from "~/stores/app/chats";
  import { useCasesStore } from "~/stores/app/cases";
  const casesStore = useCasesStore();
  const chatsStore = useChatsStore();
  const { chattingUser, currentChattingUser } = storeToRefs(chatsStore);
  const { caseDetail, loadings } = storeToRefs(casesStore);
  const tabs = computed(() => {
    const _tabs = [
      {
        key: "case-information",
        label: "ケースの情報",
        icon: "i-heroicons-information-circle",
      },
      {
        key: "chat-histories",
        label: "過去履歴",
        icon: "i-heroicons-clock",
      },
    ];
    if (caseDetail.value.surveyResults?.length) {
      _tabs.push({
        key: "survey-results",
        label: "アンケート結果",
        icon: "i-wpf-survey",
      });
    }

    if (caseDetail.value.wizardResults?.length) {
      _tabs.push({
        key: "wizard-results",
        label: "ウィザード結果",
        icon: "i-clarity-flow-chart-line",
      });
    }
    return _tabs;
  });

  const selected = computed({
    get() {
      return caseDetail.value?.chatExtendActiveTab || 0;
    },
    set(value) {
      caseDetail.value.chatExtendActiveTab = value;
    },
  });
</script>
