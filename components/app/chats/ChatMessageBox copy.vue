<template>
  <div>
    <UCard :ui="{ body: { padding: '!px-0 !pt-1 !pb-0' } }" class="relative">
      <UTextarea
        ref="inputBox"
        autoresize
        variant="none"
        placeholder="Message..."
        rows="1"
        class="pl-1.5 pr-1 max-h-[300px] overflow-auto mb-10"
        :ui="{
          padding: {
            sm: 'pb-0',
          },
        }"
      />
      <div
        class="absolute bottom-0 w-full flex flex-inline justify-between px-1 pb-1"
      >
        <div class="flex flex-inline">
          <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-clipboard-document-list"
            color="gray"
            :label="`定型文 (${metaSymbol}+S)`"
            :trailing="false"
            @click="isOpenSampleMessagesPalette = true"
          />
          <UDivider
            color="gray"
            orientation="vertical"
            :ui="{ wrapper: { base: 'w-fit mx-1 py-2' } }"
          />
          <!-- <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-ticket"
            color="gray"
            label="スタンプ"
            :trailing="false"
          />
          <UDivider
            color="gray"
            orientation="vertical"
            :ui="{ wrapper: { base: 'w-fit mx-1' } }"
          />
          <UButton
            size="xs"
            variant="ghost"
            icon="i-heroicons-photo"
            color="gray"
            label="画像"
            :trailing="false"
          /> -->
        </div>
        <div>
          <UButton
            size="md"
            variant="ghost"
            icon="i-heroicons-paper-airplane"
            color="primary"
            :label="$t('Send') + ` (${metaSymbol}+Enter)`"
            :trailing="false"
            @click="onSend"
          />
        </div>
      </div>
    </UCard>
    <ChatSampleMessagesPalette
      :is-open="isOpenSampleMessagesPalette"
      @close="isOpenSampleMessagesPalette = false"
      @select="onSelectSampleMessage"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useChatsStore } from "~/stores/app/chats";
  import { useCasesStore } from "~/stores/app/cases";
  import type { SampleMessage } from "~/types";
  const chatsStore = useChatsStore();
  const { currentChattingUser } = storeToRefs(chatsStore);
  const { metaSymbol } = useShortcuts();
  const inputBox = ref<{ textarea: HTMLTextAreaElement } | null>(null);
  const onSend = () => {
    chatsStore.sendMessage({
      isUserMessage: true,
      message: currentChattingUser.value.messageText,
    });
    currentChattingUser.value.messageText = "";
    console.log(
      "🚀 ~ file: ChatMessageBox.vue:87 ~ onSend ~ inputBox:",
      inputBox,
    );

    inputBox.value?.textarea?.focus();
  };

  defineShortcuts({
    meta_enter: {
      usingInput: true,
      handler: () => {
        onSend();
      },
    },
    meta_s: {
      usingInput: true,
      handler: () => {
        isOpenSampleMessagesPalette.value = true;
      },
    },
  });

  const isOpenSampleMessagesPalette = ref(false);
  const onSelectSampleMessage = (sample: SampleMessage) => {
    console.log(
      "🚀 ~ file: ChatMessageBox.vue:110 ~ onSelectSampleMessage ~ message:",
      sample,
    );
    currentChattingUser.value.messageText = sample.text;
    isOpenSampleMessagesPalette.value = false;
  };
</script>
