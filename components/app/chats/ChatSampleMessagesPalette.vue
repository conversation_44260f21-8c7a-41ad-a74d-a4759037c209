<script setup>
  import { storeToRefs } from "pinia";
  import { useSampleMessagesStore } from "~/stores/app/sample-messages";
  const props = defineProps({
    isOpen: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(["close", "select"]);
  const sampleMessagesStore = useSampleMessagesStore();
  const { loadings, sampleMessagesForPalette } =
    storeToRefs(sampleMessagesStore);

  const commandPaletteRef = ref();

  function onSelect(option) {
    emit("select", option);
  }

  const tabs = [
    {
      label: "アカウント共通",
      content: "This is the content shown for Tab1",
      key: "common",
    },
    {
      label: "自分のみ",
      content: "And, this is the content for Tab2",
      key: "personal",
    },
  ];

  sampleMessagesStore.fetchSampleMessages();
</script>

<template>
  <UModal
    :model-value="props.isOpen"
    @close="emit('close')"
    :ui="{ background: 'bg-white dark:bg-gray-900', ring: 'dark:ring-1 dark:ring-gray-600' }"
  >
    <UTabs :items="tabs" class="w-full">
      <template #default="{ item }">
        <div class="flex items-center gap-2 relative truncate">
          <UIcon :name="item.icon" class="w-4 h-4 flex-shrink-0" />

          <span class="truncate">{{ item.label }}</span>
        </div>
      </template>
      <template #item="{ item }">
        <UCommandPalette
          v-if="loadings['fetchSampleMessages']"
          loading
          :empty-state="{
            icon: 'i-heroicons-cloud-arrow-down-solid',
            label: $t('Loading...'),
            queryLabel: $t('Loading...'),
          }"
          :placeholder="$t('Loading...')"
        />

        <UCommandPalette
          v-if="
            !loadings['fetchSampleMessages'] && sampleMessagesForPalette.length
          "
          ref="commandPaletteRef"
          icon="i-mi-message"
          :groups="sampleMessagesForPalette(item.key === 'common')"
          :autoselect="false"
          :fuse="{ resultLimit: 100, fuseOptions: { threshold: 0.1 } }"
          :placeholder="$t('Search by name...')"
          :ui="{
            container: 'max-h-[50vh] hidden-scrollbar',
            group: {
              command: {},
            },
          }"
          @update:model-value="onSelect"
        >
          <template
            v-for="group in sampleMessagesForPalette(item.key === 'common')"
            v-slot:[`${group.label}-command`]="{ command }"
          >
            <UTooltip
              :text="command.text"
              :popper="{ placement: 'right' }"
              :ui="{
                wrapper: 'flex flex-wrap',
                base: 'text-wrap whitespace-break-spaces h-fit',
              }"
            >
              {{ command.label }}
            </UTooltip>
          </template>
        </UCommandPalette>
      </template>
    </UTabs>
  </UModal>
</template>
