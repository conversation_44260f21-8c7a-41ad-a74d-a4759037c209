<template>
  <div
    ref="chatMessagesList"
    class="overflow-y-auto h-auto flex-1 hidden-scrollbar space-y-4 scroll-smooth"
  >
    <div v-if="loading" class="px-4">
      <div v-for="i in 3" :key="i" class="flex flex-col space-y-4 mb-4">
        <div class="space-y-2">
          <USkeleton class="h-12 w-2/3" />
          <USkeleton class="h-4 w-32" />
        </div>
        <div class="flex flex-col items-end justify-end space-y-2">
          <USkeleton class="h-12 w-1/2" />
          <USkeleton class="h-4 w-32" />
        </div>
      </div>
    </div>
    <ChatMessage
      v-for="(message, index) in props.messages"
      v-else
      :key="index"
      :data="message"
      :surveyResults="props.surveyResults"
      @open-survey-result="emits('open-survey-result', $event)"
    />
  </div>
</template>

<script setup lang="ts">
  import type { CaseChatMessage } from "@/types";
  const chatMessagesList = ref();

  const props = defineProps<{
    messages: CaseChatMessage[];
    loading: boolean;
    userAvatar?: string;
    surveyResults?: any[];
  }>();
  const emits = defineEmits(["open-survey-result"]);
  const hasNewMessage = ref(false);

  const getCurrentScrollPosition = () => {
    if (chatMessagesList.value) {
      return chatMessagesList.value.scrollTop;
    }
    return 0;
  };

  watch(
    () => props.messages,
    () => {
      if (chatMessagesList.value) {
        // scroll to bottom
        nextTick(() => {
          // check if user is not scrolling up
          if (
            chatMessagesList.value.scrollTop + chatMessagesList.value.clientHeight >=
            chatMessagesList.value.scrollHeight - 200
          ) {
            chatMessagesList.value.scrollTop = chatMessagesList.value.scrollHeight;
          }

          // chatMessagesList.value.scrollTop =
          //   chatMessagesList.value.scrollHeight;
            
        });
      }
    },
    { immediate: true, deep: true },
  );

  onMounted(() => {
    nextTick(() => {
      if (chatMessagesList.value) {
        chatMessagesList.value.scrollTop = chatMessagesList.value.scrollHeight;
      }
    });
  });
</script>
