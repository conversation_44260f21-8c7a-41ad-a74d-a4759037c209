<template>
  <UDropdown
    :items="items"
    :ui="{
      item: { disabled: 'cursor-text select-text' },
      wrapper:
        block &&
        'w-full px-1.5 py-1.5 border rounded-md border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 shadow-sm hover:shadow-md hover:border-primary-600',
    }"
    :popper="{ placement: 'right-start', offsetDistance: 10 }"
  >
    <div class="flex flex-row items-center space-x-2">
      <UAvatar
        :ui="{
          rounded: 'rounded-xl',
          background: 'bg-primary-300 dark:bg-primary-700',
          placeholder: 'text-xl font-semibold text-gray-700 dark:text-gray-300',
        }"
        :src="user?.profileImage"
        :alt="user?.fullName"
        size="lg"
        :chip-color="getSocketStatusObject(appSocket?.status).color"
        chip-position="bottom-right"
        :chip-text="getSocketStatusObject(appSocket?.status).text"
      />
      <div v-if="block" class="flex flex-col">
        <span class="text-md font-semibold text-gray-700 dark:text-gray-300">
          {{ user?.fullName }}
        </span>
        <span
          v-if="user?.role"
          class="text-xs text-gray-500 dark:text-gray-400"
        >
          {{ $t(user?.role) }}
        </span>
      </div>
    </div>
    <template #item="{ item }">
      <span class="truncate">{{ item.label }}</span>

      <UIcon
        :name="item.icon"
        class="flex-shrink-0 h-4 w-4 text-gray-400 dark:text-gray-500 ms-auto"
      />
    </template>
    <template #account="{ item }">
      <div class="flex flex-row justify-between items-center w-full">
        <span class="text-md font-semibold text-gray-700 dark:text-gray-300">
          {{ user?.fullName }}
        </span>
        <UBadge
          size="xs"
          v-if="user?.role"
          :label="$t(user?.role)"
          :color="getCounselorRoleColor(user?.role)"
        />
      </div>
    </template>
    <template #dark-mode="{ item }">
      <div class="flex items-center justify-between w-full">
        <span class="truncate">{{ item.label }}</span>

        <BaseColorModeToggle />
      </div>
    </template>
  </UDropdown>
  <Teleport to="body">
    <AuthPasswordChangeDialog
      :show="isShowPasswordChangeDialog"
      @close="isShowPasswordChangeDialog = false"
    />
    <AuthProfileUpdateDialog
      :show="isShowProfileUpdateDialog"
      @close="isShowProfileUpdateDialog = false"
    />
  </Teleport>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAuthStore } from "~/stores/auth";
  import { useSocketStore } from "~/stores/socket";

  const props = defineProps<{
    block: boolean;
  }>();
  const authStore = useAuthStore();
  const socketStore = useSocketStore();

  const { appSocket } = storeToRefs(socketStore);
  const { user } = storeToRefs(authStore);
  const isShowPasswordChangeDialog = ref(false);
  const isShowProfileUpdateDialog = ref(false);
  const route = useRoute();

  const items = computed(() => {
    const menus = [
      {
        label: "プロファイル変更",
        icon: "i-heroicons-cog-8-tooth",
        click: () => (isShowProfileUpdateDialog.value = true),
      },
      {
        label: "パスワード変更",
        icon: "i-heroicons-lock-closed",
        click: () => (isShowPasswordChangeDialog.value = true),
      },
    ];

    if (user.value?.role === "admin") {
      if (route.name && route.name.includes("admin")) {
        menus.unshift({
          label: "相談アプリへ",
          icon: "i-heroicons-chat-bubble-left-right",
          click: () => navigateTo("/app") as any,
        });
      } else {
        menus.unshift({
          label: "管理者画面",
          icon: "i-heroicons-bolt",
          click: () => navigateTo("/admin") as any,
        });
      }
    }
    return [
      [
        {
          label: user.value?.fullName,
          slot: "account",
          disabled: true,
        },
      ],
      menus,
      [
        {
          label: "ダークモード",
          slot: "dark-mode",
        },
      ],
      [
        {
          label: "ログアウト",
          icon: "i-heroicons-arrow-left-on-rectangle",
          click: () => {
            const dialogsStore = useDialogsStore();
            dialogsStore.onOpenConfirmDialog({
              title: "ログアウトの確認",
              message: "ログアウトしてもよろしいですか？",
              confirmButtonText: "はい",
              cancelButtonText: "キャンセル",
              onConfirm: async () => {
                authStore.logout();
              },
            });
          },
        },
      ],
    ];
  });

  const wrapperClass = computed(() => {
    return "!border-red-800";
  });
</script>
