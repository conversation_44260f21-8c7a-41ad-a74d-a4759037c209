<template>
  <div
    class="bg-gray-50 dark:bg-gray-800 dark:border-gray-600 py-3 px-3 rounded-xl border hover:border-primary-400 transition-all duration-200 cursor-pointer"
    :class="{
      'border-primary-400': active,
    }"
    @click="emits('select', props)"
  >
    <div class="flex flex-row justify-between items-center">
      <div class="flex flex-row items-center space-x-3">
        <UIcon :name="icon" class="text-3xl" />
        <div class="flex flex-col">
          <div class="text-sm">{{ title }}</div>
          <div
            class="text-sm"
            :class="{
              'text-gray-500 font-light': !modelValue[isEnable],
              'text-primary-500 font-semibold': modelValue[isEnable],
            }"
          >
            {{ modelValue[isEnable] ? "ON" : "OFF" }}
          </div>
        </div>
      </div>
      <UToggle size="lg" v-model="modelValue[isEnable]" :disabled="disabled" />
    </div>
    <div v-if="modelValue[isEnable]" class="pt-2 ml-10">
      <UFormGroup label="メッセージ">
        <UTextarea
          ref="messageTextAreas"
          autoresize
          placeholder="メッセージを入力してください"
          v-model="modelValue[message]"
          :disabled="disabled"
        />
        <div v-if="hasSurvey">
          <div
            v-if="modelValue?.survey?.surveyId"
            class="mt-3 px-0 flex flex-col text-left space-y-1"
          >
            <div class="text-sm font-medium">アンケート</div>
            <div
              class="relative group border bg-gray-100 dark:bg-gray-900 dark:border-gray-600 cursor-pointer rounded-lg flex items-center justify-center h-14 w-14 hover:shadow-md"
            >
              <div
                v-if="!disabled"
                class="absolute hidden group-hover:shadow-lg -top-2.5 -right-2.5 h-6 w-6 group-hover:flex items-center justify-center border border-gray-400 rounded-full"
              >
                <UIcon
                  name="i-carbon-close-filled"
                  color="gray"
                  class="text-xl hover:shadow-lg text-red-500"
                  @click="
                    modelValue.survey = {
                      surveyId: '',
                      surveyName: '',
                    }
                  "
                />
              </div>

              <UIcon name="i-wpf-survey" color="gray" class="text-2xl" />
            </div>
            <div class="text-xs truncate">
              {{ modelValue?.survey?.surveyName }}
            </div>
          </div>
          <UTooltip v-else text="アンケートを選択" class="mt-3">
            <UButton
              v-if="!disabled"
              size="xs"
              variant="solid"
              icon="i-wpf-survey"
              color="gray"
              label="アンケート"
              :trailing="false"
              @click="isOpenSurveysPalette = true"
            />
          </UTooltip>
        </div>
      </UFormGroup>
    </div>
    <AppSurveysPalette
      :is-open="isOpenSurveysPalette"
      @close="isOpenSurveysPalette = false"
      @select="onSelectSurvey"
    />
  </div>
</template>

<script setup lang="ts">
  import type { CustomerSetting } from "~/types";
  const props = defineProps({
    modelValue: {
      type: Object as PropType<{ [key: string]: any }>,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    isEnable: {
      type: String,
      default: false,
    },
    message: {
      type: String,
      default: "",
    },
    hasSurvey: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emits = defineEmits(["update:modelValue", "select"]);

  const messageTextAreas = ref<any>();
  const isOpenSurveysPalette = ref(false);
  const onSelectSurvey = (survey: any) => {
    props.modelValue.survey = {
      surveyId: survey.surveyId,
      surveyName: survey.surveyName,
    };
    isOpenSurveysPalette.value = false;
  };
  watch(
    () => props.modelValue.isEnable,
    (value) => {
      if (value) {
        nextTick(() => {
          messageTextAreas.value?.textarea?.focus();
        });
      }
    },
    { immediate: true, deep: true },
  );
</script>
