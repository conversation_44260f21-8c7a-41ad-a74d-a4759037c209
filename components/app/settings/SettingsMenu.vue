<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const userPermissions = usePermissions();
  const links = [
    {
      name: "app-settings-accounts",
      icon: "i-heroicons-user-circle",
      label: "アカウント設定",
      to: "/app/settings/accounts",
    },
    {
      name: "app-settings-automatic-chats",
      icon: "i-heroicons-chat-bubble-left-ellipsis",
      label: "自動チャット設定",
      to: "/app/settings/automatic-chats",
    },
    {
      name: "app-settings-counseling-term",
      icon: "i-heroicons-calendar-days",
      label: "相談期間",
      to: "/app/settings/counseling-term",
    },
    {
      name: "app-settings-segment-delivery",
      icon: "i-heroicons-inbox-arrow-down",
      label: "セグメント配信",
      to: "/app/settings/segment-delivery",
    },
    {
      name: "app-settings-monitoring-keywords",
      icon: "i-heroicons-beaker",
      label: "監視キーワード",
      to: "/app/settings/monitoring-keywords",
    },
    {
      name: "app-settings-tags",
      icon: "i-heroicons-hashtag",
      label: "相談分類管理",
      to: "/app/settings/tags",
    },
    {
      name: "app-settings-sample-messages",
      icon: "i-heroicons-command-line",
      label: "定型文",
      to: "/app/settings/sample-messages",
    },
  ];

  const menuList = computed(() => {
    return links
      .filter((obj) => userPermissions.value?.includes("read:" + obj.name))
      .map((link) => {
        const user = { ...link };
        if (isSubNavigationMini.value) {
          delete user.label;
          delete user.badge;
        }
        return user;
      });
  });
</script>

<template>
  <UVerticalNavigation :links="menuList" :ui="{ size: 'text-sm' }" />
</template>
