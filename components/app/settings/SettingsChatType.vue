<template>
  <div
    class="relative border-2 border-transparent rounded-lg h-full"
    :class="{
      '!border-primary-500': active,
    }"
  >
    <UIcon
      v-if="active"
      name="i-icon-park-twotone-check-one"
      class="text-lg absolute top-2 right-2 text-primary-500"
    />
    <div
      class="bg-gray-50 dark:bg-gray-800 dark:border-gray-600 py-3 px-3 rounded-lg border transition-all duration-200 cursor-pointer"
      @click="emits('select', modelValue)"
    >
      <div class="flex flex-col items-center space-y-2">
        <div
          v-if="modelValue.icons"
          class="ml-4"
          :class="[{ '-mb-1.5': size === 'lg' }, { 'mb-1.5': size === 'xl' }]"
        >
          <UIcon
            v-for="(icon, index) in modelValue.icons"
            :name="icon"
            class="-ml-5 hover:-ml-4 transition-all duration-150"
            :class="[
              modelValue.iconColor,
              { 'grayscale ': !active },
              { 'text-4xl': size === 'xl' },
              { 'text-3xl': size === 'lg' },
            ]"
          />
        </div>
        <UIcon
          v-else
          :name="modelValue.icon"
          :class="[
            modelValue.iconColor,
            { 'grayscale ': !active },
            { 'text-5xl': size === 'xl' },
            { 'text-3xl': size === 'lg' },
          ]"
        />
        <div class="items-center space-y-0">
          <div
            class="text-sm font-semibold"
            :class="{ 'text-primary-500': active, 'text-xs': size === 'lg' }"
          >
            {{ modelValue.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps({
    modelValue: {
      type: Object as PropType<SettingsChatType>,
      default: false,
    },
    active: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "xl",
    },
  });

  const emits = defineEmits(["select"]);
</script>
