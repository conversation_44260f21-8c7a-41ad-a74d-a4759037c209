<template>
  <div class="flex flex-col space-y-1">
    <FormRadio :elementId="tagId" :label="tagName" :options="props.options" :disabled="readonly"/>
    <div class="pt-4 pb-3">
      <UDivider />
    </div>
    <div>
      <div class="text-xs font-semibold mb-3">選択オプション</div>
      <div class="flex flex-col space-y-3">
        <draggable
          class="dragArea list-group w-full flex flex-col space-y-3"
          tag="transition-group"
          handle=".handle"
          :component-data="{
            tag: 'ul',
            type: 'transition-group',
            name: !drag ? 'flip-list' : null,
          }"
          v-model="list"
          v-bind="dragOptions"
        >
          <template #item="{ element, index }">
            <div class="flex items-center space-x-2">
              <UIcon
                name="i-nimbus-drag-dots"
                class="handle text-xl hover:cursor-pointer text-gray-500"
              />
              <UInput v-model="options[index]" class="flex-1"> </UInput>
              <UButton
                icon="i-heroicons-trash"
                size="xs"
                color="red"
                variant="soft"
                @click="options.splice(index, 1)"
              />
            </div>
          </template>
        </draggable>
        <div class="text-center">
          <UButton
            icon="i-heroicons-plus"
            size="xs"
            color="primary"
            square
            variant="soft"
            @click="emits('add:option')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import draggable from "vuedraggable";

const props = defineProps({
  tagId: String,
  tagName: {
    type: String,
    default: "<相談分類名>",
  },
  options: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  }
});

const emits = defineEmits(["add:option", "update:options"]);

const list = computed({
  get: () => props.options,
  set: (value) => emits("update:options", value),
});

const selected = ref();
const drag = ref(false);
const dragOptions = computed(() => ({
  animation: 200,
  group: "description",
  disabled: false,
  ghostClass: "ghost",
}));
</script>

<style>
.button {
  margin-top: 35px;
}

.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.list-group {
  min-height: 20px;
}

.list-group-item {
  cursor: move;
}

.list-group-item i {
  cursor: pointer;
}
</style>
