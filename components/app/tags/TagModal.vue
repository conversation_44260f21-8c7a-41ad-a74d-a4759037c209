<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              {{ isAddNew ? $t("相談分類作成") : $t("相談分類編集") }}
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div class="space-y-4">
          <div class="flex items-start space-x-4">
            <UFormGroup
              class="flex-1"
              :label="$t('相談分類名')"
              name="tagName"
              required
            >
              <UInput v-model="state.tagName" />
            </UFormGroup>
            <UFormGroup
              :label="$t('フォームテンプレート')"
              name="formTemplateId"
              required
            >
              <USelect
                v-model="state.formType"
                :options="typeOptions"
                option-attribute="name"
              />
            </UFormGroup>
          </div>
          <UDivider />

          <div v-if="state.formType" class="flex flex-col space-y-1">
            <label class="text-sm"> フォームのプレビュー </label>
            <div
              class="border border-gray-300 border-dashed rounded-md px-6 pt-4 pb-5 bg-gray-50 dark:bg-gray-800 dark:border-gray-500"
            >
              <component
                :is="tagFormTypeElement[state.formType]"
                :tagId="state.tagId"
                :tagName="state.tagName"
                :options="state.options"
                @add:option="onAddOption"
                @update:options="onUpdateOptions"
              />
            </div>
          </div>
        </div>
        <template #footer>
          <div class="text-right">
            <UButton type="submit" class="px-4">
              {{ isAddNew ? $t("相談分類作成") : $t("相談分類編集") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import type { CaseTag } from "~/types";
  import { TagFormType } from "~/types/enums.d";
  import TagText from "./TagText.vue";
  import TagTextAndPulldown from "./TagTextAndPulldown.vue";
  import TagPulldown from "./TagPulldown.vue";
  import TagRadio from "./TagRadio.vue";
  import TagCheckbox from "./TagCheckbox.vue";
  import { cloneDeep } from "lodash";

  const tagFormTypeElement: TagFormTypeElement = {
    [TagFormType.TEXT]: TagText,
    [TagFormType.TEXT_AND_PULLDOWN]: TagTextAndPulldown,
    [TagFormType.PULLDOWN]: TagPulldown,
    [TagFormType.RADIO]: TagRadio,
    [TagFormType.CHECKBOX]: TagCheckbox,
  };
  const { object, string } = useYup();
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
    isAddNew: Boolean,
    data: {
      type: Object as PropType<CaseTag>,
      default: () => ({}),
    },
  });

  const typeOptions = Object.values(TagFormType).map((value) => ({
    name: t(value),
    value,
  }));

  const schema = object().shape({
    tagName: string().required(),
    formType: string().required(),
    formTemplate: string(),
  });

  const initialState: CaseTag = {
    tagName: "",
    formType: TagFormType.TEXT,
    options: [],
  };
  let state = reactive<CaseTag>(cloneDeep(initialState));

  watch(
    () => props.data,
    (data) => {
      if (data) {
        Object.assign(state, cloneDeep(data));
      } else {
        Object.assign(state, cloneDeep(initialState));
      }
    },
    { immediate: true, deep: true },
  );

  const emit = defineEmits(["close", "update", "addNew"]);

  function onSubmit() {
    if (props.isAddNew) {
      emit("addNew", cloneDeep(state));
    } else {
      emit("update", cloneDeep(state));
    }
    Object.assign(state, cloneDeep(initialState));
    emit("close");
  }

  function onAddOption() {
    if (!state.options) {
      state.options = [];
    }
    state.options?.push("");
  }

  function onUpdateOptions(options: string[]) {
    state ? (state.options = options) : null;
  }
</script>
