<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { useSurveysStore } from "~/stores/app/surveys";
  const surveysStore = useSurveysStore();
  const { allSurveys, loadings } = storeToRefs(surveysStore);
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const props = defineProps({
    isOpen: {
      type: Boolean,
      default: false,
    },
    actionLabel: {
      type: String,
      default: "を選択",
    },
  });

  const emit = defineEmits(["close", "select"]);

  const commandPaletteRef = ref();

  function onSelect(option: any) {
    console.log("🚀 ~ onSelect ~ option:", option);
    emit("select", option);
  }

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        surveysStore.fetchAllSurveys(
          currentCustomer.value.customerId as string,
        );
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>

<template>
  <UModal
    :model-value="props.isOpen"
    @close="emit('close')"
    :ui="{
      background: 'bg-white dark:bg-gray-900',
      ring: 'dark:ring-1 dark:ring-gray-600',
    }"
  >
    <UCommandPalette
      v-if="loadings['fetchAllSurveys']"
      loading
      :empty-state="{
        icon: 'i-heroicons-cloud-arrow-down-solid',
        label: $t('Loading...'),
        queryLabel: $t('Loading...'),
      }"
      :placeholder="$t('Loading...')"
    />

    <UCommandPalette
      v-if="!loadings['fetchAllSurveys']"
      by="surveyId"
      ref="commandPaletteRef"
      :groups="[{ key: 'surveys', commands: allSurveys || [] }]"
      :autoselect="false"
      :fuse="{
        resultLimit: 100,
        fuseOptions: {
          threshold: 0.1,
          ignoreLocation: true,
          includeMatches: true,
          keys: ['surveyName'],
        },
      }"
      :placeholder="$t('Search by name...')"
      :ui="{
        container: 'max-h-[50vh] hidden-scrollbar',
        group: {
          command: {
            avatar: {
              size: '3xs',
            },
            base: 'group/command',
          },
        },
      }"
      @update:model-value="onSelect"
    >
      <template #surveys-icon="{ command }">
        <UIcon name="i-wpf-survey" />
      </template>
      <template #surveys-command="{ command }">
        <div class="flex items-center justify-between w-full space-x-4">
          <div>{{ command.surveyName }}</div>
          <div class="text-gray-400 text-sm hidden group-hover/command:block">
            {{ actionLabel }}
          </div>
        </div>
      </template>
    </UCommandPalette>
  </UModal>
</template>
