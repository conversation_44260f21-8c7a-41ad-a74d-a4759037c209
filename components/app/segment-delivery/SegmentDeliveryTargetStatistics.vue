<template>
  <div class="relative">
    <div class="font-semibold text-sm mb-3">ターゲット統計</div>
    <div class="relative">
      <DoughnutChart
        :chartData="chartData"
        :key="primaryColor"
        :options="options"
      />
      <div
        class="absolute top-1/2 w-full text-center text-3xl text-primary-600"
      >
        <div
          v-if="loading"
          class="flex flex-row items-center space-x-2 justify-center"
        >
          <UIcon name="i-heroicons-arrow-path" class="animate-spin" />
          <div class="text-lg">ロード中...</div>
        </div>
        <div v-else-if="targetsPercent >= 0">{{ targetsPercent }}%</div>
        <div v-else class="text-gray-400">未設定</div>
      </div>
    </div>
    <div class="text-center text-sm font-light py-4">
      <div v-if="loading" class="font-light">しばらくお待ちください</div>
      <div v-else class="">
        <div v-if="targetsPercent === 100">
          すべての相談者が対象になります
          {{ targetsPercent === 100 ? "" : `` }}
        </div>
        <div v-else>
          約
          <span class="text-primary-600 text-lg font-bold">{{
            targetsCount
          }}</span>
          / <span>{{ totalCount }}</span> 人が対象になります
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { DoughnutChart } from "vue-chart-3";
import { Chart, registerables } from "chart.js";
Chart.register(...registerables);

const props = defineProps<{
  targetsCount: number;
  totalCount: number;
  loading?: boolean;
}>();

const targetsPercent = computed(() => {
  if (props.totalCount === 0) return -1;
  return Math.round((props.targetsCount / props.totalCount) * 100);
});

// get primary color tailwind
const appConfig = useAppConfig();
const primaryColor = ref(appConfig.ui.primary);

watch(
  () => appConfig.ui.primary,
  (primary) => {
    primaryColor.value = primary;
  }
);

const chartData = computed(() => {
  const notTargetCount = props.totalCount - props.targetsCount;

  return {
    labels: ["配信対象者", "対象外"],
    datasets: [
      {
        data: [props.targetsCount, notTargetCount ],
        backgroundColor: [getColor(primaryColor.value), getColor("grey-200")],
      },
    ],
  };
});

const options = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {},
  cutout: 90,
};
</script>
