<template>
  <div>
    <div class="w-2/3 mx-auto flex flex-inline space-x-4">
      <UInput
        color="white"
        class="flex-1"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="md"
        :trailing="false"
        placeholder="キーワード(セグメント配信名)..."
        v-model="searchConditions.segmentName"
      />
      <UButton
        class="px-8"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="sm"
        color="primary"
        variant="solid"
        label="検索"
        :trailing="false"
        @click="segmentDeliveryStore.fetchData"
      />
    </div>
    <UAccordion :items="items" variant="link">
      <template #default="{ item, open }">
        <UButton
          color="gray"
          variant="link"
          class="dark:border-gray-700 w-fit"
          :ui="{ rounded: 'rounded-none', padding: { sm: 'p-3' } }"
        >
          <template #leading>
            <UIcon
              :name="item.icon"
              class="w-3 h-3 text-gray-500 dark:text-gray-900 transition-transform duration-200"
              :class="{ 'rotate-90': open }"
            />
          </template>
          <span class="truncate">{{ item.label }}</span>
        </UButton>
      </template>

      <template #getting-started>
        <UCard>
          <div class="grid grid-cols-12 space-x-4 mt-4">
            <UFormGroup label="セグメント配信ID" class="col-span-6">
              <UInput color="white" v-model="searchConditions.segmentId" />
            </UFormGroup>
            <UFormGroup label="この日から" class="col-span-3">
              <BaseDatePicker v-model="startDate" block color="white" />
            </UFormGroup>
            <UFormGroup label="この日まで" class="col-span-3">
              <BaseDatePicker v-model="endDate" block color="white" />
            </UFormGroup>
          </div>
          <div class="grid grid-cols-4 space-x-4 mt-2">
            <UFormGroup class="mt-2 col-span-2" label="セグメント配信の状態">
              <div class="flex flex-inline justify-between mt-2">
                <UCheckbox
                  color="primary"
                  label="未対応"
                  v-model="searchConditions.includeBeforeStart"
                />
                <UCheckbox
                  color="primary"
                  label="送信中"
                  v-model="searchConditions.includeInProgress"
                />
                <UCheckbox
                  color="primary"
                  label="送信完了"
                  v-model="searchConditions.includeResolved"
                />
                <UCheckbox
                  color="primary"
                  label="エラー"
                  v-model="searchConditions.includeError"
                />
              </div>
            </UFormGroup>
          </div>
          <template #footer>
            <div class="flex flex-inline justify-center space-x-4">
              <UButton
                class="px-5"
                icon="i-heroicons-arrow-uturn-left-solid"
                size="sm"
                color="white"
                variant="solid"
                label="リセット"
                :trailing="false"
                @click="segmentDeliveryStore.resetSearchConditions"
              />
              <UButton
                class="px-8"
                icon="i-heroicons-magnifying-glass-20-solid"
                size="sm"
                color="primary"
                variant="solid"
                label="検索"
                :trailing="false"
                @click="segmentDeliveryStore.fetchData"
              />
            </div>
          </template>
        </UCard>
      </template>
    </UAccordion>
  </div>
</template>
<script setup lang="ts">
  import { useSegmentDeliveryStore } from "~/stores/app/segment-delivery";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { cloneDeep } from "lodash";
  const segmentDeliveryStore = useSegmentDeliveryStore();
  const { pagination, searchConditions, sortConditions } =
    storeToRefs(segmentDeliveryStore);
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const route = useRoute();
  const items = [
    {
      label: "高度な検索",
      icon: "i-heroicons-play-solid",
      defaultOpen: false,
      slot: "getting-started",
    },
  ];

  const startDate = computed({
    get: () => {
      return searchConditions.value.startDate
        ? new Date(searchConditions.value.startDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.startDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  const endDate = computed({
    get: () => {
      return searchConditions.value.endDate
        ? new Date(searchConditions.value.endDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.endDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  onMounted(async () => {
    //surveyResultsStore.fetchSurveyResults(searchConditions.value);
    const query = route.query;
    let _searchConditions, _pagination, _sortConditions;
    if (query) {
      _searchConditions = cloneDeep({
        ...searchConditions.value,
        ...query,
      });

      _searchConditions.segmentId = query.segmentId as string;

      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      segmentDeliveryStore.updateConditions(
        _searchConditions,
        _pagination,
        _sortConditions,
      );
    }
  });

  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page && currentCustomer.value?.customerId) {
        segmentDeliveryStore.fetchData();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy && currentCustomer.value?.customerId) {
        segmentDeliveryStore.fetchData();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        segmentDeliveryStore.fetchData();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>
