<template>
  <UModal :model-value="props.show" prevent-close :ui="{ width: '!max-w-3xl' }">
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!pt-0 !pb-4 !px-0 space-y-3',
        },
      }"
    >
      <template #header>
        <div class="flex items-center justify-between max-w-7xl">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            送信先対象一覧 ({{ targets?.length }}人)
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="emit('close')"
          />
        </div>
      </template>
      <div class="max-h-[75vh] overflow-y-auto block">
        <UTable
          :rows="targets"
          :columns="columns"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          class="w-full"
          :ui="{
            tr: {
              base: 'group hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer',
            },
            td: { base: 'truncate' },
            th: {
              size: 'text-xs',
              padding: 'px-0 pl-2  pt-4',
            },
          }"
        >
          <template #clientName-data="{ row }">
            <div class="flex items-center space-x-2">
              <div class="border-r pr-2">
                <component
                  :is="getSNSIconComponent(row.channel)"
                  class="h-5 w-5"
                />
              </div>
              <UAvatar size="xs" :src="row.pictureUrl" :alt="row.fullname" />
              <div>
                <ULink
                  inactive-class="text-primary-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                >
                  {{ row.fullname }}
                </ULink>
              </div>
            </div>
          </template>
          <template #gender-data="{ row }">
            <div>
              {{ $t(row.gender) }}
            </div>
          </template>
          <template #ageDecade-data="{ row }">
            <div>
              {{ getAgeDecadeLabel(row.ageDecade) }}
            </div>
          </template>
          <template #conveyed-data="{ row }">
            <div class="flex justify-center items-center max-w-sm whitespace-break-spaces">
              {{ row.conveyed }}
            </div>
          </template>
        </UTable>
      </div>
    </UCard>
  </UModal>
</template>

<script lang="ts" setup>
  import type { TableColumn } from "~/types";
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
    targets: Array,
  });

  const emit = defineEmits(["close"]);

  const columns: TableColumn[] = [
    {
      key: "clientName",
      label: t("Counselee"),
      sortable: true,
      class: "text-left",
    },
    {
      key: "gender",
      label: "性別",
      sortable: true,
      class: "text-left w-fit",
    },
    {
      key: "ageDecade",
      label: "年代",
      sortable: true,
      class: "text-left w-fit",
    },
    {
      key: "conveyed",
      label: "申し送り事項",
      sortable: true,
      class: "text-left max-w-[200px]",
    },
  ];
</script>
