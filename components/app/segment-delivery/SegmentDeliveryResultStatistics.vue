<template>
  <div class="relative">
    <div class="font-semibold text-sm mb-3">配信処理統計</div>
    <div class="relative">
      <DoughnutChart
        :chartData="chartData"
        :key="primaryColor"
        :options="options"
        class="w-full"
      />
      <div
        class="absolute top-1/2 w-full text-center text-3xl text-green-600"
      >
        <div v-if="deliverySuccessRate >= 0">{{ deliverySuccessRate }}%</div>
        <div v-else class="text-gray-400">未設定</div>
      </div>
    </div>
    <div class="text-center text-sm font-light py-4">
      <div class="">
        <div v-if="deliverySuccessRate === 100">
          すべての対象者に配信済みです
          {{ deliverySuccessRate === 100 ? "" : `` }}
        </div>
        <div v-else>
          <span class="text-green-600 text-lg font-bold">{{
            deliverySuccessCount
          }}</span>
          /
          <span>{{ deliveryScheduleCount }}</span>
          人にセグメント配信が完了しています。
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { DoughnutChart } from "vue-chart-3";
  import { Chart, registerables } from "chart.js";
  Chart.register(...registerables);

  const props = defineProps<{
    scheduledRecipientList: string[];
    recipientList: string[];
  }>();

  const deliverySuccessRate = computed(() => {
    if (deliveryScheduleCount.value === 0) return -1;
    return Math.round(
      (deliverySuccessCount.value / deliveryScheduleCount.value) * 100,
    );
  });

  const deliverySuccessCount = computed(() => {
    return props.recipientList?.length;
  });

  const deliveryScheduleCount = computed(() => {
    return props.scheduledRecipientList?.length;
  });

  // get primary color tailwind
  const appConfig = useAppConfig();
  const primaryColor = ref(appConfig.ui.primary);

  watch(
    () => appConfig.ui.primary,
    (primary) => {
      primaryColor.value = primary;
    },
  );

  const chartData = computed(() => {
    const notDeliveryCount =
      deliveryScheduleCount.value - deliverySuccessCount.value;

    return {
      labels: ["配信済み", "未配信"],
      datasets: [
        {
          data: [deliverySuccessCount.value, notDeliveryCount],
          backgroundColor: [getColor("green-500"), getColor("grey-200")],
        },
      ],
    };
  });

  const options = {
    responsive: true,
    maintainAspectRatio: true,
    plugins: {},
    cutout: '80%'
  };
</script>
