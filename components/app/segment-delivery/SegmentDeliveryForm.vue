<template>
  <div class="flex flex-col space-y-3">
    <UFormGroup label="配信名">
      <UInput placeholder="配信名" v-model="segmentDelivery.segmentName"/>
    </UFormGroup>
    <UFormGroup label="配信先対象の設定">
      <div class="pt-2 pb-2 px-6 border border-dashed rounded-lg bg-gray-50 dark:bg-gray-800 dark:border-gray-500">
        <div class="grid grid-cols-12 gap-4 mt-2">
          <UFormGroup label="検索範囲の開始時間" class="col-span-6">
            <BaseDatePicker v-model="startDate" block color="white" />
          </UFormGroup>
          <UFormGroup label="検索範囲の終了時間" class="col-span-6">
            <BaseDatePicker v-model="endDate" block color="white" />
          </UFormGroup>
          <UFormGroup label="ケースの状態" class="col-span-4">
            <USelectMenu
              v-model="segmentDelivery.filter.statuses"
              :options="caseStatus"
              @change="onFilterByStatus"
              multiple
              class="w-full"
              value-attribute="value"
              option-attribute="label"
            >
              <template #label>
                <template v-if="selectedStatus.length">
                  <span class="flex items-center -space-x-1">
                    <span
                      v-for="label of selectedStatus"
                      :key="label.value"
                      class="flex-shrink-0 w-3 h-3 mt-px rounded-full"
                      :class="`bg-${getCaseStatusColor(label.value)}-500`"
                    />
                  </span>
                  <span>{{ selectedStatus.length }}つの状態</span>
                </template>
              </template>
              <template #option="{ option: status }">
                <span
                  :class="[
                    `bg-${getCaseStatusColor(status.value)}-500`,
                    'inline-block h-3 w-3 flex-shrink-0 rounded-full',
                  ]"
                  aria-hidden="true"
                />
                <span class="truncate">{{ status.label }}</span>
              </template>
            </USelectMenu>
          </UFormGroup>
          <UFormGroup label="相談者の性別" class="col-span-4">
            <AppGendersMultiSelect v-model="segmentDelivery.filter.genders" />
          </UFormGroup>
          <UFormGroup label="相談者の年代" class="col-span-4">
            <AppAgeDecadesMultiSelect
              v-model="segmentDelivery.filter.ageDecades"
            />
          </UFormGroup>
        </div>
        <!-- <UDivider class="mt-4" />

        <div class="mt-4 flex flex-row justify-between items-center">
          <div class="text-sm font-semibold">相談分類</div>
          <UButton
            size="xs"
            label="相談分類追加"
            variant="solid"
            color="gray"
            icon="i-heroicons-plus-circle-solid"
            @click="isOpenCaseTagsPalette = true"
          />
        </div> -->
        <!-- <div class="flex flex-col space-y-3 pt-3">
          <div
            class="relative group"
            v-for="(element, index) in segmentDelivery?.filter?.tags"
          >
            <component
              :is="FormElements[element.formTemplateId]"
              :elementId="element.tagId"
              :label="element.tagName"
              :options="JSON.parse(element.formTemplate || '{}').options"
            />
            <div
              class="hidden absolute -top-1 right-0 group-hover:flex items-start"
            >
              <UButton
                icon="i-heroicons-trash-solid"
                size="2xs"
                color="red"
                variant="soft"
                :trailing="false"
                label="削除"
                @click="removeTag(element.tagId)"
              />
            </div>
            <UDivider
              v-if="index !== segmentDelivery?.filter?.tags?.length - 1"
              class="mt-3"
            />
          </div>
        </div> -->
        <UDivider class="mt-3" />
        <div class="mt-4 mb-2 flex flex-row justify-between items-center">
          <UButton
            class="px-5"
            icon="i-heroicons-arrow-uturn-left-solid"
            size="sm"
            color="white"
            variant="solid"
            label="配信先対象をリセット"
            :trailing="false"
            @click="segmentDeliveryStore.resetFilter()"
          />
          <UButton
            icon="i-heroicons-arrow-right"
            class="px-6"
            trailing
            variant="solid"
            size="sm"
            @click="segmentDeliveryStore.fetchTargets()"
          >
            配信先対象を確認
          </UButton>
        </div>
      </div>
    </UFormGroup>

    <UFormGroup label="送信内容">
      <ChatMessageBox
        :hasSendButton="false"
        class="px-0 pt-0"
        v-model="segmentDelivery.content.text"
        @select:survey="segmentDeliveryStore.selectSurvey($event)"
      />
    </UFormGroup>
    <!-- <AppCaseTagsPalette
      :is-open="isOpenCaseTagsPalette"
      @close="isOpenCaseTagsPalette = false"
      @select="onSelectCaseTag"
    /> -->
  </div>
</template>
<script setup lang="ts">
  import { CaseStatus } from "~/types/enums.d";
  import type { CaseTag } from "~/types";
  import { useSegmentDeliveryStore } from "~/stores/app/segment-delivery";
  import { storeToRefs } from "pinia";
  const { FormElements } = useFormTagElements();
  const segmentDeliveryStore = useSegmentDeliveryStore();
  const { segmentDelivery } = storeToRefs(segmentDeliveryStore);
  const { t } = useI18n();
  const { genderOptions, ageOptions } = useConstants();
  const startDate = computed({
    get: () => {
      return segmentDelivery.value.filter.startDate
        ? new Date(segmentDelivery.value.filter.startDate)
        : "";
    },
    set: (value: Date) => {
      segmentDelivery.value.filter.startDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  const endDate = computed({
    get: () => {
      return segmentDelivery.value.filter.endDate
        ? new Date(segmentDelivery.value.filter.endDate)
        : "";
    },
    set: (value: Date) => {
      segmentDelivery.value.filter.endDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  const selectedStatus = computed(() => {
    return caseStatus.filter((status) =>
      segmentDelivery.value.filter.statuses?.includes(status.value),
    );
  });
  const caseStatus = Object.values(CaseStatus).map((status) => ({
    label: t(`caseStatus.${status}`),
    value: status,
  }));

  const onFilterByStatus = (status: CaseStatus[]) => {
    segmentDelivery.value.filter.statuses = status;
  };

  const isOnline = ref(false);
  const toast = useToast();
  const isOpenCaseTagsPalette = ref(false);
</script>
