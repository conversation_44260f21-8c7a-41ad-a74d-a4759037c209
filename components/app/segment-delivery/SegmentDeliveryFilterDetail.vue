<template>
  <UModal :model-value="props.show" :ui="{ width: '!max-w-4xl' }">
    <UCard
      v-if="segment"
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!pt-0 !pb-4 !px-0 space-y-3',
        },
      }"
    >
      <template #header>
        <div class="flex items-center justify-between max-w-7xl">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white flex space-x-2 items-center"
          >
            <span>
              {{ segment?.segmentName }}
            </span>
            <UBadge :color="getSegmentDeliveryStatusColor(segment?.status)">
              {{ $t(segment?.status) }}
            </UBadge>
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="emit('close')"
          />
        </div>
      </template>
      <div class="grid grid-cols-12">
        <div class="p-6 col-span-8">
          <div class="mb-2 text-sm font-semibold">配信先対象の条件</div>
          <div class="flex flex-col space-y-2">
            <div class="text-sm flex items-center justify-between">
              <div class="font-medium">検索範囲の開始時間</div>
              <div>
                {{
                  segment?.filter?.startDate
                    ? formatDate(
                        new Date(segment?.filter?.startDate),
                        "YYYY年MM月DD日",
                      )
                    : "条件なし"
                }}
              </div>
            </div>
            <div class="text-sm flex items-center justify-between">
              <div class="font-medium">検索範囲の終了時間</div>
              <div>
                {{
                  segment?.filter?.endDate
                    ? formatDate(
                        new Date(segment?.filter?.endDate),
                        "YYYY年MM月DD日",
                      )
                    : "条件なし"
                }}
              </div>
            </div>
            <div class="text-sm flex items-center justify-between">
              <div class="font-medium">ケースの状態</div>
              <div class="flex space-x-3">
                <div
                  v-if="segment?.filter?.statuses?.length"
                  v-for="status in segment?.filter?.statuses"
                  class="flex flex-row space-x-1 items-center"
                >
                  <span
                    :class="[
                      `bg-${getCaseStatusColor(status)}-500`,
                      'inline-block h-3 w-3 flex-shrink-0 rounded-full',
                    ]"
                    aria-hidden="true"
                  />
                  <span class="truncate">{{ $t(`caseStatus.${status}`) }}</span>
                </div>
                <span v-else> 条件なし </span>
              </div>
            </div>
            <div class="text-sm flex items-center justify-between">
              <div class="font-medium">相談者の性別</div>
              <div class="flex space-x-3">
                <div
                  v-if="segment?.filter?.genders?.length"
                  v-for="gender in segment?.filter?.genders"
                  class="flex flex-row space-x-1 items-center"
                >
                  <span class="truncate">{{ $t(gender) }}</span>
                </div>
                <span v-else> 条件なし </span>
              </div>
            </div>
            <div class="text-sm flex items-center justify-between">
              <div class="font-medium">相談者の年代</div>
              <div class="flex space-x-3">
                <div
                  v-if="segment?.filter?.ageDecades?.length"
                  v-for="ageDecade in segment?.filter?.ageDecades"
                  class="flex flex-row space-x-1 items-center"
                >
                  <span class="truncate">{{
                    getAgeDecadeLabel(ageDecade)
                  }}</span>
                </div>
                <span v-else> 条件なし </span>
              </div>
            </div>
          </div>
          <UDivider class="my-4" />
          <div class="mb-2 text-sm font-semibold">配信内容</div>
          <div>
            <div class="flex flex-col space-y-2">
              <div class="text-sm flex flex-col">
                <div class="font-medium">メッセージ</div>
                <div
                  class="break-words whitespace-break-spaces p-4 bg-gray-50 rounded-lg border text-sm"
                >
                  {{ segment?.content?.text }}
                </div>
              </div>
              <div v-if="segment?.survey" class="text-sm flex flex-col space-y-2">
                <div class="font-medium">アンケート</div>
                <div class="group w-fit" @click="openSurvey">
                  <div
                    class="relative group border border-gray-500 group-hover:border-primary-500 bg-gray-100 cursor-pointer rounded-lg flex items-center justify-center h-14 w-14"
                  >
                    <UIcon
                      name="i-wpf-survey"
                      color="gray"
                      class="text-3xl text-gray-500 group-hover:text-primary-500"
                    />
                  </div>
                  <span
                    class="group-hover:cursor-pointer group-hover:text-primary-500"
                  >
                    {{ segment?.survey?.surveyName }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="p-6 col-span-4 border-l">
          <SegmentDeliveryResultStatistics
            :scheduledRecipientList="segment?.scheduledRecipientList"
            :recipientList="segment?.recipientList"
          />
        </div>
      </div>
    </UCard>
  </UModal>
</template>

<script lang="ts" setup>
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
    segment: Object,
  });

  const emit = defineEmits(["close"]);

  const openSurvey = () => {
    const link = getLinkSurvey(
      props.segment?.survey?.surveyId,
      props.segment?.customerId,
    );
    window.open(link, "__blank");
  };
</script>
