<template>
  <USelectMenu
    :options="counselorsForPalette"
    :value="modelValue"
    @update:modelValue="$emit('update:modelValue', $event)"
    value-attribute="id"
    option-attribute="label"
    placeholder="選択してください"
  >
    <template #leading>
      <UAvatar
        v-if="selectedCounselor"
        v-bind="selectedCounselor?.avatar"
        size="3xs"
        class="mx-0.5"
      />
      <UIcon v-else name="i-heroicons-user-circle" class="w-4 h-4 mx-0.5" />
    </template>
    <template #label>
      <span class="truncate">{{
        selectedCounselor?.label || "選択してください"
      }}</span>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/app/counselors";
  const counselorsStore = useCounselorsStore();
  const { counselorsForPalette } = storeToRefs(counselorsStore);
  const props = defineProps({
    modelValue: {
      type: String,
      default: false,
    },
  });

  const selectedCounselor = computed(() => {
    return counselorsForPalette.value?.find(
      (counselor) => counselor.id === props.modelValue,
    );
  });
</script>
