<script setup>
  import { storeToRefs } from "pinia";
  import { useTagsStore } from "~/stores/app/tags";
  const props = defineProps({
    isOpen: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(["close", "select"]);
  const tagsStore = useTagsStore();
  const { loadings, caseTagsForPalette } = storeToRefs(tagsStore);

  const commandPaletteRef = ref();

  function onSelect(option) {
    emit("select", option);
  }

  onMounted(() => {
    if (!caseTagsForPalette.length) {
      tagsStore.fetchTags();
    }
  });
</script>

<template>
  <UModal
    :model-value="props.isOpen"
    @close="emit('close')"
    :ui="{
      background: 'bg-white dark:bg-gray-900',
      ring: 'dark:ring-1 dark:ring-gray-600',
    }"
  >
    <UCommandPalette
      v-if="loadings['fetchTags']"
      loading
      :empty-state="{
        icon: 'i-heroicons-cloud-arrow-down-solid',
        label: $t('Loading...'),
        queryLabel: $t('Loading...'),
      }"
      :placeholder="$t('Loading...')"
    />

    <UCommandPalette
      v-if="!loadings['fetchTags']"
      ref="commandPaletteRef"
      :groups="caseTagsForPalette"
      :autoselect="false"
      :fuse="{ resultLimit: 100, fuseOptions: { threshold: 0.1 } }"
      placeholder="相談分類名で検索"
      :ui="{
        container: 'max-h-[50vh] hidden-scrollbar',
        group: {
          command: {
            avatar: {
              size: '3xs',
            },
          },
        },
      }"
      @update:model-value="onSelect"
    >
      <template #empty-state>
        <div class="flex flex-col items-center justify-center py-6 gap-3">
          <span class="text-sm"> 相談分類が登録されていません </span>
        </div>
      </template>
    </UCommandPalette>
  </UModal>
</template>
