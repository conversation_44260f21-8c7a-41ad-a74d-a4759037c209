<template>
  <NuxtLink :to="props.to">
    <div
      class="cursor-pointer rounded-2xl group text-center justify-center flex flex-col items-center"
    >
      <div
        class="h-10 w-10 rounded-xl flex items-center justify-center text-sm group-hover:bg-primary-900 dark:group-hover:bg-gray-500"
        :class="{ 'bg-primary-900/80 backdrop-blur dark:bg-gray-500': props.isActive }"
      >
        <UIcon
          :name="iconState"
          class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
        />
        <div v-show="false">
          <UIcon
            name="i-heroicons-home"
            class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
          />
          <UIcon
            name="i-heroicons-document-chart-bar"
            class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
          />
          <UIcon
            name="i-heroicons-chat-bubble-left-right"
            class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
          />
          <UIcon
            name="i-heroicons-briefcase"
            class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
          />
          <UIcon
            name="i-heroicons-clipboard-document-list"
            class="text-[22px] group-hover:scale-125 transition ease-in-out duration-300"
          />
        </div>
      </div>
      <div class="text-[11px] mt-1 font-base">
        {{ props.label }}
      </div>
    </div>
  </NuxtLink>
</template>
<script setup lang="ts">
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  to: {
    type: String,
    default: "/app",
  },
});

const iconState = computed(() => {
  return props.isActive
    ? `${props.icon?.replace("-outline", "")}`
    : `${props.icon?.replace("-solid", "")}`;
});
</script>
