<template>
  <USelectMenu
    :options="allSurveys"
    v-model="selectedSurveys"
    value-attribute="surveyId"
    option-attribute="surveyName"
    placeholder="選択してください"
    :multiple="multiple"
    searchable
    searchable-placeholder="アンケート名でフィルター..."
    :ui="{
      option: {
        size: '!text-xs',
      },
    }"
  >
    <template #label>
      <template v-if="selectedSurveysObject.length">
        <span v-if="selectedSurveysObject.length === 1" class="truncate">{{
          selectedSurveysName
        }}</span>
        <span v-else class="truncate"
          >{{ selectedSurveysObject.length }}件が選択されています</span
        >
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          選択してください
        </span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useSurveysStore } from "~/stores/app/surveys";
  const surveysStore = useSurveysStore();
  const { allSurveys } = storeToRefs(surveysStore);
  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]> | PropType<string>,
      default: [],
    },
    multiple: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const selectedSurveys = computed({
    get() {
      return props.modelValue;
    },
    set(val: string) {
      // if val is already an array, remove it
      if (props.modelValue.includes(val)) {
        emits("update:modelValue", []);
      } else {
        emits("update:modelValue", val);
      }
    },
  });
  const selectedSurveysObject = computed(() => {
    return allSurveys.value?.filter((survey) =>
      selectedSurveys.value.includes(survey.surveyId || ""),
    );
  });

  const selectedSurveysName = computed(() => {
    return allSurveys.value
      ?.filter((survey) =>
        selectedSurveys.value.includes(survey.surveyId || ""),
      )
      .map((survey) => survey.surveyName)
      .join(", ");
  });
</script>
