<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              {{ isAddNew ? $t("定型文作成") : $t("定型文編集") }}
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div class="space-y-4">
          <div class="flex items-start space-x-4">
            <UFormGroup :label="$t('定型文の種類')" name="type" required>
              <USelect
                v-model="state.type"
                :options="typeOptions"
                option-attribute="name"
              />
            </UFormGroup>
            <UFormGroup class="flex-1" :label="$t('グループ名')" name="group">
              <UInput v-model="state.group" />
            </UFormGroup>
          </div>
          <UFormGroup :label="$t('定型文タイトル')" name="title" required>
            <UInput v-model="state.title" />
          </UFormGroup>
          <UFormGroup :label="$t('文章内容')" name="text" required>
            <UTextarea
              v-model="state.text"
              autoresize
              placeholder="..."
              rows="5"
            />
          </UFormGroup>
        </div>

        <template #footer>
          <div class="text-right">
            <UButton type="submit" class="px-4">
              {{ isAddNew ? $t("定型文作成") : $t("定型文編集") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import type { SampleMessage } from "~/types";
  import { SampleMessageType } from "~/types/enums.d";
  const userPermissions = usePermissions();
  const { object, string } = useYup();
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
    isAddNew: Boolean,
    sampleMessage: {
      type: Object as PropType<SampleMessage>,
      default: () => ({}),
    },
  });

  const typeOptions = computed(() => {
    return Object.values(SampleMessageType)
      .map((value) => ({
        name: t(value),
        value,
      }))
      .filter((obj: any) => {
        if (
          userPermissions.value.includes("create:app-settings-sample-messages")
        ) {
          return true;
        } else if (
          userPermissions.value.includes(
            "create:app-settings-sample-messages-personal",
          )
        ) {
          return obj.value === SampleMessageType.PERSONAL;
        }
      });
  });

  const schema = object().shape({
    type: string().required(),
    group: string(),
    title: string().required(),
    text: string().required(),
  });

  const initialState: SampleMessage = {
    type: SampleMessageType.PERSONAL,
    group: "",
    title: "",
    text: "",
    createdAt: "",
    textTemplateId: "",
    customerId: "",
  };
  let state = reactive<SampleMessage>({ ...initialState });

  watch(
    () => props.sampleMessage,
    (sampleMessage) => {
      if (sampleMessage) {
        state = {
          ...sampleMessage,
        };
      } else {
        Object.assign(state, initialState);
      }
    },
    { immediate: true, deep: true },
  );

  const emit = defineEmits(["close", "update", "addNew"]);

  function onSubmit() {
    if (props.isAddNew) {
      emit("addNew", state);
    } else {
      emit("update", state);
    }
    state = { ...initialState };
    emit("close");
  }
</script>
