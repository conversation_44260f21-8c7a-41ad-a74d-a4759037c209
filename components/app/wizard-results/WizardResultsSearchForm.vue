<template>
  <div>
    <!-- <div class="w-2/3 mx-auto flex flex-inline space-x-4">
      <UInput
        color="gray"
        class="flex-1"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="md"
        :trailing="false"
        placeholder="キーワード(アンケート名, 相談者名)..."
      />
      <UButton
        class="px-8"
        icon="i-heroicons-magnifying-glass-20-solid"
        size="sm"
        color="primary"
        variant="solid"
        label="検索"
        :trailing="false"
      />
    </div> -->
    <UAccordion :items="items" variant="link">
      <template #default="{ item, open }">
        <UButton
          color="gray"
          variant="link"
          class="dark:border-gray-700 w-fit"
          :ui="{ rounded: 'rounded-none', padding: { sm: 'p-3' } }"
        >
          <template #leading>
            <UIcon
              :name="item.icon"
              class="w-3 h-3 text-gray-500 dark:text-gray-900 transition-transform duration-200"
              :class="{ 'rotate-90': open }"
            />
          </template>
          <span class="truncate">{{ item.label }}</span>
        </UButton>
      </template>

      <template #getting-started>
        <UCard :ui="{ base: 'overflow-visible' }">
          <div class="grid grid-cols-4 space-x-4 mt-4">
            <UFormGroup label="ウィザード">
              <AppWizardsMultiSelect v-model="searchConditions.wizardId" />
            </UFormGroup>

            <UFormGroup label="相談者">
              <AppCounseleesMultiSelect
                v-model="searchConditions.counseleeId"
              />
            </UFormGroup>
            <UFormGroup label="この日から">
              <BaseDatePicker
                color="white"
                v-model="startDate"
                block
                :disabled="false"
              />
            </UFormGroup>
            <UFormGroup label="この日まで">
              <BaseDatePicker
                color="white"
                v-model="endDate"
                block
                :disabled="false"
              />
            </UFormGroup>
          </div>

          <template #footer>
            <div class="flex flex-inline justify-center space-x-4">
              <UButton
                class="px-5"
                icon="i-heroicons-arrow-uturn-left-solid"
                size="sm"
                color="white"
                variant="solid"
                label="リセット"
                :trailing="false"
                @click="appWizardResultsStore.resetSearchConditions"
              />
              <UButton
                class="px-8"
                icon="i-heroicons-magnifying-glass-20-solid"
                size="sm"
                color="primary"
                variant="solid"
                label="検索"
                :trailing="false"
                @click="
                  appWizardResultsStore.fetchWizardResults(
                    currentCustomer.customerId as string,
                  )
                "
              />
            </div>
          </template>
        </UCard>
      </template>
    </UAccordion>
  </div>
</template>
<script setup lang="ts">
  import { useAppWizardResultsStore } from "~/stores/app/wizard-results";
  import { cloneDeep } from "lodash";
  const route = useRoute();
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const appWizardsStore = useAppWizardsStore();
  const { allWizards } = storeToRefs(appWizardsStore);
  const appWizardResultsStore = useAppWizardResultsStore();
  const { pagination, searchConditions, sortConditions } =
    storeToRefs(appWizardResultsStore);
  const date = ref(new Date());

  const items = [
    {
      label: "高度な検索",
      icon: "i-heroicons-play-solid",
      defaultOpen: true,
      slot: "getting-started",
    },
  ];

  const startDate = computed({
    get: () => {
      return searchConditions.value.startDate
        ? new Date(searchConditions.value.startDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.startDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  const endDate = computed({
    get: () => {
      return searchConditions.value.endDate
        ? new Date(searchConditions.value.endDate)
        : "";
    },
    set: (value: Date) => {
      searchConditions.value.endDate = formatDate(value, "YYYY-MM-DD");
    },
  });

  onMounted(async () => {
    //appWizardResultsStore.fetchSurveyResults(searchConditions.value);
    const query = route.query;
    let _searchConditions, _pagination, _sortConditions;
    if (query) {
      _searchConditions = cloneDeep({
        ...searchConditions.value,
        ...query,
      });

      _searchConditions.wizardId = query.surveyId as string;

      _searchConditions.counseleeId = [query.counseleeId as string];

      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      appWizardResultsStore.updateConditions(
        _searchConditions,
        _pagination,
        _sortConditions,
      );
    }
  });

  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        appWizardResultsStore.fetchWizardResults(
          currentCustomer.value.customerId as string,
        );
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        appWizardResultsStore.fetchWizardResults(
          currentCustomer.value.customerId as string,
        );
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        appWizardsStore.fetchAllWizards(
          currentCustomer.value.customerId as string,
        );

        appWizardResultsStore.fetchWizardResults(
          currentCustomer.value.customerId as string,
        );

        if (oldValue?.customerId) {
          searchConditions.value.counseleeId = [];
          searchConditions.value.wizardId = [];
        }
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>
