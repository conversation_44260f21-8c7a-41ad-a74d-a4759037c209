<script setup lang="ts">
  import type { WizardResult } from "@/types";
  const props = defineProps<{
    wizardResult?: WizardResult;
    isOpen: boolean;
  }>();
  const emit = defineEmits(["close", "select"]);
</script>

<template>
  <UModal :model-value="props.isOpen" @close="emit('close')">
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div class="font-semibold truncate">
            {{ wizardResult?.wizardName }}
          </div>
          <div class="flex flex-row items-center space-x-2">
            <UAvatar
              size="xs"
              :src="wizardResult?.counseleeAvatar"
              :alt="wizardResult?.counseleeName"
            />
            <div>
              <ULink
                inactive-class="text-primary-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              >
                {{ wizardResult?.counseleeName }}
              </ULink>
            </div>
          </div>
        </div>
      </template>
      <div class="space-y-4">
        <WizardResultRender :formTemplate="props.wizardResult?.wizardResult" />
      </div>

      <template #footer>
        <div class="flex flex-row items-center justify-between">
          <div class="text-sm font-light">
            {{
              formatDate(
                new Date(wizardResult?.createdAt || ""),
                "YYYY年MM月DD日 HH時mm分",
              )
            }}
          </div>
          <UButton @click="emit('close')" class="px-4"> 閉じる </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
