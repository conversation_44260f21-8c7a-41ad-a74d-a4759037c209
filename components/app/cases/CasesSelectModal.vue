<template>
  <UModal
    :model-value="props.show"
    :ui="{
      width: 'sm:max-w-5xl',
    }"
  >
    <UForm class="space-y-4">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              ケースのエクスポート対象の選択
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div>
          <CasesSearchForm />
          <CasesDataTable />
        </div>
        <template #footer>
          <div class="flex justify-end">
            <UButton
              label="確定"
              type="submit"
              class="px-10"
              @click="onSelect"
            />
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import { useCasesStore } from "~/stores/app/cases";
  const casesStore = useCasesStore();
  const { casesSelection } = storeToRefs(casesStore);
  const props = defineProps({
    show: Boolean,
  });

  const emit = defineEmits(["select", "close"]);
  const onSelect = () => {
    emit("select", casesSelection.value.selected);
    emit("close");
  };

  onMounted(() => {
    casesSelection.value.selected = [];
  });
</script>
