<template>
  <div class="group/tag">
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-0 space-y-3',
        },
      }"
    >
      <div class="flex px-4 justify-between items-center">
        <div class="text-sm font-semibold">相談分類</div>
        <div class="">
          <Icon v-if="loading" icon="eos-icons:loading" />
          <Icon
            v-else-if="updated"
            icon="ep:success-filled"
            class="text-green-500"
          />
        </div>
      </div>
      <div v-if="tags.length">
        <UDivider />
        <div class="py-2">
          <draggable
            class="dragArea list-group w-full flex flex-col"
            tag="transition-group"
            handle=".handle"
            :component-data="{
              tag: 'ul',
              type: 'transition-group',
              name: !drag ? 'flip-list' : null,
            }"
            v-model="tagsLocal"
            v-bind="dragOptions"
            @change="onUpdateDrag"
          >
            <template #item="{ element, index }">
              <div
                class="group py-3 flex relative flex-inline hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <div
                  v-if="!readonly"
                  class="absolute -left-1 h-full hidden group-hover:flex items-center justify-center"
                >
                  <UIcon
                    name="i-mdi-drag-vertical"
                    class="handle text-2xl hover:cursor-pointer text-gray-500 group-hover:text-primary-500"
                  />
                </div>
                <div class="flex-1 pl-4 pr-4">
                  <component
                    :is="FormElements[element.formType]"
                    :elementId="element.tagId"
                    :label="element.tagName"
                    :options="element.options"
                    :disabled="readonly"
                    :model-value="element.value"
                    @update:model-value="onUpdateTag"
                  />
                </div>
                <div
                  v-if="!readonly"
                  class="hidden absolute top-1 right-4 group-hover:flex items-start"
                >
                  <UButton
                    icon="i-heroicons-trash-solid"
                    size="2xs"
                    color="red"
                    variant="soft"
                    :trailing="false"
                    label="削除"
                    @click="removeTag(element.tagId, element.no)"
                  />
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
  import { cloneDeep } from "lodash";
  import draggable from "vuedraggable";
  import type { CaseTag } from "~/types";
  import { Icon } from "@iconify/vue";
  const { FormElements } = useFormTagElements();

  const props = defineProps<{
    tags: CaseTag[];
    no: number;
    readonly?: boolean;
    loading?: boolean;
    updated?: boolean;
  }>();

  const tagsLocal = ref(cloneDeep(props.tags));

  watch(
    () => props.tags,
    (tags) => {
      tagsLocal.value = cloneDeep(tags);
    },
    { immediate: true, deep: true },
  );

  const emits = defineEmits([
    "add:tag",
    "remove:tag",
    "update:tag",
    "drag:tags",
  ]);

  const removeTag = (elementId: string, no: number) => {
    emits("remove:tag", elementId, no);
  };

  const drag = ref(false);
  const dragOptions = computed(() => ({
    animation: 200,
    group: "description",
    disabled: false,
    ghostClass: "ghost",
  }));

  const onUpdateTag = (elementId: string, value: string) => {
    tagsLocal.value.map((t: any) => {
      if (t.tagId === elementId) {
        t.value = value;
        emits("update:tag", elementId, value);
      }
    });
  };

  const onUpdateDrag = (event: any) => {
    emits("drag:tags", tagsLocal.value);
  };
</script>
