<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import type { TableColumn, App<PERSON>ou<PERSON>lor, Case } from "~/types";
  import { useCasesStore } from "~/stores/app/cases";
  import { CaseStatus } from "~/types/enums.d";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { Icon } from "@iconify/vue";
  import { useAuthStore } from "~/stores/auth";
  const userPermissions = usePermissions();
  const toast = useToast();
  const casesStore = useCasesStore();
  const isOpenCounselorsPalette = ref(false);
  const isOpenCaseDetail = ref(false);
  const appCustomersStore = useAppCustomersStore();
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
  const { currentCustomer } = storeToRefs(appCustomersStore);
  let autoIntervals: any[] = [];
  const {
    casesSelection,
    loadings,
    pageFromCasesSelection,
    pageToCasesSelection,
  } = storeToRefs(casesStore);
  const { t } = useI18n();
  const columns: TableColumn[] = [
    {
      key: "isSelected",
      label: "対象",
      sortable: false,
      class: "text-center w-fit",
    },
    {
      key: "caseId",
      label: "ケースID",
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "status",
      label: t("Status"),
      sortable: true,
      class: "text-center",
    },
    {
      key: "assigneeName",
      label: t("Assignee"),
      sortable: true,
    },
    {
      key: "latestPostTime",
      label: t("Latest message"),
      sortable: true,
    },
    {
      key: "clientName",
      label: t("Counselee"),
      sortable: true,
      class: "text-center",
    },
    // {
    //   key: "userId",
    //   label: t("Counselee ID"),
    //   sortable: true,
    //   class: "text-center w-fit",
    // },
    {
      key: "count",
      label: t("Times"),
      sortable: true,
      class: "text-center w-fit",
    },
    {
      key: "risk",
      label: t("Priority"),
      sortable: true,
      class: "text-center w-fit",
    },
  ];

  const selectedColumns = ref(columns);
  const columnsTable = computed(() =>
    columns.filter((column) => selectedColumns.value.includes(column)),
  );
  // Filters
  const caseStatus = Object.values(CaseStatus).map((status) => ({
    label: t(`caseStatus.${status}`),
    value: status,
  }));

  const resetFilters = () => {
    casesStore.resetSearchConditions();
    casesStore.fetchCasesSelection(currentCustomer.value.customerId as string);
  };

  const sort = computed({
    get: () => {
      return {
        column: casesSelection.value.sortConditions.sortBy,
        direction: casesSelection.value.sortConditions.sortDesc
          ? "desc"
          : "asc",
      };
    },
    set: (value) => {
      casesSelection.value.sortConditions.sortBy = value.column;
      casesSelection.value.sortConditions.sortDesc = value.direction === "desc";
    },
  });
  const selectedStatus = computed(() => {
    return caseStatus.filter((status) =>
      casesSelection.value.searchConditions.caseStatus?.includes(status.value),
    );
  });
  const onFilterByStatus = (status: CaseStatus[]) => {
    casesSelection.value.searchConditions.caseStatus = status;
    casesStore.fetchCasesSelection(currentCustomer.value.customerId as string);
  };

  const onSelectRow = (row: Case) => {
    const index = casesSelection.value.selected.findIndex(
      (caseId) => caseId === row.caseId,
    );
    if (index === -1) {
      casesSelection.value.selected.push(row.caseId);
    } else {
      casesSelection.value.selected.splice(index, 1);
    }
  };
</script>

<template>
  <UCard
    id="dashboard-table"
    class="w-full"
    :ui="{
      base: 'overflow-visible',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: 'px-4 py-5' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700',
      },
      footer: { padding: 'p-4' },
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="font-semibold text-xl text-gray-900 dark:text-white leading-tight"
        >
          ケース一覧
        </h2>
        <USelectMenu
          :modelValue="casesSelection.searchConditions.caseStatus"
          :options="caseStatus"
          @change="onFilterByStatus"
          multiple
          value-attribute="value"
          option-attribute="label"
          placeholder="状態"
          class="w-44"
        >
          <template #label>
            <template v-if="selectedStatus.length">
              <span class="flex items-center -space-x-1">
                <span
                  v-for="label of selectedStatus"
                  :key="label.value"
                  class="flex-shrink-0 w-3 h-3 mt-px rounded-full"
                  :class="`bg-${getCaseStatusColor(label.value)}-500`"
                />
              </span>
              <span>{{ selectedStatus.length }}つの状態</span>
            </template>
            <template v-else>
              <span class="text-gray-500 dark:text-gray-400 truncate">
                {{ $t("Select status") }}
              </span>
            </template>
          </template>
          <template #option="{ option: status }">
            <span
              :class="[
                `bg-${getCaseStatusColor(status.value)}-500`,
                'inline-block h-3 w-3 flex-shrink-0 rounded-full',
              ]"
              aria-hidden="true"
            />
            <span class="truncate">{{ status.label }}</span>
          </template>
        </USelectMenu>
      </div>
    </template>

    <!-- Filters -->

    <!-- Header and Action buttons -->
    <div class="flex justify-between items-center w-full px-6 py-3">
      <div class="flex items-center gap-1.5">
        <span class="text-sm leading-5">1ページあたりの表示数</span>

        <USelect
          v-model="casesSelection.pagination.pageRangeDisplayed"
          :options="[3, 5, 10, 20, 30, 40]"
          class="me-2 w-20"
          size="xs"
        />
      </div>

      <div
        v-if="casesSelection.selected.length"
        class="flex gap-1.5 items-center"
      >
        <div class="text-sm">
          {{ casesSelection.selected.length }} 件が選択されています。
        </div>
        <UButton
          :padded="false"
          color="red"
          variant="link"
          label="選択を解除"
          @click="casesSelection.selected = []"
        />
      </div>
    </div>
    <!-- Table -->
    <UTable
      :rows="casesSelection.cases"
      :columns="columnsTable"
      :loading="loadings['fetchCasesSelection']"
      v-model:sort="sort"
      sort-mode="manual"
      sort-asc-icon="i-heroicons-arrow-up"
      sort-desc-icon="i-heroicons-arrow-down"
      @select="onSelectRow"
      class="w-full"
      :ui="{
        tr: {
          base: 'group hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer',
        },
        td: { base: 'truncate' },
        th: {
          size: 'text-xs',
          padding: 'px-0 pl-2  pt-4',
        },
      }"
    >
      <template #isSelected-data="{ row }">
        <UCheckbox
          color="primary"
          :model-value="casesSelection.selected.includes(row.caseId)"
        />
      </template>
      <template #caseId-data="{ row }">
        <UTooltip :text="row.caseId" :popper="{ placement: 'top' }">
          <template #text>
            <div>
              <div class="text-center">{{ row.caseId }}</div>
            </div>
          </template>
          <div class="text-xs w-14 truncate">
            {{ row.caseId }}
          </div>
        </UTooltip>
      </template>
      <template #status-data="{ row }">
        <CaseStatusChange :case="row" disabled />
      </template>
      <template #assigneeName-data="{ row }">
        <div v-if="row.counselorInChargeName" class="group/assigneeName">
          <div class="flex items-center space-x-2 transition-all duration-200">
            <UAvatar
              size="xs"
              :src="row.counselorInChargeImage"
              :alt="row.counselorInChargeName"
              :ui="{
                rounded: 'rounded-md',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
              }"
            />
            <div class="flex flex-col -space-y-1">
              {{ row.counselorInChargeName }}
            </div>
          </div>
        </div>
        <div v-else></div>
      </template>
      <template #elapsedTimeBeforeStart-data="{ row }">
        <div class="text-sm">{{ row.elapsedTimeBeforeStart }}</div>
      </template>
      <template #elapsedTimeInProgress-data="{ row }">
        <div class="text-sm">{{ row.elapsedTimeInProgress }}</div>
      </template>
      <template #latestPostTime-data="{ row }">
        <div class="text-sm">
          {{ row.latestPostTime ? fromNow(new Date(row.latestPostTime)) : "" }}
        </div>
      </template>
      <template #clientName-data="{ row }">
        <div class="flex items-center space-x-2">
          <div class="border-r pr-2">
            <UChip
              size="md"
              position="bottom-right"
              inset
              color="red"
              :ui="{ base: '-mx-0.5 -my-0.5 rounded-full' }"
              :show="row.isBlock || false"
              text="ー"
            >
              <component
                :is="getSNSIconComponent(row.channel)"
                class="h-5 w-5"
              />
            </UChip>
          </div>
          <UAvatar
            v-if="row.counseleeImage"
            size="xs"
            :src="row.counseleeImage"
            :alt="row.counseleeName"
          />
          <div class="flex flex-col -space-y-1">
            <ULink
              inactive-class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
            >
              {{ row.counseleeName }}
            </ULink>
            <div
              v-if="row.isBlock"
              class="text-[10px] text-red-500 dark:text-red-300"
            >
              ブロックされています
            </div>
          </div>
        </div>
      </template>
      <template #count-data="{ row }">
        <div :class="getHasAlertWork(row.hasAlertWork)">
          {{ row.count }} 回目
        </div>
      </template>
      <template #risk-data="{ row }">
        <div
          class="flex justify-center items-center"
          :class="`text-${getCasePriorityColorIcon(row.risk).color}-500`"
        >
          {{ $t(`casePriority.${row.risk}`) }}
        </div>
      </template>
      <template #preview-data="{ row }">
        <div></div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div class="flex flex-wrap justify-between items-center">
        <div>
          <span class="text-sm leading-5">
            <span class="font-medium">{{
              casesSelection.totalCasesCount
            }}</span>
            件中
            <span class="font-medium">{{ pageFromCasesSelection }}</span> ～
            <span class="font-medium">{{ pageToCasesSelection }}</span>
            件の結果を表示
          </span>
        </div>

        <UPagination
          v-model="casesSelection.pagination.page"
          :page-count="casesSelection.pagination.pageRangeDisplayed"
          :total="casesSelection.totalCasesCount"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline',
              },
            },
          }"
        />
      </div>
    </template>
  </UCard>
</template>
