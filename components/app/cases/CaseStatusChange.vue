<template>
  <Icon
    v-if="updateStatusLoading[props.case.caseId]"
    icon="eos-icons:loading"
    class="text-xl w-12 text-primary-500"
  />
  <UDropdown
    v-else
    :items="getNextCaseStatusOptions(props.case.status)"
    :popper="{ placement: 'bottom-start' }"
    class="w-full"
    :ui="{
      item: {
        padding: 'p-0'
      }
    }"
  >
    <UButton
      size="2xs"
      block
      :color="getCaseStatusColor(props.case.status)"
      :disabled="!hasPermissionToChangeCaseStatus(props.case) || disabled"
      :trailing-icon="
        hasPermissionToChangeCaseStatus(props.case) && !disabled
          ? 'i-heroicons-chevron-down'
          : ''
      "
    >
      {{ t(`caseStatus.${props.case.status}`) }}
    </UButton>
    <template #item="{ item }">
      <div
        class="flex items-center space-x-2 w-full px-1.5 py-1.5"
        @click="onUpdateCaseStatus(props.case, item.value)"
      >
        <UIcon
          :name="item.icon"
          class="h-4 w-4 rounded-full"
          :class="`bg-${item.color}-500 dark:bg-${item.color}-500`"
        />
        <span class="">{{ item.label }}にする</span>
      </div>
    </template>
  </UDropdown>
  <AppCounselorsPalette
    :is-open="isOpenCounselorsPalette"
    @close="isOpenCounselorsPalette = false"
    @select="onSelectCounselor"
    actionLabel="に担当者を変更します"
  />
</template>

<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  import { useCasesStore } from "~/stores/app/cases";
  import type { TableColumn, AppCounselor, Case } from "~/types";
  import { CaseStatus } from "~/types/enums.d";
  const userPermissions = usePermissions();
  const { t } = useI18n();
  const casesStore = useCasesStore();
  const {
    cases,
    loadings,
    pagination,
    pageFrom,
    pageTo,
    caseDetail,
    updateAssigneeLoading,
    updateStatusLoading,
    totalCasesCount,
    sortConditions,
    searchConditions,
  } = storeToRefs(casesStore);
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
  const toast = useToast();
  const isOpenCounselorsPalette = ref(false);
  const props = defineProps({
    case: {
      type: Object as PropType<any>,
      required: true,
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });

  const hasPermissionToChangeCaseStatus = (_case: Case) => {
    const isOpenCase = [CaseStatus.BEFORE_START, CaseStatus.OPEN].includes(
      _case.status,
    );
    const hasPermission =
      (isOpenCase &&
        userPermissions.value.includes("update:case-status-of-open-cases")) ||
      (!isOpenCase && userPermissions.value.includes("update:case-status"));

    const canUpdateCaseStatus = canUpdateCase(
      _case.status,
      user.value as any,
      _case.counselorInChargeId,
      userPermissions.value,
    );
    return hasPermission || canUpdateCaseStatus;
  };

  const nextStatus = ref();
  const onUpdateCaseStatus = async (_case: Case, _status: CaseStatus) => {
    const hasPermission = hasPermissionToChangeCaseStatus(_case);
    if (hasPermission) {
      nextStatus.value = _status;
      caseDetail.value = _case;
      if (needAddAssignee(_case.status, _status)) {
        isOpenCounselorsPalette.value = true;
      } else {
        const result = await casesStore.updateStatus(
          caseDetail.value,
          nextStatus.value,
        );
        if (result) {
          toast.add({
            title: t("Success"),
            description: t("Status has been updated"),
            icon: "i-heroicons-check-badge",
          });
        }
      }
    }
  };

  const onSelectCounselor = async (counselor: AppCounselor) => {
    isOpenCounselorsPalette.value = false;
    let result;

    if (caseDetail.value.status !== nextStatus.value) {
      result = await casesStore.updateCounselorAndStatus(
        caseDetail.value,
        counselor,
        nextStatus.value,
      );
    } else {
      result = await casesStore.updateCounselorInCharge(
        caseDetail.value,
        counselor,
      );
    }

    if (result) {
      toast.add({
        title: t("Success"),
        description: "データを更新しました。",
        icon: "i-heroicons-check-badge",
      });
    } else {
      toast.add({
        title: "処理失敗",
        description: "データの更新ができませんでした。",
        color: "red",
        icon: "i-heroicons-exclamation-circle",
      });
    }
  };
</script>
