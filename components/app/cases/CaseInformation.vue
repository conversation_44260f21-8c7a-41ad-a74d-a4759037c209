<template>
  <div class="flex flex-col space-y-2">
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-4 space-y-3',
        },
      }"
    >
      <div class="flex justify-between items-center gap-3">
        <div class="flex items-center space-x-2">
          <UAvatar
            v-bind="{
              src: props.case?.counseleeImage,
              alt: props.case?.counseleeName,
            }"
            size="sm"
            loading="lazy"
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-300 dark:bg-gray-400',
              placeholder:
                'text-xs font-semibold text-gray-700 dark:text-gray-800',
            }"
          />
          <!-- <UInput
            :padded="false"
            color="primary"
            variant="none"
            v-model="caseLocal.counseleeName"
            placeholder="相談者名"
            :disabled="readonly || loadings.updateName"
            @update:model-value="onUpdateName"
            maxlength="20"
            class="border-b border-gray-300 dark:border-gray-700 border-dashed pb-1"
          >
            <template #trailing>
              <Icon v-if="loadings.updateName" icon="eos-icons:loading" />
              <Icon
                v-else-if="updatedName"
                icon="ep:success-filled"
                class="text-green-500"
              />
            </template>
          </UInput> -->
          <div class="truncate">
            {{ props.case?.counseleeName }}
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <UFormGroup class="w-fit" :error="errors.updateGender">
            <USelect
              class="w-fit min-w-[80px]"
              size="xs"
              :model-value="caseLocal.gender"
              :options="genderOptions"
              :disabled="readonly || loadings.updateGender"
              @update:model-value="onUpdateGender"
              :color="
                updatedGender ? 'green' : errors.updateGender ? 'red' : 'white'
              "
            >
              <template #trailing>
                <Icon v-if="loadings.updateGender" icon="eos-icons:loading" />
                <Icon
                  v-else-if="updatedGender"
                  icon="ep:success-filled"
                  class="text-green-500"
                />
              </template>
            </USelect>
          </UFormGroup>
          <UFormGroup class="w-fit" :error="errors.updateAgeDecade">
            <USelect
              class="w-fit min-w-[95px]"
              size="xs"
              :model-value="caseLocal.ageDecade"
              :options="ageOptions"
              :disabled="readonly || loadings.updateAgeDecade"
              @update:model-value="onUpdateAgeDecade"
              :color="
                updatedAgeDecade
                  ? 'green'
                  : errors.updateAgeDecade
                  ? 'red'
                  : 'white'
              "
            >
              <template #trailing>
                <Icon
                  v-if="loadings.updateAgeDecade"
                  icon="eos-icons:loading"
                />
                <Icon
                  v-else-if="updatedAgeDecade"
                  icon="ep:success-filled"
                  class="text-green-500"
                />
              </template>
            </USelect>
          </UFormGroup>
        </div>
      </div>
      <UDivider />

      <UFormGroup label="申し送り事項" class="group">
        <UTextarea
          rows="1"
          autoresize
          size="sm"
          variant="outline"
          placeholder="申し送り事項を入力してください"
          :disabled="readonly"
          v-model="caseLocal.conveyed"
          @update:model-value="onUpdateConveyed"
        />
        <template #hint>
          <Icon
            v-if="casesStoreLoadings.updateConveyed"
            icon="eos-icons:loading"
          />
          <Icon
            v-else-if="updatedConveyed"
            icon="ep:success-filled"
            class="text-green-500"
          />
        </template>
      </UFormGroup>
    </UCard>
    <div class="flex justify-between items-center space-x-2">
      <div class="flex items-center space-x-2">
        <UButton
          v-if="!readonly"
          size="xs"
          label="相談分類追加"
          variant="solid"
          color="gray"
          icon="i-heroicons-plus-circle-solid"
          @click="isOpenCaseTagsPalette = true"
        />
        <UButton
          v-if="!readonly"
          size="xs"
          label="メモ追加"
          variant="solid"
          color="gray"
          icon="i-heroicons-plus-circle-solid"
          @click="addMemo"
        />
      </div>
      <USelectMenu
        :options="riskOptions"
        size="xs"
        :model-value="props.case.risk"
        @change="onUpdateCaseRisk"
        :disabled="readonly || casesStoreLoadings.updateCaseRisk"
      >
        <UButton
          size="xs"
          :color="updatedCaseRisk ? 'green' : 'white'"
          :variant="updatedCaseRisk ? 'outline' : 'solid'"
          class="justify-between"
        >
          <div
            v-if="props.case.risk"
            class="flex justify-center items-center space-x-1 pr-4"
            :class="`text-${getCasePriorityColorIcon(props.case.risk)}-500`"
          >
            <span
              class="inline-block h-3 w-3 flex-shrink-0 rounded-full"
              :class="`bg-${
                getCasePriorityColorIcon(props.case.risk).color
              }-500`"
              aria-hidden="true"
            />
            <div>{{ $t(`casePriority.${props.case.risk}`) }}</div>
          </div>
          <div v-else>優先度</div>
          <Icon
            v-if="casesStoreLoadings.updateCaseRisk"
            icon="eos-icons:loading"
            class="text-sm"
          />
          <Icon
            v-else-if="updatedCaseRisk"
            icon="ep:success-filled"
            class="text-green-500 text-sm"
          />
          <UIcon v-else name="i-heroicons-chevron-down" />
        </UButton>
        <template #option="{ option: risk }">
          <span
            class="inline-block h-3 w-3 flex-shrink-0 rounded-full"
            :class="`bg-${getCasePriorityColorIcon(risk).color}-500`"
            aria-hidden="true"
          />
          <span class="truncate">{{ $t(`casePriority.${risk}`) }}</span>
        </template>
      </USelectMenu>
    </div>
    <!-- <CaseCategories :categories="categories" /> -->
    <CaseTags
      v-if="tags.length"
      :tags="tags"
      @remove:tag="onRemoveTag"
      @update:tag="onUpdateTag"
      @drag:tags="onDragTags"
      :readonly="readonly"
      :loading="casesStoreLoadings.updateCaseTags"
      :updated="updatedCaseTags"
    />
    <UCard
      v-if="memos.length"
      :ui="{
        body: {
          padding: '!py-4 !px-0 space-y-3',
        },
      }"
    >
      <div class="flex px-4 justify-between items-center">
        <div class="text-sm font-semibold">メモ</div>
        <div class="">
          <Icon
            v-if="casesStoreLoadings.updateCaseMemos"
            icon="eos-icons:loading"
          />
          <Icon
            v-else-if="updatedCaseMemos"
            icon="ep:success-filled"
            class="text-green-500"
          />
        </div>
      </div>
      <UDivider />
      <div class="px-4 py-2 flex flex-col space-y-3">
        <template v-for="(memo, index) in memos">
          <CaseMemo
            :model-value="memos[index]"
            :no="index"
            @delete:memo="deleteMemo(index)"
            :readonly="readonly"
            @update:model-value="(value: string) => onUpdateMemo(value, index)"
          />
        </template>
      </div>
    </UCard>

    <AppCaseTagsPalette
      :is-open="isOpenCaseTagsPalette"
      @close="isOpenCaseTagsPalette = false"
      @select="onSelectCaseTag"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCounseleeStore } from "~/stores/app/counselee";
  import { useCasesStore } from "~/stores/app/cases";
  import {
    CasePriority,
    Gender,
    AgeDecade,
    CaseRisk,
    SNSChannel,
  } from "~/types/enums.d";
  import type { CaseTag, Case } from "~/types";
  import { cloneDeep, debounce } from "lodash";
  import { Icon } from "@iconify/vue";

  const props = defineProps<{
    case: Case;
    readonly?: boolean;
    maxTags?: number;
  }>();

  const casesStore = useCasesStore();
  const { loadings: casesStoreLoadings } = storeToRefs(casesStore);

  const counseleeStore = useCounseleeStore();

  const { loadings, errors } = storeToRefs(counseleeStore);

  const caseLocal = ref(cloneDeep(props.case)) as Ref<Case>;

  watch(
    () => props.case,
    () => {
      caseLocal.value = cloneDeep(props.case);
    },
    { immediate: true },
  );

  const { genderOptions, ageOptions } = useConstants();
  //["10代", "20代", "30代", "40代", "50代", "60代", "70代", "80代以上"];

  const riskOptions = [
    CasePriority.HIGH,
    CasePriority.NORMAL,
    CasePriority.LOW,
  ];
  const memos = ref(props.case?.memos || []) as Ref<string[]>;
  const tags = ref(props.case?.selectedTags || []) as Ref<any[]>;

  watch(
    () => props.case,
    () => {
      memos.value = props.case?.memos || [];
      tags.value = props.case?.selectedTags || [];
    },
    { immediate: true },
  );

  const toast = useToast();

  const addMemo = async () => {
    memos.value.push("");
  };
  const deleteMemo = (index: number) => {
    memos.value.splice(index, 1);
    updateCaseMemos(memos.value);
  };

  const onUpdateMemo = debounce((value: string, index: number) => {
    memos.value[index] = value;
    updateCaseMemos(memos.value);
  }, 1000);

  const updatedCaseMemos = ref(false);
  const updateCaseMemos = async (memos: string[]) => {
    const result = await casesStore.updateCaseMemos(props.case.caseId, memos);
    if (result) {
      updatedCaseMemos.value = true;
      setTimeout(() => {
        updatedCaseMemos.value = false;
      }, 2000);
    }
  };

  const increaseNo = ref(0);
  const isOpenCaseTagsPalette = ref(false);
  const updatedCaseTags = ref(false);
  const onSelectCaseTag = async (caseTag: CaseTag) => {
    // check if tag already exists
    const index = tags.value.findIndex((tag) => tag.tagId === caseTag.tagId);
    if (index > -1) {
      toast.add({
        title: "タグが重複しています",
        description: "同じタグは追加できません",
        icon: "i-heroicons-exclamation-triangle",
        color: "red",
      });
      return;
    }

    if (props.maxTags && tags.value.length >= props.maxTags) {
      toast.add({
        title: "タグの追加上限に達しました",
        description: `最大${props.maxTags}個までタグを追加できます`,
        icon: "i-heroicons-exclamation-triangle",
        color: "red",
      });
      return;
    }

    tags.value.push({
      ...caseTag,
      value: caseTag.formType === "checkbox" ? [] : "",
      no: increaseNo.value,
    });
    increaseNo.value += 1;
    isOpenCaseTagsPalette.value = false;
    updateCaseTags(tags.value);
  };

  const onRemoveTag = async (tagId: string, no: number) => {
    const index = tags.value.findIndex((tag) => tag.tagId === tagId);
    tags.value.splice(index, 1);
    updateCaseTags(tags.value);
  };

  const onUpdateTag = debounce(
    async (elementId: string, value: string | string[]) => {
      tags.value.map((t: any) => {
        if (t.tagId === elementId) {
          t.value = value;
        }
      });
      updateCaseTags(tags.value);
    },
    1000,
  );

  const onDragTags = (newTags: any) => {
    tags.value = cloneDeep(newTags);
    updateCaseTags(newTags);
  };

  const updateCaseTags = async (tags: any[]) => {
    const result = await casesStore.updateCaseTags(props.case.caseId, tags);
    if (result) {
      updatedCaseTags.value = true;
      setTimeout(() => {
        updatedCaseTags.value = false;
      }, 2000);
    }
  };

  const updatedGender = ref(false);
  const onUpdateGender = async (gender: Gender) => {
    const result = await counseleeStore.updateGender(
      caseLocal.value.counseleeId,
      gender,
    );
    if (result) {
      updatedGender.value = true;
      caseLocal.value.gender = gender;
      setTimeout(() => {
        updatedGender.value = false;
      }, 2000);
    }
  };

  const updatedName = ref(false);
  const onUpdateName = debounce(async () => {
    if (caseLocal.value.counseleeName) {
      const result = await counseleeStore.updateName(
        caseLocal.value.counseleeId,
        caseLocal.value.counseleeName,
      );
      if (result) {
        updatedName.value = true;
        setTimeout(() => {
          updatedName.value = false;
        }, 2000);
      }
    }
  }, 1000);
  const updatedAgeDecade = ref(false);
  const onUpdateAgeDecade = async (ageDecade: AgeDecade) => {
    const result = await counseleeStore.updateAgeDecade(
      caseLocal.value.counseleeId,
      ageDecade,
    );
    if (result) {
      updatedAgeDecade.value = true;
      caseLocal.value.ageDecade = ageDecade;
      setTimeout(() => {
        updatedAgeDecade.value = false;
      }, 2000);
    }
  };

  const updatedConveyed = ref(false);
  const onUpdateConveyed = debounce(async () => {
    const result = await casesStore.updateConveyed(
      caseLocal.value.counseleeId,
      caseLocal.value.conveyed,
    );

    if (result) {
      updatedConveyed.value = true;
      setTimeout(() => {
        updatedConveyed.value = false;
      }, 2000);
    }
  }, 1000);
  // const onUpdateConveyed = async () => {
  //   await casesStore.updateConveyed(
  //     caseLocal.value.caseId,
  //     caseLocal.value.conveyed,
  //   );
  // };
  const updatedCaseRisk = ref(false);
  const onUpdateCaseRisk = async (risk: CaseRisk) => {
    const result = await casesStore.updateCaseRisk(
      caseLocal.value.caseId,
      risk,
    );

    if (result) {
      props.case.risk = risk;
      updatedCaseRisk.value = true;
      setTimeout(() => {
        updatedCaseRisk.value = false;
      }, 2000);
    }
  };
</script>
