<template>
  <div>
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-4 space-y-3',
        },
      }"
    >
      <div class="flex justify-between items-center">
        <div class="text-sm">相談分類名</div>
        <div class="flex items-center space-x-2">
          <UButton
            v-if="categories.length"
            v-confirm="{
              title: '全てタグの削除',
              message: `全てタグを削除してもよろしいですか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: () => removeTag(-1),
            }"
            size="2xs"
            label="全削除"
            variant="soft"
            color="red"
            icon="i-heroicons-trash-solid"
          />
          <UPopover :popper="{ arrow: true, placement: 'top-start' }">
            <UButton
              size="2xs"
              label="追加"
              variant="soft"
              icon="i-heroicons-plus-circle-solid"
            />
            <template #panel>
              <UForm
                :schema="schema"
                :state="state"
                class="p-4"
                @submit="onSubmit"
              >
                <UFormGroup name="tag">
                  <UInput
                    v-model="state.tag"
                    autofocus
                    placeholder="入力してEnterで作成できます"
                  />
                </UFormGroup>
              </UForm>
            </template>
          </UPopover>
        </div>
      </div>
      <div v-if="categories.length">
        <UDivider />
        <div class="mt-2 flex flex-wrap gap-2">
          <div v-for="(tag, index) in categories" :key="index" class="group">
            <UBadge color="gray" variant="solid">
              <UIcon
                name="i-heroicons-hashtag"
                class="group-hover:hidden mr-1"
              />
              <UIcon
                :key="tag.id + index"
                v-confirm="{
                  title: 'タグの削除',
                  message: `「${tag}」のタグを削除してもよろしいですか？`,
                  confirmButtonText: 'はい、削除する',
                  cancelButtonText: 'いいえ',
                  onConfirm: () => removeTag(index),
                }"
                name="i-heroicons-x-mark"
                class="mr-1 text-red-500 text-md font-bold hidden group-hover:block cursor-pointer"
              />

              {{ tag }}
            </UBadge>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
  const { string, object } = useYup();
  const props = defineProps<{
    categories: any[];
    no: number;
  }>();
  const schema = object({
    tag: string(),
  });
  const emits = defineEmits(["add:tag", "remove:tag"]);

  const state = reactive({
    tag: undefined,
  });

  function onSubmit() {
    emits("add:tag", state.tag);
    state.tag = undefined;
  }

  const removeTag = (index: number) => {
    emits("remove:tag", index);
  };
</script>
