<template>
  <div>
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-4 space-y-3',
        },
      }"
    >
      <div class="flex justify-between items-center">
        <div class="text-sm">
          相談分類
        </div>
        <UButton
          size="2xs"
          label="追加"
          variant="soft"
          icon="i-heroicons-plus-circle-solid"
        />
      </div>
      <UDivider />
      <div v-for="(attr, index) in attributes" :key="index">
        <div class="flex items-center text-sm space-x-1">
          <div class="w-4 h-4 border flex items-center justify-center rounded-full text-xs font-bold border-gray-500">
            {{ index + 1 }}
          </div>
          <div>相談分類</div>
        </div>
        <UDivider />
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  attributes: any[];
  no: number;
}>()

const options = ref([
  { id: 1, name: 'bug', color: 'd73a4a' },
  { id: 2, name: 'documentation', color: '0075ca' },
  { id: 3, name: 'duplicate', color: 'cfd3d7' },
  { id: 4, name: 'enhancement', color: 'a2eeef' },
  { id: 5, name: 'good first issue', color: '7057ff' },
  { id: 6, name: 'help wanted', color: '008672' },
  { id: 7, name: 'invalid', color: 'e4e669' },
  { id: 8, name: 'question', color: 'd876e3' },
  { id: 9, name: 'wontfix', color: 'ffffff' }
])
</script>
