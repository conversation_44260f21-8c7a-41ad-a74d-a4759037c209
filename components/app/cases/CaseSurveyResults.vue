<template>
  <div class="flex flex-col space-y-2">
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-4 space-y-3',
        },
      }"
    >
      <div class="">
        <div v-if="props.case.currentSurveyResult" class="flex flex-col">
          <div class="pl-2 pr-3 py-2 flex items-center justify-between">
            <div class="flex items-center font-semibold space-x-2">
              <UButton
                icon="i-heroicons-arrow-left"
                size="sm"
                color="primary"
                square
                :padded="false"
                variant="link"
                @click="props.case.currentSurveyResult = null"
              />
              <div class="text-sm font-semibold">
                {{ props.case.currentSurveyResult?.surveyName }}
              </div>
            </div>
            <div class="flex-1 flex space-x-1 justify-end relative truncate">
              <div class="text-xs">
                {{
                  formatDate(
                    new Date(props.case.currentSurveyResult?.createdAt),
                    "YYYY年MM月DD日 HH:mm",
                  )
                }}
              </div>
            </div>
          </div>
          <UDivider />
          <div
            v-if="surveyResultsLoadings.fetchSurveyResult"
            class="flex flex-col items-center justify-center space-y-1 py-6"
          >
            <Icon icon="eos-icons:loading" class="text-primary-500 text-4xl" />
            <div class="text-gray-500 text-sm">アンケート結果を読み込み中</div>
          </div>
          <FormSurveyResultRender
            class="pt-4"
            v-else-if="props.case.currentSurveyResult?.formTemplate"
            :formTemplate="props.case.currentSurveyResult?.formTemplate"
          />
        </div>
        <UVerticalNavigation
          v-else
          :links="orderBy(props.case.surveyResults, 'createdAt', 'asc')"
          class="w-full"
          :ui="{
            label:
              'relative truncate text-gray-900 font-semibold dark:text-white text-left',
          }"
        >
          <template #default="{ link }">
            <div
              class="flex items-center space-x-2 relative"
              @click="onSelectSurveyResult(link)"
            >
              <div class="text-sm hover:text-primary font-semibold">
                {{ link.surveyName }}
              </div>
            </div>
          </template>
          <template #badge="{ link }">
            <div class="flex-1 flex space-x-1 justify-end relative truncate">
              <div class="text-xs">
                {{
                  formatDate(new Date(link.createdAt), "YYYY年MM月DD日 HH:mm")
                }}
              </div>
            </div>
          </template>
        </UVerticalNavigation>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
  import type { Case } from "~/types";
  import { storeToRefs } from "pinia";
  import { useCasesStore } from "~/stores/app/cases";
  import { useSurveyResultsStore } from "~/stores/app/survey-results";
  import { Icon } from "@iconify/vue";
  import { orderBy } from "lodash";
  const props = defineProps<{
    case: Case;
  }>();
  const casesStore = useCasesStore();
  const { loadings } = storeToRefs(casesStore);

  const surveyResultsStore = useSurveyResultsStore();
  const { loadings: surveyResultsLoadings } = storeToRefs(surveyResultsStore);

  watch(
    () => props.case.currentSurveyResult?.surveyResultId,
    async (newValue, oldValue) => {
      if (
        newValue !== oldValue &&
        newValue &&
        !props.case.currentSurveyResult?.formTemplate
      ) {
        const surveyResult = await surveyResultsStore.fetchSurveyResult(
          newValue,
        );
        props.case.currentSurveyResult = {
          ...props.case.currentSurveyResult,
          ...surveyResult,
        };
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  const onSelectSurveyResult = (surveyResult: any) => {
    props.case.currentSurveyResult = surveyResult;
  };
</script>
