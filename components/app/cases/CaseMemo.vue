<template>
  <div class="group">
    <UFormGroup :label="`メモ ${props.no + 1 || 1}`">
      <template #hint>
        <div v-if="!readonly" class="hidden group-hover:flex space-x-2 h-5">
          <UButton
            v-confirm="{
              title: 'メモの削除',
              message: `このメモを削除してもよろしいですか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: () => emits('delete:memo', props.no),
            }"
            size="2xs"
            label="削除"
            variant="soft"
            icon="i-heroicons-trash-solid"
            color="red"
          />
        </div>
      </template>
      <template #default>
        <UTextarea
          autoresize
          placeholder="メモを記入してください"
          rows="1"
          :model-value="modelValue"
          :disabled="readonly"
          @update:model-value="onUpdateMemo"
        />
      </template>
    </UFormGroup>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps<{
    modelValue: string;
    no: number;
    readonly?: boolean;
  }>();

  const emits = defineEmits(["delete:memo", "update:modelValue"]);

  const onUpdateMemo = (value: string) => {
    emits("update:modelValue", value);
  };
</script>
