<template>
  <div class="flex flex-col space-y-2">
    <UCard
      :ui="{
        body: {
          padding: '!py-4 !px-4 space-y-3',
        },
      }"
    >
      <div class="">
        <div v-if="props.case.currentWizardResult" class="flex flex-col">
          <div class="pl-2 pr-3 py-2 flex items-center justify-between">
            <div class="flex items-center font-semibold space-x-2">
              <UButton
                icon="i-heroicons-arrow-left"
                size="sm"
                color="primary"
                square
                :padded="false"
                variant="link"
                @click="props.case.currentWizardResult = null"
              />
              <div class="text-sm font-semibold">
                {{ props.case.currentWizardResult?.wizardName || 'ウィザード' }}
              </div>
            </div>
            <div class="flex-1 flex space-x-1 justify-end relative truncate">
              <div class="text-xs">
                {{
                  formatDate(
                    new Date(props.case.currentWizardResult?.createdAt),
                    "YYYY年MM月DD日 HH:mm",
                  )
                }}
              </div>
            </div>
          </div>
          <UDivider />
          <div
            v-if="wizardResultsLoadings.fetchWizardResult"
            class="flex flex-col items-center justify-center space-y-1 py-6"
          >
            <Icon icon="eos-icons:loading" class="text-primary-500 text-4xl" />
            <div class="text-gray-500 text-sm">ウィザード結果を読み込み中</div>
          </div>
          <WizardResultRender
            class="pt-4"
            v-else-if="props.case.currentWizardResult?.wizardResult"
            :formTemplate="props.case.currentWizardResult?.wizardResult"
          />
        </div>
        <UVerticalNavigation
          v-else
          :links="orderBy(props.case.wizardResults, 'createdAt', 'asc')"
          class="w-full"
          :ui="{
            label:
              'relative truncate text-gray-900 font-semibold dark:text-white text-left',
          }"
        >
          <template #default="{ link }">
            <div
              class="flex items-center space-x-2 relative"
              @click="onSelectWizardResult(link)"
            >
              <div class="text-sm hover:text-primary font-semibold">
                {{ link.wizardName || 'ウィザード'}}
              </div>
            </div>
          </template>
          <template #badge="{ link }">
            <div class="flex-1 flex space-x-1 justify-end relative truncate">
              <div class="text-xs">
                {{
                  formatDate(new Date(link.createdAt), "YYYY年MM月DD日 HH:mm")
                }}
              </div>
            </div>
          </template>
        </UVerticalNavigation>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
  import type { Case } from "~/types";
  import { storeToRefs } from "pinia";
  import { useCasesStore } from "~/stores/app/cases";
  import { useAppWizardResultsStore } from "~/stores/app/wizard-results";
  import { Icon } from "@iconify/vue";
  import { orderBy } from "lodash";
  const props = defineProps<{
    case: Case;
  }>();
  const casesStore = useCasesStore();
  const { loadings } = storeToRefs(casesStore);

  const appWizardResultsStore = useAppWizardResultsStore();
  const { loadings: wizardResultsLoadings } = storeToRefs(appWizardResultsStore);

  watch(
    () => props.case.currentWizardResult?.wizardResultId,
    async (newValue, oldValue) => {
      if (
        newValue !== oldValue &&
        newValue &&
        !props.case.currentWizardResult?.wizardResult
      ) {
        const surveyResult = await appWizardResultsStore.fetchWizardResult(
          newValue,
        );
        props.case.currentWizardResult = {
          ...props.case.currentWizardResult,
          ...surveyResult,
        };
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  const onSelectWizardResult = (surveyResult: any) => {
    props.case.currentWizardResult = surveyResult;
  };
</script>
