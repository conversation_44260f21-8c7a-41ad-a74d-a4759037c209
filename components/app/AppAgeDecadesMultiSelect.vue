<template>
  <USelectMenu
    :options="ageOptions"
    v-model="selectedAgeDecades"
    value-attribute="value"
    option-attribute="label"
    multiple
  >
    <template #label>
      <template v-if="selectedAgeDecadesObject.length">
        <span class="truncate">{{ selectedAgeDecadesLabel }}</span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  const { ageOptions } = useConstants();

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: [],
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const selectedAgeDecades = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      console.log(val);
      emits("update:modelValue", val);
    },
  });

  const selectedAgeDecadesObject = computed(() => {
    return ageOptions.filter((ageDecade) =>
      selectedAgeDecades.value.includes(ageDecade.value),
    );
  });

  const selectedAgeDecadesLabel = computed(() => {
    return ageOptions
      .filter((ageDecade) => selectedAgeDecades.value.includes(ageDecade.value))
      .map((ageDecade) => ageDecade.label)
      .join(", ");
  });
</script>
