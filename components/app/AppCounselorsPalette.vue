<script setup>
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/app/counselors";
  import { CounselorRole } from "~/types/enums.d";
  const counselorsStore = useCounselorsStore();
  const props = defineProps({
    isOpen: {
      type: Boolean,
      default: false,
    },
    actionLabel: {
      type: String,
      default: "を選択",
    },
    hideViewer: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(["close", "select"]);
  const { loadings, counselorsForPalette } = storeToRefs(counselorsStore);

  const commandPaletteRef = ref();

  function onSelect(option) {
    emit("select", option);
  }
</script>

<template>
  <UModal
    :model-value="props.isOpen"
    @close="emit('close')"
    :ui="{
      background: 'bg-white dark:bg-gray-900',
      ring: 'dark:ring-1 dark:ring-gray-600',
    }"
  >
    <UCommandPalette
      v-if="loadings['fetchCounselors']"
      loading
      :empty-state="{
        icon: 'i-heroicons-cloud-arrow-down-solid',
        label: $t('Loading...'),
        queryLabel: $t('Loading...'),
      }"
      :placeholder="$t('Loading...')"
    />

    <UCommandPalette
      v-if="!loadings['fetchCounselors'] && counselorsForPalette.length"
      ref="commandPaletteRef"
      :groups="[
        {
          key: 'consultants',
          commands:
            counselorsForPalette.filter(
              (obj) => obj.role !== CounselorRole.VIEWER || !props.hideViewer,
            ) || [],
        },
      ]"
      :autoselect="false"
      :fuse="{ resultLimit: 100, fuseOptions: { threshold: 0.1 } }"
      :placeholder="$t('Search by name...')"
      :ui="{
        container: 'max-h-[50vh] hidden-scrollbar',
        group: {
          command: {
            avatar: {
              size: '3xs',
            },
            base: 'group/command',
          },
        },
      }"
      @update:model-value="onSelect"
    >
      <template #consultants-icon="{ command }">
        <UAvatar
          v-bind="command.avatar"
          size="xs"
          loading="lazy"
          :ui="{
            rounded: 'rounded-lg',
            background: 'bg-gray-300 dark:bg-gray-400',
            placeholder:
              'text-xs font-semibold text-gray-700 dark:text-gray-800',
            chip: {
              size: {
                xs: 'h-2 w-2',
              },
            },
          }"
        />
      </template>
      <template #consultants-command="{ command }">
        <div class="flex items-center justify-between w-full space-x-4">
          <div>{{ command.label }}</div>
          <div>
            <UBadge
              size="xs"
              variant="soft"
              :color="getConsultantRoleColor(command.role)"
            >
              {{ $t(command.role) }}
            </UBadge>
          </div>
          <div class="text-gray-400 text-sm hidden group-hover/command:block">
            {{ actionLabel }}
          </div>
        </div>
      </template>
    </UCommandPalette>
  </UModal>
</template>
