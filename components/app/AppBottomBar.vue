<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppUIStore } from '~/stores/app/ui'
const appUIStore = useAppUIStore()
const colorMode = useColorMode()
const appConfig = useAppConfig()

const { showAppBottomBar } = storeToRefs(appUIStore)
const isDark = computed({
  get () {
    return colorMode.value === 'dark'
  },
  set () {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  }
})
const setPrimaryColor = (color: string) => {
  appConfig.ui.primary = color
  localStorage.setItem('primaryColor', color)
}

const closeAppBottomBar = () => {
  showAppBottomBar.value = false
}
</script>

<template>
  <ClientOnly>
    <div
      v-if="showAppBottomBar"
      class="fixed bottom-0 left-0 z-50 grid w-full h-16 grid-cols-1 px-8 bg-white border-t border-gray-200 md:grid-cols-3 dark:bg-gray-700 dark:border-gray-600"
    >
      <div
        class="items-center justify-center hidden mr-auto text-gray-500 dark:text-gray-400 md:flex"
      />
      <div class="flex items-center justify-center mx-auto">
        <div class="flex space-x-2 items-center justify-center">
          <div
            v-for="color in appConfig.ui.colors.filter((c) => c !== 'primary')"
            :key="color"
            class="h-5 w-5 rounded-full cursor-pointer hover:scale-150 hover:shadow-lg hover:rounded-full"
            :class="[
              `bg-${color}-500`,
              { 'scale-125 rounded-lg': color === appConfig.ui.primary },
            ]"
            @click="setPrimaryColor(color)"
          />
          <UButton
            :icon="
              isDark ? 'i-heroicons-moon-20-solid' : 'i-heroicons-sun-20-solid'
            "
            color="gray"
            variant="ghost"
            aria-label="Theme"
            @click="isDark = !isDark"
          />
        </div>
      </div>
      <div class="items-center justify-center hidden ml-auto md:flex">
        <UButton
          square
          color="red"
          variant="ghost"
          icon="i-heroicons-x-mark"
          @click="closeAppBottomBar"
        />
      </div>
    </div>
    <UButton
      icon="i-heroicons-cog-6-tooth"
      class="fixed bottom-0 right-0 z-50 animate__animated"
      :class="{
        animate__zoomInUp: !showAppBottomBar,
        animate__zoomOutDown: showAppBottomBar,
      }"
      size="sm"
      color="primary"
      square
      variant="ghost"
      @click="showAppBottomBar = true"
    />
  </ClientOnly>
</template>
