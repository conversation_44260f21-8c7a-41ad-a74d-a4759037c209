<template>
  <USelectMenu
    :options="allWizards"
    v-model="selectedWizards"
    value-attribute="wizardId"
    option-attribute="wizardName"
    placeholder="選択してください"
    :multiple="multiple"
    searchable
    searchable-placeholder="ウィザード名でフィルター..."
    :ui="{
      option: {
        size: '!text-xs',
      },
    }"
  >
    <template #label>
      <template v-if="selectedWizardsObject.length">
        <span v-if="selectedWizardsObject.length === 1" class="truncate">{{
          selectedWizardsName
        }}</span>
        <span v-else class="truncate"
          >{{ selectedWizardsObject.length }}件が選択されています</span
        >
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate"> 選択してください </span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppWizardsStore } from "~/stores/app/wizards";
const wizardsStore = useAppWizardsStore();
const { allWizards } = storeToRefs(wizardsStore);
const props = defineProps({
  modelValue: {
    type: Array as PropType<string[]> | PropType<string>,
    default: [],
  },
  multiple: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

const emits = defineEmits(["update:modelValue"]);

const selectedWizards = computed({
  get() {
    return props.modelValue;
  },
  set(val: string) {
    // if val is already an array, remove it
    if (props.modelValue.includes(val)) {
      emits("update:modelValue", []);
    } else {
      emits("update:modelValue", val);
    }
  },
});
const selectedWizardsObject = computed(() => {
  return allWizards.value?.filter((survey) =>
    selectedWizards.value.includes(survey.wizardId || "")
  );
});

const selectedWizardsName = computed(() => {
  return allWizards.value
    ?.filter((survey) => selectedWizards.value.includes(survey.wizardId || ""))
    .map((survey) => survey.wizardName)
    .join(", ");
});
</script>
