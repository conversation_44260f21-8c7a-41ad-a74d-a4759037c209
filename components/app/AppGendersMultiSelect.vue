<template>
  <USelectMenu
    :options="genderOptions"
    v-model="selectedGenders"
    value-attribute="value"
    option-attribute="label"
    multiple
  >
    <template #label>
      <template v-if="selectedGendersObject.length">
        <span class="truncate">{{ selectedGendersLabel }}</span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  const { genderOptions, ageOptions } = useConstants();

  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]>,
      default: [],
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const selectedGenders = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      console.log(val);
      emits("update:modelValue", val);
    },
  });

  const selectedGendersObject = computed(() => {
    return genderOptions.filter((gender) =>
      selectedGenders.value.includes(gender.value),
    );
  });

  const selectedGendersLabel = computed(() => {
    return genderOptions
      .filter((gender) => selectedGenders.value.includes(gender.value))
      .map((gender) => gender.label)
      .join(", ");
  });
</script>
