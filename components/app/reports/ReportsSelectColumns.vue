<template>
  <div
    class="flex w-4/5 mx-auto flex-col justify-between bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 rounded-md"
  >
    <div
      class="font-normal px-4 py-2 text-sm flex flex-row justify-between items-center"
    >
      <div>出力項目選択</div>
      <slot name="title-right" />
    </div>
    <UDivider color="gray" />
    <div class="flex-1 flex">
      <div class="py-4 px-4 flex-1 space-y-2">
        <div class="flex items-center justify-between text-sm">
          <div>選択可能な項目</div>
          <div>
            <slot name="avaiables-right" />
          </div>
        </div>
        <div
          class="h-full max-h-[calc(100vh-285px)] border border-gray-200 dark:border-gray-700 rounded-sm shadow-sm overflow-auto py-1"
        >
          <div v-for="element in avaiables">
            <UDivider
              v-if="element.group && isFirstElementOfGroup(element)"
              type="dashed"
              :ui="{
                label: 'font-semibold',
                border: { base: 'border-gray-300' },
              }"
              class="my-2"
              size="sm"
            >
              <div>
                <div class="text-sm font-semibold">{{ $t(element.group) }}</div>
                <div class="text-xs font-light max-w-52 truncate">
                  {{ element?.survey?.surveyName || element?.wizard?.wizardName }}
                </div>
              </div>
            </UDivider>
            <div
              class="flex space-x-1 items-center w-full hover:bg-gray-100 dark:hover:bg-gray-800 p-2 text-sm cursor-pointer"
              @click="element.selected = !element.selected"
            >
              <div class="w-full">
                <UCheckbox
                  :name="element.value"
                  :label="element.label"
                  v-model="element.selected"
                  :color="element.group === 'emergency' ? 'red' : 'primary'"
                >
                  <template #label>
                    <div
                      @click="element.selected = !element.selected"
                      :class="{
                        'text-red-700': element.group === 'emergency',
                      }"
                    >
                      {{ element.label }}
                    </div>
                  </template>
                </UCheckbox>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-col space-y-3 justify-center items-center">
        <UButton
          icon="i-heroicons-chevron-double-right"
          size="xs"
          color="white"
          variant="solid"
          label="全て追加"
          :trailing="true"
          @click="onSelectAll"
        ></UButton>
        <UButton
          icon="i-heroicons-chevron-double-left"
          size="xs"
          color="white"
          variant="solid"
          label="全て削除"
          :trailing="false"
          @click="onRemoveAll"
        ></UButton>
        <slot name="action-options" />
      </div>
      <div class="py-4 px-4 flex-1 space-y-2">
        <div class="text-sm">エクスポート対象項目</div>
        <div
          class="h-full max-h-[calc(100vh-285px)] border border-gray-200 dark:border-gray-700 rounded-sm shadow-sm overflow-auto py-1"
          :class="props.error ? 'border-red-500' : ''"
        >
          <draggable
            class="dragArea list-group w-full"
            item-key="value"
            ref="parent"
            :component-data="{
              tag: 'div',
              type: 'transition-group',
              name: !drag ? 'flip-list' : null,
            }"
            handle=".handle"
            :list="selected"
            :key="selected.length"
            v-bind="dragOptions"
          >
            <template #item="{ element, index }">
              <div
                :key="element.value"
                class="group !hover:cursor-pointer flex justify-between items-center w-full dark:hover:bg-gray-800 hover:bg-gray-100 p-2 text-sm cursor-pointer"
              >
                <div
                  class="flex items-center space-x-1 group-hover:text-primary-500"
                >
                  <UIcon
                    name="i-nimbus-drag-dots"
                    class="handle text-lg hover:cursor-pointer text-gray-500 group-hover:text-primary-500"
                    :class="{
                      'group-hover:text-red-700': element.group === 'emergency',
                    }"
                  />

                  <div
                    :class="{
                      'text-red-700': element.group === 'emergency',
                    }"
                  >
                    {{ index + 1 }}. {{ element.label }}
                  </div>
                </div>
                <div class="hidden items-center group-hover:flex space-x-1">
                  <UButton
                    :padded="false"
                    size="md"
                    color="gray"
                    variant="link"
                    icon="i-icons8-up-round"
                    :disabled="index === 0"
                    @click="
                      selected.splice(
                        index - 1,
                        0,
                        selected.splice(index, 1)[0],
                      )
                    "
                  />
                  <UButton
                    :padded="false"
                    size="md"
                    color="gray"
                    variant="link"
                    icon="i-icons8-down-round"
                    :disabled="index === selected.length - 1"
                    @click="
                      selected.splice(
                        index + 1,
                        0,
                        selected.splice(index, 1)[0],
                      )
                    "
                  />
                  <UButton
                    :padded="false"
                    size="md"
                    color="red"
                    variant="link"
                    icon="i-clarity-remove-line"
                    @click="
                      selected.splice(index, 1);
                      element.selected = false;
                    "
                  />
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>
    </div>
    <div class="p-4 flex space-x-2 justify-end">
      <slot name="action">
        <UButton
          icon="i-heroicons-arrow-right"
          :trailing="true"
          @click="emits('export')"
          :loading="props.loading"
          :disabled="selected.length === 0"
        >
          エクスポート
        </UButton>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import draggable from "vuedraggable";

  const props = defineProps<{
    columns: {
      value: string;
      label: string;
      selected: boolean;
      group?: string;
      survey?: any;
      wizard?: any;
    }[];
    modelValue: {
      value: string;
      label: string;
      selected: boolean;
      group?: string;
      survey?: any;
      wizard?: any;
    }[];
    loading: boolean;
    error?: string;
  }>();

  const emits = defineEmits(["export"]);
  const drag = ref(false);
  const dragOptions = computed(() => ({
    animation: 200,
    group: "description",
    disabled: false,
    ghostClass: "ghost",
  }));
  const selected = ref(props.modelValue || []);

  watch(
    () => props.modelValue,
    () => {
      selected.value = props.modelValue;
    },
  );

  const avaiables = ref(props.columns);

  watch(
    () => props.columns,
    () => {
      avaiables.value = props.columns;
    },
  );

  const onSelectAll = () => {
    avaiables.value.forEach((element) => {
      element.selected = true;
    });
  };

  const onRemoveAll = () => {
    avaiables.value.forEach((element) => {
      element.selected = false;
    });
  };

  watch(
    () => avaiables.value,
    () => {
      avaiables.value.forEach((element) => {
        if (element.selected) {
          if (!props.modelValue.includes(element)) {
            props.modelValue.push(element);
          }
        } else {
          if (props.modelValue.includes(element)) {
            props.modelValue.splice(props.modelValue.indexOf(element), 1);
          }
        }
      });
    },
    { immediate: true, deep: true },
  );

  const isFirstElementOfGroup = (element: any) => {
    return (
      avaiables.value.findIndex(
        (el) =>
          el.group === element.group &&
          el.survey?.surveyId === element.survey?.surveyId,
      ) === avaiables.value.indexOf(element)
    );
  };
</script>
