<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  const userPermissions = usePermissions();
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const links = computed(() => {
    return [
      {
        name: "app-reports",
        icon: "i-material-symbols-light-export-notes-outline",
        label: "エクスポート履歴",
        to: "/app/reports",
        exact: true,
        hasPermission: userPermissions.value.includes("read:app-reports"),
      },
      {
        name: "app-reports-consultation-logs",
        icon: "i-heroicons-chat-bubble-left-right",
        label: "チャット",
        to: "/app/reports/consultation-logs",
        hasPermission: userPermissions.value.includes(
          "read:app-reports-consultation-logs",
        ),
      },
      {
        name: "app-reports-cases",
        icon: "i-heroicons-clipboard-document-check",
        label: "ケース",
        to: "/app/reports/cases",
        hasPermission: userPermissions.value.includes("read:app-reports-cases"),
      },
      {
        name: "app-reports-summary",
        icon: "i-heroicons-presentation-chart-bar",
        label: "集計",
        to: "/app/reports/summary",
        hasPermission: userPermissions.value.includes(
          "read:app-reports-summary",
        ),
      },
      {
        name: "app-reports-counselees",
        icon: "i-fluent-people-chat-16-regular",
        label: "相談者",
        to: "/app/reports/counselees",
        hasPermission: userPermissions.value.includes(
          "read:app-reports-counselees",
        ),
      },
    ];
  });

  const menuList = computed(() => {
    return links.value
      .filter((obj) => obj.hasPermission)
      .map((link) => {
        const menu = { ...link };
        if (isSubNavigationMini.value) {
          delete menu.label;
          delete menu.badge;
        }
        return menu;
      });
  });
</script>

<template>
  <UVerticalNavigation :links="menuList" :ui="{ size: 'text-sm' }" />
</template>
