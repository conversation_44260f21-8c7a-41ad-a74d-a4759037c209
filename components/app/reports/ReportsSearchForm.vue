<template>
  <div>
    <div class="flex items-center mx-auto w-fit space-x-2">
      <BaseButtonGroups
        :options="periodOptions"
        size="sm"
        v-model="selectedPeriod"
      />
      <div class="flex items-center space-x-2">
        <BaseDatePicker
          :disabled="selectedPeriod !== 'custom'"
          v-model="fromDate"
        />
        <UIcon name="i-heroicons-arrow-small-right" />
        <BaseDatePicker
          :disabled="selectedPeriod !== 'custom'"
          v-model="toDate"
        />
      </div>
      <slot name="action" />
    </div>
  </div>
</template>

<script setup lang="ts">
  const props = defineProps<{
    modelValue: {
      value: {
        from: Date | null;
        to: Date | null;
      };
      type: string;
    };
  }>();
  const selectedPeriod = computed({
    get: () => props.modelValue.type,
    set: (value) => {
      props.modelValue.value.from = fromDate.value;
      props.modelValue.value.to = toDate.value;
      props.modelValue.type = value;
    },
  });
  const fromDate = computed({
    get: () => props.modelValue.value.from,
    set: (value) => {
      props.modelValue.value.from = value;
    },
  });
  const toDate = computed({
    get: () => props.modelValue.value.to,
    set: (value) => {
      props.modelValue.value.to = value;
    },
  });

  const periodOptions = [
    {
      value: "this_month",
      label: "当月",
    },
    {
      value: "last_month",
      label: "前月",
    },
    {
      value: "today",
      label: "今日",
    },
    {
      value: "yesterday",
      label: "昨日",
    },

    {
      value: "custom",
      label: "期間",
    },
  ];

  watch(
    () => selectedPeriod.value,
    () => {
      if (selectedPeriod.value === "last_month") {
        fromDate.value = new Date();
        fromDate.value.setMonth(fromDate.value.getMonth() - 1);
        fromDate.value.setDate(1);
        toDate.value = new Date();
        toDate.value.setDate(0);
      } else if (selectedPeriod.value === "this_month") {
        fromDate.value = new Date();
        fromDate.value.setDate(1);
        toDate.value = new Date();
      } else if (selectedPeriod.value === "yesterday") {
        fromDate.value = new Date();
        fromDate.value.setDate(fromDate.value.getDate() - 1);
        toDate.value = new Date();
        toDate.value.setDate(toDate.value.getDate() - 1);
      } else if (selectedPeriod.value === "today") {
        fromDate.value = new Date();
        toDate.value = new Date();
      }
    },
    { immediate: true },
  );
</script>
