<template>
  <a
    href="#"
    class="group text-5xl relative flex space-x-2 !font-logo items-center font-semibold text-gray-100 bg-gradient-to-r from-primary-400 via-blue-500 to-green-400 hover:from-green-400 hover:to-primary-400 text-transparent bg-clip-text transition-all duration-300"
  >
    <div class="">
      {{ $t("system_name") }}
    </div>
    <div v-if="!hideIcon" class="absolute -top-5 -right-12 z-50">
      <Icon
        icon="fluent:chat-sparkle-24-filled"
        class="text-green-400 group-hover:text-primary-400"
      />
    </div>
  </a>
</template>

<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  defineProps<{
    hideIcon?: boolean;
  }>();
</script>
