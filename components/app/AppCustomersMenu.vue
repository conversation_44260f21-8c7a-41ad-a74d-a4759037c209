<template>
  <aside
    class="fixed pb-24 pt-0 top-0 left-0 z-40 w-20 h-screen transition-transform -translate-x-full bg-primary-900/90 backdrop-blur dark:bg-gray-900 md:translate-x-0 text-center"
  >
    <div
      class="flex flex-col overflow-y-auto pt-1 h-full items-center space-y-3 hidden-scrollbar"
    >
      <template v-if="loadings['fetchCustomers']">
        <USkeleton
          v-for="i in 3"
          :key="i"
          class="h-16 w-16"
          :ui="{
            rounded: 'rounded-xl',
            background: 'bg-gray-300 dark:bg-gray-400',
          }"
        />
      </template>
      <draggable
        v-else
        class="dragArea list-group flex flex-col space-y-2.5"
        item-key="customerId"
        ref="parent"
        :component-data="{
          tag: 'div',
          type: 'transition-group',
          name: !drag ? 'flip-list' : null,
        }"
        handle=".handle"
        v-model="customersSorted"
        v-bind="dragOptions"
        @start="onStartDrag"
        @end="onEndDrag"
        :key="customers.length"
        direction="vertical"
      >
        <template #item="{ element: customer, index }">
          <UTooltip
            :key="customer.customerId"
            :text="customer.basic?.customerName"
            :popper="{ arrow: true, placement: 'right' }"
          >
            <div
              class="handle relative p-0.5 h-16 w-16 cursor-pointer rounded-2xl border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-700 hover:scale-125"
              :class="{
                '!border-gray-50 !dark:border-gray-300 scale-110':
                  currentCustomer.customerId === customer.customerId,
              }"
            >
              <UChip
                size="md"
                position="bottom-right"
                inset
                :ui="{
                  base: '-mx-2 -my-0.5 rounded-none ring-0',
                  background: '',
                }"
              >
                <div @click="onChangeCustomer(customer)">
                  <UAvatar
                    :ui="{
                      rounded: 'rounded-xl',
                      background: 'bg-gray-300 dark:bg-gray-400',
                      placeholder:
                        'text-md font-semibold text-gray-700 dark:text-gray-800 uppercase',
                      chip: {
                        position: {
                          'top-right': '-top-1 -right-1',
                        },
                        size: {
                          lg: 'h-4 w-4 text-xs',
                        },
                      },
                      size: {
                        lg: 'h-14 w-14',
                      },
                    }"
                    :src="customer.basic?.customerImage"
                    :alt="customer.basic?.customerName"
                    size="lg"
                    :chip-color="customer.openCaseCount ? 'red' : undefined"
                    :chip-text="`${customer.openCaseCount}`"
                    chip-position="top-right"
                  />
                </div>
                <template #content>
                  <div
                    v-if="customer.unReadMessages?.length"
                    class="flex flex-row -space-x-0.5"
                  >
                    <div
                      v-for="message in customer.unReadMessages"
                      class="relative hover:z-10 transition-all duration-300 ease-in-out hover:shadow-lg hover:scale-150"
                      @click="onReadMessage(message)"
                    >
                      <UAvatar
                        :src="message.senderAvatar"
                        alt="Avatar"
                        size="2xs"
                        :ui="{
                          rounded: 'rounded-full',
                          wrapper: 'border',
                        }"
                        class="animate__animated animate__heartBeat shadow-md animate__repeat-3"
                      />
                    </div>
                  </div>
                </template>
              </UChip>
            </div>
          </UTooltip>
        </template>
      </draggable>
      <!-- <UTooltip
        v-for="(customer, index) in customers"
        v-else
        :key="customer.customerId"
        :text="customer.basic?.customerName"
        :popper="{ arrow: true, placement: 'right' }"
      >
        <div
          class="relative p-0.5 h-16 w-16 cursor-pointer rounded-2xl border-2 border-transparent hover:border-gray-300 dark:hover:border-gray-700 hover:scale-125"
          :class="{
            '!border-gray-50 !dark:border-gray-300 scale-110':
              currentCustomer.customerId === customer.customerId,
          }"
        >
          <UChip
            size="md"
            position="bottom-right"
            inset
            :ui="{ base: '-mx-2 -my-0.5 rounded-none ring-0', background: '' }"
          >
            <div @click="onChangeCustomer(customer)">
              <UAvatar
                :ui="{
                  rounded: 'rounded-xl',
                  background: 'bg-gray-300 dark:bg-gray-400',
                  placeholder:
                    'text-md font-semibold text-gray-700 dark:text-gray-800 uppercase',
                  chip: {
                    position: {
                      'top-right': '-top-1 -right-1',
                    },
                    size: {
                      lg: 'h-4 w-4 text-xs',
                    },
                  },
                  size: {
                    lg: 'h-14 w-14',
                  },
                }"
                :src="customer.basic?.customerImage"
                :alt="customer.basic?.customerName"
                size="lg"
                :chip-color="customer.openCaseCount ? 'red' : undefined"
                :chip-text="`${customer.openCaseCount}`"
                chip-position="top-right"
              />
            </div>
            <template #content>
              <div
                v-if="customer.unReadMessages?.length"
                class="flex flex-row -space-x-0.5"
              >
                <div
                  v-for="message in customer.unReadMessages"
                  class="relative hover:z-10 transition-all duration-300 ease-in-out hover:shadow-lg hover:scale-150"
                  @click="onReadMessage(message)"
                >
                  <UAvatar
                    :src="message.senderAvatar"
                    alt="Avatar"
                    size="2xs"
                    :ui="{
                      rounded: 'rounded-full',
                      wrapper: 'border',
                    }"
                    class="animate__animated animate__heartBeat shadow-md animate__repeat-3"
                  />
                </div>
              </div>
            </template>
          </UChip>
        </div>
      </UTooltip> -->
      <div
        class="fixed border-t pt-1 border-primary-400 dark:border-gray-500 bottom-0 flex flex-col justify-center items-center"
      >
        <div
          class="relative p-0.5 h-14 w-14 justify-center items-center cursor-pointer rounded-2xl border-2 border-transparent hover:border-primary-300 dark:hover:border-primary-700"
          :class="{ 'border-primary-50 dark:border-gray-300': false }"
        >
          <AppAuthUser />
        </div>
        <div class="grid grid-cols-2">
          <UButton
            icon="i-gala-menu-left"
            size="xs"
            color="gray"
            square
            variant="ghost"
            class="text-white"
            @click="isHideAppNavigation = !isHideAppNavigation"
          />
          <UButton
            icon="i-uiw-menu-fold"
            size="xs"
            color="gray"
            square
            variant="ghost"
            class="text-white"
            @click="
              isHideAppSubNavigation = !isHideAppSubNavigation;
              isSubNavigationMini = true;
            "
          />
        </div>
      </div>
    </div>
  </aside>
</template>
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppCustomersStore } from "~/stores/app/customers";
import { useCasesStore } from "~/stores/app/cases";
import { useCounselorsStore } from "~/stores/app/counselors";
import type { CaseChatMessage, Customer } from "~/types";
import draggable from "vuedraggable";
import { cloneDeep } from "lodash";

let autoIntervals: any[] = [];
const router = useRouter();
const route = useRoute();
const appUIStore = useAppUIStore();
const { isHideAppNavigation, isHideAppSubNavigation, isSubNavigationMini } = storeToRefs(
  appUIStore
);

const appCustomersStore = useAppCustomersStore();
const {
  customers,
  loadings,
  currentCustomer,
  customersSorted,
  customerIndexes,
  currentCustomerId,
} = storeToRefs(appCustomersStore);
const authStore = useAuthStore();
const { getCustomerRole } = storeToRefs(authStore);

const onChangeCustomer = (customer: Customer) => {
  currentCustomer.value = customer;
  if (route.name !== "app") {
    navigateTo("/app");
  }
};

watch(
  () => route.name,
  () => {
    if (currentCustomer.value?.customerId) {
      router.push({ query: { customerId: currentCustomer.value?.customerId } });
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
// const currentCustomerId = ref(route.query.customerId as string);

onMounted(async () => {
  await appCustomersStore.fetchCustomers();
  const runtimeConfig = useRuntimeConfig();
  currentCustomerId.value = route.query.customerId as string;
  // auto refresh
  setInterval(() => {
    appCustomersStore.fetchCustomers(false);
  }, runtimeConfig.public.autoRefreshs.customers);

  // const customerId = route.query.customerId as string;
  if (currentCustomerId.value) {
    const customer = customers.value.find(
      (customer) => customer.customerId === currentCustomerId.value
    );
    if (customer) {
      currentCustomer.value = customer;

      permissionsStore.setPermissionsAndRoles(customer.customerId as string);
      appUIStore.updateTheme(customer);
      casesStore.fetchCaseStatistics(customer.customerId as string);
      counselorsStore.fetchCounselors(customer.customerId as string);

      // auto refresh
      const updateStatistics = setInterval(() => {
        casesStore.fetchCaseStatistics(customer.customerId as string, false);
      }, runtimeConfig.public.autoRefreshs.statistics);
      autoIntervals.push(updateStatistics);
      // auto refresh
      const updateCounselors = setInterval(() => {
        counselorsStore.fetchCounselors(customer.customerId as string, false);
      }, runtimeConfig.public.autoRefreshs.counselors);
      autoIntervals.push(updateCounselors);
    }
  } else {
    currentCustomer.value = customers.value[0];
    currentCustomerId.value = currentCustomer.value?.customerId as string;
  }
});

onBeforeUnmount(() => {
  autoIntervals.forEach((interval) => {
    clearInterval(interval);
  });
});

const casesStore = useCasesStore();
const counselorsStore = useCounselorsStore();
const permissionsStore = usePermissionsStore();
const { readCaseId } = storeToRefs(casesStore);
watch(currentCustomer, (customer: Customer, oldCustomer: Customer) => {
  if (customer && customer.customerId !== oldCustomer?.customerId) {
    currentCustomerId.value = customer.customerId as string;
    permissionsStore.setPermissionsAndRoles(customer.customerId as string);
    appUIStore.updateTheme(customer);
    casesStore.fetchCaseStatistics(customer.customerId as string);
    counselorsStore.fetchCounselors(customer.customerId as string);

    // add cusomterId to route query
    const query = { customerId: customer.customerId };
    router.push({ query });
  }
});

const onReadMessage = (message: CaseChatMessage) => {
  appCustomersStore.readMessage(message.customerId, message.caseId);
  const customer = customers.value.find(
    (customer) => customer.customerId === message.customerId
  );
  if (customer) {
    currentCustomer.value = customer;
    readCaseId.value = message.caseId as string;
    router.push("/app/chats?caseId=" + message.caseId);
  }
};

const drag = ref(false);
const dragOptions = computed(() => ({
  animation: 200,
  group: "description",
  disabled: false,
  ghostClass: "ghost",
  dragClass: "no-move",
}));

const onStartDrag = () => {
  drag.value = true;
};
const onEndDrag = () => {
  drag.value = false;
  nextTick(() => {
    // update customerIndexes
    const customerIds = customersSorted.value.map((customer) => customer.customerId);
    const indexes = customerIds.reduce((acc, customerId, index) => {
      acc[customerId as string] = index;
      return acc;
    }, {} as Record<string, number>);
    customerIndexes.value = cloneDeep(indexes);
  });
};
</script>
