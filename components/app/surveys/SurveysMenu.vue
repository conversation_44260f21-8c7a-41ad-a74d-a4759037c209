<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppUIStore } from '~/stores/app/ui'
const appUIStore = useAppUIStore()
const { isSubNavigationMini } = storeToRefs(appUIStore)
const links = [
  {
    name: 'app-surveys',
    icon: 'i-ri-survey-line',
    label: 'アンケート結果',
    to: '/app/surveys',
    exact: true
  },
  {
    name: 'app-surveys-list',
    icon: 'i-fluent-form-24-regular',
    label: 'アンケート一覧',
    to: '/app/surveys/list'
  },
]

const menuList = computed(() => {
  return links.map((link) => {
    const menu = { ...link }
    if (isSubNavigationMini.value) {
      delete menu.label
      delete menu.badge
    }
    return menu
  })
})
</script>

<template>
  <UVerticalNavigation :links="menuList" :ui="{ size: 'text-sm' }" />
</template>
