<script setup>
import { storeToRefs } from "pinia";
import { useSurveysStore } from "~/stores/app/surveys";
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["close", "select"]);
const surveysStore = useSurveysStore();
const { surveyTemplatesForPalette } = storeToRefs(surveysStore);

const commandPaletteRef = ref();

function onSelect(option) {
  emit("select", option);
}
</script>

<template>
  <UModal :model-value="props.isOpen" @close="emit('close')">
    <UCommandPalette
      ref="commandPaletteRef"
      :groups="[
        { key: 'survey-templates', commands: surveyTemplatesForPalette || [] },
      ]"
      :autoselect="false"
      :fuse="{ resultLimit: 100, fuseOptions: { threshold: 0.1 } }"
      placeholder="アンケートテンプレート名で検索"
      :ui="{
        container: 'max-h-[50vh] hidden-scrollbar',
        group: {
          command: {
            avatar: {
              size: '3xs',
            },
          },
        },
      }"
      @update:model-value="onSelect"
    >
      <template #consultants-icon="{ command }">
        <UAvatar
          v-bind="command.avatar"
          size="xs"
          loading="lazy"
          :ui="{
            rounded: 'rounded-lg',
            background: 'bg-gray-300 dark:bg-gray-400',
            placeholder:
              'text-xs font-semibold text-gray-700 dark:text-gray-800',
            chip: {
              size: {
                xs: 'h-2 w-2',
              },
            },
          }"
        />
      </template>
      <template #consultants-command="{ command }">
        <div class="flex items-center justify-between w-full space-x-4">
          <div>{{ command.label }}</div>
          <div>
            <UBadge
              size="xs"
              variant="soft"
              :color="getConsultantRoleColor(command.role)"
            >
              {{ $t(command.role) }}
            </UBadge>
          </div>
        </div>
      </template>
    </UCommandPalette>
  </UModal>
</template>
