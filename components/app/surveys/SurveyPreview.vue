<script setup lang="ts">
import type { SurveyFormElement } from "@/types";
import { useAutomaticChatsStore } from "~/stores/app/automatic-chats";
import { storeToRefs } from "pinia";

const automaticChatsStore = useAutomaticChatsStore();
const { settings, loadings, activeSettingAutoMessage } =
  storeToRefs(automaticChatsStore);

const props = defineProps<{
  formTemplate: SurveyFormElement[];
  activeElement: SurveyFormElement;
}>();
const surveyPreviewRef = ref(null as any);

watch(
  () => props.activeElement,
  () => {
    // scroll survey preview to active element
    nextTick(() => {
      const element = document.getElementById(props.activeElement._id);
      if (element) {
        // smooth scroll
        surveyPreviewRef.value.scrollTo({
          top: element.offsetTop,
          behavior: "smooth",
        });
      }
    });
  }
);
</script>

<template>
  <BaseiPhone12Mockup class="sticky top-[70px]" bgColor="bg-gray-100 dark:bg-gray-900">
    <div
      ref="surveyPreviewRef"
      class="py-2 overflow-auto hidden-scrollbar pb-20"
    >
      <FormSurveyRender
        :formTemplate="formTemplate"
        :activeElement="activeElement"
      />
    </div>
  </BaseiPhone12Mockup>
</template>
