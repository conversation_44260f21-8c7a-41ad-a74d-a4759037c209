<template>
  <div class="scenario-map-container">
    <!-- Map Controls -->
    <div class="map-controls">
      <div class="flex items-center space-x-2">
        <UButton
          size="xs"
          variant="soft"
          icon="i-heroicons-magnifying-glass-plus"
          @click="zoomIn"
          title="ズームイン"
        />
        <UButton
          size="xs"
          variant="soft"
          icon="i-heroicons-magnifying-glass-minus"
          @click="zoomOut"
          title="ズームアウト"
        />
        <UButton
          size="xs"
          variant="soft"
          icon="i-heroicons-arrow-path"
          @click="resetZoom"
          title="リセット"
        />
        <div class="text-xs text-gray-500">
          {{ Math.round(zoomLevel * 100) }}%
        </div>
      </div>
    </div>

    <div class="scenario-map" ref="mapContainer" :style="{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }">
      <!-- Start Node -->
      <div class="node start-node" :style="{ left: '50px', top: '20px' }">
        <div class="node-content">
          <UIcon name="i-heroicons-play" class="text-green-500" />
          <span class="text-sm font-medium">開始</span>
        </div>
      </div>

      <!-- Question Nodes -->
      <div
        v-for="(question, qIndex) in questions"
        :key="question.id"
        class="node question-node"
        :class="{ 'first-question': question.isFirstQuestion }"
        :style="getQuestionPosition(qIndex)"
        @click="$emit('edit-question', qIndex)"
        :title="`設問${qIndex + 1}: ${question.text || '未設定'}`"
      >
        <div class="node-content">
          <div class="flex items-center justify-between mb-2">
            <UBadge
              :color="question.isFirstQuestion ? 'yellow' : 'blue'"
              variant="soft"
              size="xs"
            >
              設問{{ qIndex + 1 }}
              {{ question.isFirstQuestion ? '(最初)' : '' }}
            </UBadge>
            <div class="flex space-x-1">
              <UButton
                size="xs"
                variant="ghost"
                icon="i-heroicons-pencil"
                @click.stop="$emit('edit-question', qIndex)"
                title="編集"
              />
              <UButton
                v-if="questions.length > 1"
                size="xs"
                variant="ghost"
                icon="i-heroicons-trash"
                color="red"
                @click.stop="$emit('delete-question', qIndex)"
                title="削除"
              />
            </div>
          </div>
          <div class="text-sm font-medium mb-2 line-clamp-2">
            {{ question.text || '未設定' }}
          </div>
          <div class="text-xs text-gray-500">
            選択肢: {{ question.choices.length }}個
          </div>
        </div>
      </div>

      <!-- Choice Nodes -->
      <template v-for="(question, qIndex) in questions" :key="`choices-${question.id}`">
        <div
          v-for="(choice, cIndex) in question.choices"
          :key="choice.id"
          class="node choice-node"
          :style="getChoicePosition(qIndex, cIndex)"
          @click="$emit('edit-choice', qIndex, cIndex)"
          :title="`選択肢${cIndex + 1}: ${choice.text || '未設定'}\n挙動: ${getActionLabel(choice.action.type)}\n${choice.responseMessage ? '返答: ' + choice.responseMessage : ''}`"
        >
          <div class="node-content">
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs font-medium text-gray-600">選択肢{{ cIndex + 1 }}</span>
              <div class="flex items-center space-x-1">
                <UBadge
                  :color="getActionColor(choice.action.type)"
                  variant="soft"
                  size="xs"
                >
                  {{ getActionLabel(choice.action.type) }}
                </UBadge>
                <UButton
                  size="xs"
                  variant="ghost"
                  icon="i-heroicons-pencil"
                  @click.stop="$emit('edit-choice', qIndex, cIndex)"
                  title="編集"
                />
              </div>
            </div>
            <div class="text-xs line-clamp-2 mb-1">
              {{ choice.text || '未設定' }}
            </div>
            <div v-if="choice.responseMessage" class="text-xs text-gray-500 italic line-clamp-1">
              返答: {{ choice.responseMessage }}
            </div>
          </div>
        </div>
      </template>

      <!-- End Nodes -->
      <div 
        v-for="(endNode, index) in endNodes" 
        :key="`end-${index}`"
        class="node end-node"
        :style="getEndNodePosition(index)"
      >
        <div class="node-content">
          <UIcon :name="endNode.icon" :class="endNode.iconClass" />
          <span class="text-xs font-medium">{{ endNode.label }}</span>
        </div>
      </div>

      <!-- SVG for connections -->
      <svg class="connections-svg" :width="svgWidth" :height="svgHeight">
        <!-- Start to first question -->
        <path
          v-if="questions.length > 0"
          :d="getConnectionPath(startPosition, getQuestionPositionData(0))"
          class="connection-line"
          marker-end="url(#arrowhead)"
        />

        <!-- Question to choices -->
        <template v-for="(question, qIndex) in questions" :key="`q-connections-${question.id}`">
          <path
            v-for="(choice, cIndex) in question.choices"
            :key="`connection-${choice.id}`"
            :d="getConnectionPath(getQuestionPositionData(qIndex), getChoicePositionData(qIndex, cIndex))"
            class="connection-line"
            marker-end="url(#arrowhead)"
          />
        </template>

        <!-- Choice to next question/end -->
        <template v-for="(question, qIndex) in questions" :key="`c-connections-${question.id}`">
          <path
            v-for="(choice, cIndex) in question.choices"
            :key="`next-connection-${choice.id}`"
            :d="getNextConnectionPath(qIndex, cIndex, choice)"
            class="connection-line"
            :class="getConnectionClass(choice.action.type)"
            marker-end="url(#arrowhead)"
          />
        </template>

        <!-- Arrow marker definition -->
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              class="fill-gray-400"
            />
          </marker>
        </defs>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ScenarioQuestion, ScenarioChoice, ScenarioActionType } from '@/types'

interface Props {
  questions: ScenarioQuestion[]
}

interface EndNode {
  label: string
  icon: string
  iconClass: string
  type: ScenarioActionType
}

const props = defineProps<Props>()

defineEmits<{
  'edit-question': [index: number]
  'edit-choice': [questionIndex: number, choiceIndex: number]
  'delete-question': [index: number]
}>()

const mapContainer = ref<HTMLElement>()
const svgWidth = ref(1200)
const svgHeight = ref(800)
const zoomLevel = ref(1)

// Node dimensions
const nodeWidth = 180
const nodeHeight = 80
const questionSpacing = 250
const choiceSpacing = 120
const levelSpacing = 200

// Start position
const startPosition = { x: 50 + nodeWidth / 2, y: 20 + nodeHeight / 2 }

// Action labels and colors
const actionLabels: Record<ScenarioActionType, string> = {
  'show_end_survey': 'アンケート',
  'select_end_template': 'テンプレート',
  'open_talk': 'トーク',
  'next_question': '次の設問',
  'end_scenario': '終了'
}

const actionColors: Record<ScenarioActionType, string> = {
  'show_end_survey': 'purple',
  'select_end_template': 'blue',
  'open_talk': 'green',
  'next_question': 'orange',
  'end_scenario': 'red'
}

const getActionLabel = (actionType: ScenarioActionType): string => {
  return actionLabels[actionType] || actionType
}

const getActionColor = (actionType: ScenarioActionType): string => {
  return actionColors[actionType] || 'gray'
}

// End nodes based on actions used
const endNodes = computed<EndNode[]>(() => {
  const usedActions = new Set<ScenarioActionType>()
  
  props.questions.forEach(question => {
    question.choices.forEach(choice => {
      if (choice.action.type !== 'next_question') {
        usedActions.add(choice.action.type)
      }
    })
  })

  const endNodeMap: Record<ScenarioActionType, EndNode> = {
    'show_end_survey': {
      label: '終了アンケート',
      icon: 'i-heroicons-clipboard-document-list',
      iconClass: 'text-purple-500',
      type: 'show_end_survey'
    },
    'select_end_template': {
      label: '終了テンプレート',
      icon: 'i-heroicons-document-text',
      iconClass: 'text-blue-500',
      type: 'select_end_template'
    },
    'open_talk': {
      label: '1:1トーク',
      icon: 'i-heroicons-chat-bubble-left-right',
      iconClass: 'text-green-500',
      type: 'open_talk'
    },
    'end_scenario': {
      label: 'シナリオ終了',
      icon: 'i-heroicons-stop',
      iconClass: 'text-red-500',
      type: 'end_scenario'
    }
  }

  return Array.from(usedActions).map(action => endNodeMap[action])
})

// Position calculations
const getQuestionPosition = (qIndex: number) => {
  const x = 50
  const y = 120 + qIndex * questionSpacing
  return { left: `${x}px`, top: `${y}px` }
}

const getQuestionPositionData = (qIndex: number) => {
  const x = 50 + nodeWidth / 2
  const y = 120 + qIndex * questionSpacing + nodeHeight / 2
  return { x, y }
}

const getChoicePosition = (qIndex: number, cIndex: number) => {
  const x = 300
  const y = 120 + qIndex * questionSpacing + cIndex * choiceSpacing - (nodeHeight / 2)
  return { left: `${x}px`, top: `${y}px` }
}

const getChoicePositionData = (qIndex: number, cIndex: number) => {
  const x = 300 + nodeWidth / 2
  const y = 120 + qIndex * questionSpacing + cIndex * choiceSpacing
  return { x, y }
}

const getEndNodePosition = (index: number) => {
  const x = 600
  const y = 120 + index * 150
  return { left: `${x}px`, top: `${y}px` }
}

const getEndNodePositionData = (index: number) => {
  const x = 600 + nodeWidth / 2
  const y = 120 + index * 150 + nodeHeight / 2
  return { x, y }
}

// Connection path calculations
const getConnectionPath = (from: { x: number; y: number }, to: { x: number; y: number }) => {
  const midX = (from.x + to.x) / 2
  return `M ${from.x} ${from.y} Q ${midX} ${from.y} ${to.x} ${to.y}`
}

const getNextConnectionPath = (qIndex: number, cIndex: number, choice: ScenarioChoice) => {
  const choicePos = getChoicePositionData(qIndex, cIndex)
  
  if (choice.action.type === 'next_question' && choice.action.nextQuestionId) {
    // Find the target question
    const targetQIndex = props.questions.findIndex(q => q.id === choice.action.nextQuestionId)
    if (targetQIndex !== -1) {
      const targetPos = getQuestionPositionData(targetQIndex)
      return getConnectionPath(choicePos, targetPos)
    }
  } else {
    // Connect to appropriate end node
    const endNodeIndex = endNodes.value.findIndex(node => node.type === choice.action.type)
    if (endNodeIndex !== -1) {
      const endPos = getEndNodePositionData(endNodeIndex)
      return getConnectionPath(choicePos, endPos)
    }
  }
  
  return ''
}

const getConnectionClass = (actionType: ScenarioActionType) => {
  return `connection-${actionType}`
}

// Zoom functions
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

onMounted(() => {
  // Calculate SVG dimensions based on content
  const maxQuestions = props.questions.length
  const maxChoicesPerQuestion = Math.max(...props.questions.map(q => q.choices.length), 0)

  svgWidth.value = Math.max(1200, 800 + endNodes.value.length * 200)
  svgHeight.value = Math.max(800, 200 + maxQuestions * questionSpacing + maxChoicesPerQuestion * choiceSpacing)
})
</script>

<style scoped>
.scenario-map-container {
  @apply w-full h-full overflow-auto bg-gray-50 dark:bg-gray-900 rounded-lg border relative;
}

.map-controls {
  @apply absolute top-4 right-4 z-10 bg-white dark:bg-gray-800 rounded-lg shadow-md p-2 border;
}

.scenario-map {
  @apply relative min-w-full min-h-full p-4;
  min-width: 1200px;
  min-height: 800px;
}

.node {
  @apply absolute border-2 rounded-lg bg-white dark:bg-gray-800 shadow-md cursor-pointer transition-all duration-200;
  width: 180px;
  min-height: 80px;
}

.node:hover {
  @apply shadow-lg transform scale-105;
}

.start-node {
  @apply border-green-300 bg-green-50 dark:bg-green-900/20;
}

.question-node {
  @apply border-blue-300 bg-blue-50 dark:bg-blue-900/20;
}

.question-node.first-question {
  @apply border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20;
}

.choice-node {
  @apply border-gray-300 bg-gray-50 dark:bg-gray-800;
}

.end-node {
  @apply border-red-300 bg-red-50 dark:bg-red-900/20;
}

.node-content {
  @apply p-3 h-full flex flex-col justify-center;
}

.connections-svg {
  @apply absolute top-0 left-0 pointer-events-none;
  z-index: 1;
}

.connection-line {
  @apply stroke-gray-400 stroke-2 fill-none;
}

.connection-next_question {
  @apply stroke-orange-400;
}

.connection-show_end_survey {
  @apply stroke-purple-400;
}

.connection-select_end_template {
  @apply stroke-blue-400;
}

.connection-open_talk {
  @apply stroke-green-400;
}

.connection-end_scenario {
  @apply stroke-red-400;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
