<template>
  <div class="flex flex-col h-full bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <UIcon name="i-fluent-emoji-robot" class="text-2xl" />
          <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              シナリオプレビュー
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              チャットボットの動作を確認できます
            </p>
          </div>
        </div>

        <!-- Debug Toggle -->
        <div class="flex items-center space-x-2">
          <UTooltip text="デバッグ情報を表示/非表示">
            <UButton
              :variant="debugMode ? 'solid' : 'outline'"
              color="gray"
              icon="i-heroicons-bug-ant"
              size="sm"
              @click="toggleDebugMode"
            />
          </UTooltip>
        </div>
      </div>
    </div>

    <!-- Chat Container -->
    <div class="flex-1 flex flex-col min-h-0">
      <!-- Messages Area -->
      <div 
        ref="messagesContainer"
        class="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth"
      >
        <!-- Welcome Message -->
        <div class="flex items-start space-x-3">
          <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
          <div class="flex-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <p class="text-sm text-gray-700 dark:text-gray-300">
                こんにちは！{{ scenario?.name || 'シナリオ' }}を開始します。
              </p>
            </div>
            <div class="text-xs text-gray-500 mt-1 px-1">
              チャットボット
            </div>
          </div>
        </div>

        <!-- Validation Errors -->
        <div v-if="validationErrors.length > 0" class="space-y-2">
          <div class="flex items-start space-x-3">
            <UIcon name="i-heroicons-exclamation-triangle" class="text-2xl mt-1 text-red-500" />
            <div class="flex-1">
              <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg rounded-bl-none p-3 shadow-sm">
                <div class="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                  シナリオに問題があります
                </div>
                <ul class="text-xs text-red-700 dark:text-red-300 space-y-1">
                  <li v-for="error in validationErrors" :key="error">
                    • {{ error }}
                  </li>
                </ul>
              </div>
              <div class="text-xs text-gray-500 mt-1 px-1">
                システム
              </div>
            </div>
          </div>
        </div>

        <!-- Current Question -->
        <ScenarioQuestionPreview
          v-if="currentQuestion && validationErrors.length === 0"
          :question="currentQuestion"
          :end-templates="endTemplates"
          :show-debug-info="debugMode"
          @choice-selected="handleChoiceSelected"
        />

        <!-- No Questions State -->
        <div v-if="!currentQuestion && !endState && validationErrors.length === 0" class="flex items-start space-x-3">
          <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
          <div class="flex-1">
            <div class="bg-gray-100 dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                設問を追加してシナリオを作成してください。
              </p>
            </div>
            <div class="text-xs text-gray-500 mt-1 px-1">
              チャットボット
            </div>
          </div>
        </div>

        <!-- Previous Messages -->
        <div
          v-for="(message, index) in conversationHistory"
          :key="`history-${index}`"
          class="space-y-2"
        >
          <!-- User Choice -->
          <div class="flex justify-end">
            <div class="bg-primary-600 text-white rounded-lg rounded-br-none p-3 max-w-xs shadow-sm">
              <p class="text-sm">{{ message.userChoice }}</p>
            </div>
          </div>

          <!-- Bot Response -->
          <div v-if="message.botResponse" class="flex items-start space-x-3">
            <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
            <div class="flex-1">
              <div class="bg-white dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  {{ message.botResponse }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- End State Messages -->
        <div v-if="endState" class="space-y-4">
          <div class="flex items-start space-x-3">
            <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
            <div class="flex-1">
              <div class="bg-white dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  {{ getEndStateMessage() }}
                </p>
              </div>
            </div>
          </div>

          <!-- End Template Preview -->
          <EndTemplatePreview
            v-if="endState.type === 'select_end_template' && endState.endTemplate"
            :end-template="endState.endTemplate"
            :show-debug-info="debugMode"
          />
        </div>
      </div>

      <!-- Reset Button -->
      <div class="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <UButton
          variant="outline"
          color="gray"
          icon="i-heroicons-arrow-path"
          @click="resetPreview"
          block
        >
          最初からやり直す
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Scenario, ScenarioQuestion, ScenarioChoice, ScenarioAction, EndTemplate } from '~/types'
import { SCENARIO_ACTION_LABELS } from '~/utils/scenarioConstants'

interface Props {
  scenario?: Scenario | null
  endTemplates?: EndTemplate[]
}

interface ConversationMessage {
  userChoice: string
  botResponse?: string
}

interface EndState {
  type: string
  message: string
  endTemplate?: EndTemplate
}

const props = withDefaults(defineProps<Props>(), {
  scenario: null,
  endTemplates: () => []
})

// Reactive state
const currentQuestion = ref<ScenarioQuestion | null>(null)
const conversationHistory = ref<ConversationMessage[]>([])
const endState = ref<EndState | null>(null)
const messagesContainer = ref<HTMLElement>()
const validationErrors = ref<string[]>([])
const debugMode = ref(false)

// Validate scenario
const validateScenario = () => {
  const errors: string[] = []

  if (!props.scenario) {
    errors.push('シナリオが設定されていません')
    validationErrors.value = errors
    return false
  }

  if (!props.scenario.name?.trim()) {
    errors.push('シナリオ名が入力されていません')
  }

  if (!props.scenario.questions?.length) {
    errors.push('設問が設定されていません')
  } else {
    // Check for first question
    const hasFirstQuestion = props.scenario.questions.some(q => q.isFirstQuestion)
    if (!hasFirstQuestion) {
      errors.push('最初の設問が設定されていません')
    }

    // Validate each question
    props.scenario.questions.forEach((question, index) => {
      if (!question.text?.trim()) {
        errors.push(`設問${index + 1}: 設問内容が入力されていません`)
      }

      if (!question.choices?.length) {
        errors.push(`設問${index + 1}: 選択肢が設定されていません`)
      } else {
        question.choices.forEach((choice, choiceIndex) => {
          if (!choice.text?.trim()) {
            errors.push(`設問${index + 1} 選択肢${choiceIndex + 1}: 選択肢テキストが入力されていません`)
          }
          if (!choice.action?.type) {
            errors.push(`設問${index + 1} 選択肢${choiceIndex + 1}: アクションが設定されていません`)
          }
        })
      }
    })
  }

  validationErrors.value = errors
  return errors.length === 0
}

// Initialize preview
const initializePreview = () => {
  // Validate first
  if (!validateScenario()) {
    currentQuestion.value = null
    conversationHistory.value = []
    endState.value = null
    return
  }

  if (!props.scenario?.questions?.length) {
    currentQuestion.value = null
    conversationHistory.value = []
    endState.value = null
    return
  }

  // Find first question
  const firstQuestion = props.scenario.questions.find(q => q.isFirstQuestion)
  currentQuestion.value = firstQuestion || props.scenario.questions[0]
  conversationHistory.value = []
  endState.value = null
}

// Handle choice selection
const handleChoiceSelected = (choice: ScenarioChoice) => {
  if (!currentQuestion.value) return

  // Add to conversation history
  const message: ConversationMessage = {
    userChoice: choice.text,
    botResponse: choice.responseMessage
  }
  conversationHistory.value.push(message)

  // Process action
  processAction(choice.action)

  // Scroll to bottom
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// Process scenario action
const processAction = (action: ScenarioAction) => {
  switch (action.type) {
    case 'next_question':
      const nextQuestion = props.scenario?.questions.find(q => q.id === action.nextQuestionId)
      currentQuestion.value = nextQuestion || null
      break

    case 'select_end_template':
      const endTemplate = props.endTemplates.find(t => t.id === action.endTemplateId)
      endState.value = {
        type: 'select_end_template',
        message: '終了テンプレートが表示されます',
        endTemplate
      }
      currentQuestion.value = null
      break

    case 'show_end_survey':
      endState.value = {
        type: 'show_end_survey',
        message: '終了アンケートが表示されます'
      }
      currentQuestion.value = null
      break

    case 'open_talk':
      endState.value = {
        type: 'open_talk',
        message: '1:1トークが開設されます'
      }
      currentQuestion.value = null
      break

    case 'end_scenario':
      endState.value = {
        type: 'end_scenario',
        message: 'シナリオが終了しました'
      }
      currentQuestion.value = null
      break
  }
}

// Get end state message
const getEndStateMessage = () => {
  return endState.value?.message || 'シナリオが終了しました'
}

// Reset preview
const resetPreview = () => {
  initializePreview()
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = 0
    }
  })
}

// Toggle debug mode
const toggleDebugMode = () => {
  debugMode.value = !debugMode.value
}

// Watch for scenario changes
watch(() => props.scenario, () => {
  initializePreview()
}, { immediate: true, deep: true })

// Initialize on mount
onMounted(() => {
  initializePreview()
})
</script>
