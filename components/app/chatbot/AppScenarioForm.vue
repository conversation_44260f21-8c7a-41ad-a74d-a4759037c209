<template>
  <form @submit.prevent="handleSave" class="space-y-8">
      <!-- Scenario Basic Info -->
      <UCard>
        <template #header>
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-document-text" class="text-blue-500" />
            <h3 class="text-lg font-semibold">基本情報</h3>
          </div>
        </template>
        
        <div class="space-y-4">
          <UFormGroup
            label="シナリオ名"
            required
            :error="formErrors.name"
          >
            <UInput
              v-model="form.name"
              placeholder="シナリオ名を入力してください"
              :maxlength="SCENARIO_VALIDATION.NAME_MAX_LENGTH"
              @blur="validateField('name')"
            />
            <template #hint>
              {{ form.name.length }}/{{ SCENARIO_VALIDATION.NAME_MAX_LENGTH }}文字
            </template>
          </UFormGroup>

          <UFormGroup label="説明（任意）">
            <UTextarea
              v-model="form.description"
              placeholder="シナリオの説明を入力してください"
              rows="3"
            />
          </UFormGroup>

          <UFormGroup label="状態">
            <UToggle
              v-model="form.isActive"
              label="有効"
            >
              <template #label>
                {{ form.isActive ? '有効' : '無効' }}
              </template>
            </UToggle>
          </UFormGroup>
        </div>
      </UCard>

      <!-- Questions -->
      <UCard>
        <template #header>
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <UIcon name="i-heroicons-chat-bubble-left-right" class="text-green-500" />
              <h3 class="text-lg font-semibold">設問設定</h3>
            </div>
            <UButton
              icon="i-heroicons-plus"
              variant="ghost"
              color="primary"
              size="sm"
              @click="addQuestion"
            >
              設問を追加
            </UButton>
          </div>
        </template>

        <div v-if="form.questions.length === 0" class="text-center py-8">
          <div class="text-gray-500 dark:text-gray-400">
            <UIcon name="i-heroicons-question-mark-circle" class="w-12 h-12 mx-auto mb-4" />
            <p class="text-lg font-medium">設問がありません</p>
            <p class="mt-2">最初の設問を追加してください</p>
          </div>
        </div>

        <div v-else class="space-y-6">
          <div
            v-for="(question, questionIndex) in form.questions"
            :key="question.tempId || question.id"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
            :class="{ 'border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20': question.isFirstQuestion }"
          >
            <div class="flex justify-between items-start mb-4">
              <div class="flex items-center space-x-2">
                <span class="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {{ questionIndex + 1 }}
                </span>
                <span v-if="question.isFirstQuestion" class="text-yellow-600 dark:text-yellow-400 text-sm font-medium">
                  最初の設問
                </span>
              </div>
              <div class="flex space-x-2">
                <UButton
                  v-if="!question.isFirstQuestion"
                  icon="i-heroicons-star"
                  variant="ghost"
                  color="yellow"
                  size="xs"
                  @click="setAsFirstQuestion(questionIndex)"
                >
                  最初に設定
                </UButton>
                <UButton
                  icon="i-heroicons-trash"
                  variant="ghost"
                  color="red"
                  size="xs"
                  @click="removeQuestion(questionIndex)"
                />
              </div>
            </div>

            <!-- Question Text -->
            <UFormGroup
              label="設問内容"
              required
              :error="formErrors.questions?.[questionIndex]?.text"
            >
              <UInput
                v-model="question.text"
                placeholder="設問内容を入力してください"
                :maxlength="SCENARIO_VALIDATION.QUESTION_MAX_LENGTH"
                @blur="validateQuestion(questionIndex, 'text')"
              />
              <template #hint>
                {{ question.text.length }}/{{ SCENARIO_VALIDATION.QUESTION_MAX_LENGTH }}文字
              </template>
            </UFormGroup>

            <!-- Choices -->
            <div class="mt-4">
              <div class="flex justify-between items-center mb-3">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  選択肢 <span class="text-red-500">*</span>
                </label>
                <UButton
                  icon="i-heroicons-plus"
                  variant="ghost"
                  color="primary"
                  size="xs"
                  :disabled="question.choices.length >= SCENARIO_VALIDATION.MAX_CHOICES_PER_QUESTION"
                  @click="addChoice(questionIndex)"
                >
                  選択肢を追加
                </UButton>
              </div>

              <div v-if="question.choices.length === 0" class="text-center py-4 border border-dashed border-gray-300 dark:border-gray-600 rounded">
                <p class="text-sm text-gray-500 dark:text-gray-400">選択肢を追加してください</p>
              </div>

              <div v-else class="space-y-3">
                <div
                  v-for="(choice, choiceIndex) in question.choices"
                  :key="choice.tempId || choice.id"
                  class="border border-gray-200 dark:border-gray-600 rounded p-3 bg-gray-50 dark:bg-gray-800/50"
                >
                  <div class="flex justify-between items-start mb-3">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      選択肢 {{ choiceIndex + 1 }}
                    </span>
                    <UButton
                      icon="i-heroicons-trash"
                      variant="ghost"
                      color="red"
                      size="xs"
                      @click="removeChoice(questionIndex, choiceIndex)"
                    />
                  </div>

                  <!-- Choice Text -->
                  <UFormGroup
                    label="選択肢テキスト"
                    required
                    :error="formErrors.questions?.[questionIndex]?.choices?.[choiceIndex]?.text"
                  >
                    <UInput
                      v-model="choice.text"
                      placeholder="選択肢のテキストを入力"
                      :maxlength="SCENARIO_VALIDATION.CHOICE_MAX_LENGTH"
                      @blur="validateChoice(questionIndex, choiceIndex, 'text')"
                    />
                    <template #hint>
                      {{ choice.text.length }}/{{ SCENARIO_VALIDATION.CHOICE_MAX_LENGTH }}文字
                    </template>
                  </UFormGroup>

                  <!-- Choice Message -->
                  <UFormGroup
                    label="選択時のメッセージ（任意）"
                    class="mt-3"
                  >
                    <UInput
                      v-model="choice.responseMessage"
                      placeholder="選択された時に表示するメッセージ"
                    />
                  </UFormGroup>

                  <!-- Choice Action -->
                  <UFormGroup
                    label="選択肢の挙動"
                    required
                    :error="formErrors.questions?.[questionIndex]?.choices?.[choiceIndex]?.action"
                    class="mt-3"
                  >
                    <USelect
                      v-model="choice.action.type"
                      :options="actionOptions"
                      option-attribute="label"
                      value-attribute="value"
                      @update:model-value="onActionTypeChange(questionIndex, choiceIndex)"
                    />
                  </UFormGroup>

                  <!-- Additional Action Fields -->
                  <div v-if="choice.action.type === 'next_question'" class="mt-3">
                    <UFormGroup
                      label="次の設問"
                      required
                      :error="formErrors.questions?.[questionIndex]?.choices?.[choiceIndex]?.nextQuestionId"
                    >
                      <USelect
                        v-model="choice.action.nextQuestionId"
                        :options="getNextQuestionOptions(questionIndex)"
                        option-attribute="label"
                        value-attribute="value"
                        placeholder="次の設問を選択"
                      />
                    </UFormGroup>
                  </div>

                  <div v-if="choice.action.type === 'select_end_template'" class="mt-3">
                    <UFormGroup
                      label="終了テンプレート"
                      required
                      :error="formErrors.questions?.[questionIndex]?.choices?.[choiceIndex]?.endTemplateId"
                    >
                      <USelect
                        v-model="choice.action.endTemplateId"
                        :options="endTemplateOptions"
                        option-attribute="label"
                        value-attribute="value"
                        placeholder="終了テンプレートを選択"
                        :loading="loadingEndTemplates"
                      />
                    </UFormGroup>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </form>
</template>

<script setup lang="ts">
import type { Scenario, ScenarioQuestion, ScenarioChoice, ScenarioAction, EndTemplate } from '~/types'
import { SCENARIO_VALIDATION, SCENARIO_ACTION_LABELS } from '~/utils/scenarioConstants'

// Extended interfaces for form handling with temporary IDs
interface FormScenarioChoice extends ScenarioChoice {
  tempId?: string;
  order?: number;
}

interface FormScenarioQuestion extends ScenarioQuestion {
  tempId?: string;
  choices: FormScenarioChoice[];
}

interface FormScenario extends Omit<Scenario, 'questions'> {
  questions: FormScenarioQuestion[];
}

interface Props {
  scenario?: Scenario
  saving?: boolean
  endTemplates?: EndTemplate[]
  loadingEndTemplates?: boolean
}

interface Emits {
  (event: 'save', data: Partial<Scenario>): void
  (event: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  saving: false,
  endTemplates: () => [],
  loadingEndTemplates: false
})

const emit = defineEmits<Emits>()

const isEdit = computed(() => !!props.scenario)

// Expose canSave for parent components
defineExpose({
  canSave: computed(() => {
    return form.name.trim() &&
           form.questions.length > 0 &&
           form.questions.some(q => q.isFirstQuestion) &&
           form.questions.every(q =>
             q.text.trim() &&
             q.choices.length > 0 &&
             q.choices.every(c => c.text.trim() && c.action.type)
           )
  })
})

// Form data
const form = reactive<FormScenario>({
  id: props.scenario?.id || '',
  name: props.scenario?.name || '',
  description: props.scenario?.description || '',
  isActive: props.scenario?.isActive ?? true,
  customerId: props.scenario?.customerId,
  createdAt: props.scenario?.createdAt,
  updatedAt: props.scenario?.updatedAt,
  questions: props.scenario?.questions?.map(q => ({
    ...q,
    choices: q.choices.map(c => ({ ...c }))
  })) || []
})

// Form validation errors
const formErrors = reactive<Record<string, any>>({})

// Action options
const actionOptions = computed(() => [
  { value: 'show_end_survey', label: SCENARIO_ACTION_LABELS.show_end_survey },
  { value: 'select_end_template', label: SCENARIO_ACTION_LABELS.select_end_template },
  { value: 'open_talk', label: SCENARIO_ACTION_LABELS.open_talk },
  { value: 'next_question', label: SCENARIO_ACTION_LABELS.next_question },
  { value: 'end_scenario', label: SCENARIO_ACTION_LABELS.end_scenario }
])

// End template options
const endTemplateOptions = computed(() => 
  props.endTemplates.map(template => ({
    value: template.id,
    label: template.templateName
  }))
)



// Generate temporary ID
const generateTempId = () => Math.random().toString(36).substr(2, 9)

// Question management
const addQuestion = () => {
  const newQuestion: FormScenarioQuestion = {
    id: '',
    tempId: generateTempId(),
    text: '',
    choices: [],
    isFirstQuestion: form.questions.length === 0,
    order: form.questions.length + 1
  }
  form.questions.push(newQuestion)
}

const removeQuestion = (index: number) => {
  const question = form.questions[index]
  if (question.isFirstQuestion && form.questions.length > 1) {
    // Set another question as first if removing the first question
    const nextQuestion = form.questions.find((q, i) => i !== index)
    if (nextQuestion) {
      nextQuestion.isFirstQuestion = true
    }
  }
  form.questions.splice(index, 1)
  updateQuestionOrders()
}

const setAsFirstQuestion = (index: number) => {
  form.questions.forEach((q, i) => {
    q.isFirstQuestion = i === index
  })
}

const updateQuestionOrders = () => {
  form.questions.forEach((question, index) => {
    question.order = index + 1
  })
}

// Choice management
const addChoice = (questionIndex: number) => {
  const question = form.questions[questionIndex]
  if (question.choices.length >= SCENARIO_VALIDATION.MAX_CHOICES_PER_QUESTION) {
    return
  }

  const newChoice: FormScenarioChoice = {
    id: '',
    tempId: generateTempId(),
    text: '',
    responseMessage: '',
    order: question.choices.length + 1,
    action: {
      type: 'end_scenario'
    }
  }
  question.choices.push(newChoice)
}

const removeChoice = (questionIndex: number, choiceIndex: number) => {
  form.questions[questionIndex].choices.splice(choiceIndex, 1)
}

const onActionTypeChange = (questionIndex: number, choiceIndex: number) => {
  const choice = form.questions[questionIndex].choices[choiceIndex]
  // Reset action-specific fields when type changes
  choice.action.nextQuestionId = undefined
  choice.action.endTemplateId = undefined
}

const getNextQuestionOptions = (currentQuestionIndex: number) => {
  return form.questions
    .slice(currentQuestionIndex + 1)
    .map((question, index) => ({
      value: question.id || question.tempId,
      label: `設問${currentQuestionIndex + index + 2}: ${question.text || '未設定'}`
    }))
}

// Validation
const validateField = (field: string) => {
  delete formErrors[field]
  
  if (field === 'name') {
    if (!form.name.trim()) {
      formErrors.name = 'シナリオ名は必須です'
    } else if (form.name.length > SCENARIO_VALIDATION.NAME_MAX_LENGTH) {
      formErrors.name = `シナリオ名は${SCENARIO_VALIDATION.NAME_MAX_LENGTH}文字以内で入力してください`
    }
  }
}

const validateQuestion = (questionIndex: number, field: string) => {
  if (!formErrors.questions) formErrors.questions = {}
  if (!formErrors.questions[questionIndex]) formErrors.questions[questionIndex] = {}
  
  const question = form.questions[questionIndex]
  
  if (field === 'text') {
    delete formErrors.questions[questionIndex].text
    if (!question.text.trim()) {
      formErrors.questions[questionIndex].text = '設問内容は必須です'
    } else if (question.text.length > SCENARIO_VALIDATION.QUESTION_MAX_LENGTH) {
      formErrors.questions[questionIndex].text = `設問内容は${SCENARIO_VALIDATION.QUESTION_MAX_LENGTH}文字以内で入力してください`
    }
  }
}

const validateChoice = (questionIndex: number, choiceIndex: number, field: string) => {
  if (!formErrors.questions) formErrors.questions = {}
  if (!formErrors.questions[questionIndex]) formErrors.questions[questionIndex] = {}
  if (!formErrors.questions[questionIndex].choices) formErrors.questions[questionIndex].choices = {}
  if (!formErrors.questions[questionIndex].choices[choiceIndex]) formErrors.questions[questionIndex].choices[choiceIndex] = {}
  
  const choice = form.questions[questionIndex].choices[choiceIndex]
  
  if (field === 'text') {
    delete formErrors.questions[questionIndex].choices[choiceIndex].text
    if (!choice.text.trim()) {
      formErrors.questions[questionIndex].choices[choiceIndex].text = '選択肢テキストは必須です'
    } else if (choice.text.length > SCENARIO_VALIDATION.CHOICE_MAX_LENGTH) {
      formErrors.questions[questionIndex].choices[choiceIndex].text = `選択肢テキストは${SCENARIO_VALIDATION.CHOICE_MAX_LENGTH}文字以内で入力してください`
    }
  }
}

const validateForm = () => {
  formErrors.questions = {}
  
  // Validate basic fields
  validateField('name')
  
  // Validate questions
  form.questions.forEach((question, questionIndex) => {
    validateQuestion(questionIndex, 'text')
    
    question.choices.forEach((choice, choiceIndex) => {
      validateChoice(questionIndex, choiceIndex, 'text')
    })
  })
  
  // Check if there's at least one first question
  if (!form.questions.some(q => q.isFirstQuestion)) {
    if (!formErrors.general) formErrors.general = []
    formErrors.general.push('最初の設問を設定してください')
  }
  
  return Object.keys(formErrors).length === 0
}

const handleSave = () => {
  if (!validateForm()) {
    return
  }
  
  emit('save', {
    ...form,
    questions: form.questions.map(q => ({
      ...q,
      choices: q.choices.map(c => ({
        ...c,
        order: c.order || 1
      }))
    }))
  })
}

// Initialize with one question if creating new scenario
if (!isEdit.value && form.questions.length === 0) {
  addQuestion()
}
</script>