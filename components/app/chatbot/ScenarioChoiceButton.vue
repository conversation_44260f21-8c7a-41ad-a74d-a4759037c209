<template>
  <div class="w-full">
    <UButton
      :variant="isSelected ? 'solid' : 'outline'"
      :color="isSelected ? 'primary' : 'gray'"
      class="w-full text-left justify-start p-3 h-auto min-h-[3rem] transition-all duration-200 hover:shadow-md"
      :class="{
        'bg-primary-50 border-primary-200 hover:bg-primary-100': !isSelected,
        'shadow-lg': isSelected
      }"
      @click="handleClick"
    >
      <div class="flex flex-col items-start space-y-1 w-full">
        <!-- Choice Text -->
        <div class="text-sm font-medium text-left break-words whitespace-normal">
          {{ choice.text }}
        </div>

        <!-- Response Message Preview -->
        <div 
          v-if="choice.responseMessage && showResponsePreview"
          class="text-xs opacity-75 text-left break-words whitespace-normal"
        >
          → {{ choice.responseMessage }}
        </div>

        <!-- Action Info -->
        <div class="flex items-center space-x-2 text-xs opacity-60">
          <UIcon :name="getActionIcon()" class="w-3 h-3" />
          <span>{{ getActionLabel() }}</span>
        </div>
      </div>
    </UButton>

    <!-- Action Details (for debugging) -->
    <div v-if="showDebugInfo" class="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
      <div class="text-gray-600 dark:text-gray-400">
        <div>Action: {{ choice.action.type }}</div>
        <div v-if="choice.action.nextQuestionId">Next Question: {{ choice.action.nextQuestionId }}</div>
        <div v-if="choice.action.endTemplateId">End Template: {{ getEndTemplateName() }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ScenarioChoice, EndTemplate } from '~/types'
import { SCENARIO_ACTION_LABELS } from '~/utils/scenarioConstants'

interface Props {
  choice: ScenarioChoice
  endTemplates?: EndTemplate[]
  isSelected?: boolean
  showResponsePreview?: boolean
  showDebugInfo?: boolean
}

interface Emits {
  (event: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  endTemplates: () => [],
  isSelected: false,
  showResponsePreview: true,
  showDebugInfo: false
})

const emit = defineEmits<Emits>()

// Get action icon based on action type
const getActionIcon = () => {
  switch (props.choice.action.type) {
    case 'show_end_survey':
      return 'i-heroicons-clipboard-document-list'
    case 'select_end_template':
      return 'i-heroicons-document-text'
    case 'open_talk':
      return 'i-heroicons-chat-bubble-left-right'
    case 'next_question':
      return 'i-heroicons-arrow-right'
    case 'end_scenario':
      return 'i-heroicons-stop'
    default:
      return 'i-heroicons-question-mark-circle'
  }
}

// Get action label
const getActionLabel = () => {
  return SCENARIO_ACTION_LABELS[props.choice.action.type] || props.choice.action.type
}

// Get end template name
const getEndTemplateName = () => {
  if (!props.choice.action.endTemplateId) return ''
  const template = props.endTemplates.find(t => t.id === props.choice.action.endTemplateId)
  return template?.templateName || 'Unknown Template'
}

// Handle click
const handleClick = () => {
  emit('click')
}
</script>
