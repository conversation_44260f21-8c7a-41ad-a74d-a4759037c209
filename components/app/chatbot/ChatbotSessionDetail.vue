<template>
  <div class="space-y-6">
    <!-- Session Info -->
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">開始日時</label>
        <p class="text-sm">{{ formatDateTime(session.startTime) }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">チャットボット名</label>
        <p class="text-sm">{{ session.chatbotName }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">友だちID</label>
        <p class="text-sm font-mono">{{ session.friendId }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">プロフィール名</label>
        <p class="text-sm">{{ session.profileName }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">ステータス</label>
        <p class="text-sm">
          <UBadge 
            :color="getStatusColor(session.status)"
            variant="soft"
            size="sm"
          >
            {{ session.status }}
          </UBadge>
        </p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">トーク開設</label>
        <p class="text-sm">
          <UBadge 
            v-if="session.talkOpening !== null"
            :color="getTalkOpeningColor(session.talkOpening)"
            variant="soft"
            size="sm"
          >
            {{ session.talkOpening }}
          </UBadge>
          <span v-else class="text-gray-400">-</span>
        </p>
      </div>
    </div>
    
    <!-- Chat History -->
    <div>
      <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">チャット履歴</label>
      <div class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800 max-h-96 overflow-y-auto">
        <div v-for="(message, index) in session.chatHistory" :key="index" class="mb-4 last:mb-0">
          <div class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div 
                class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium"
                :class="message.isBot ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'"
              >
                {{ message.isBot ? 'BOT' : 'USER' }}
              </div>
            </div>
            <div class="flex-1">
              <div class="text-xs text-gray-500 mb-1">
                {{ formatDateTime(message.timestamp) }}
              </div>
              <div class="text-sm bg-white dark:bg-gray-700 rounded-lg p-3">
                {{ message.content }}
              </div>
              <div v-if="message.selectedChoice" class="text-xs text-gray-500 mt-1">
                選択: {{ message.selectedChoice }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- User Responses Summary -->
    <div v-if="session.userResponses && session.userResponses.length > 0">
      <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">ユーザー回答サマリー</label>
      <div class="border rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
        <div v-for="(response, index) in session.userResponses" :key="index" class="mb-2 last:mb-0">
          <span class="text-sm font-medium">{{ response.question }}:</span>
          <span class="text-sm ml-2">{{ response.answer }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ChatMessage {
  timestamp: Date
  isBot: boolean
  content: string
  selectedChoice?: string | null
}

interface UserResponse {
  question: string
  answer: string
}

interface ChatbotSession {
  id: number
  publishStatus: string
  startTime: Date
  chatbotId: string
  chatbotName: string
  friendId: string
  profileName: string
  status: string
  talkOpening: string | null
  staffResponse: string | null
  chatHistory: ChatMessage[]
  userResponses: UserResponse[]
}

interface Props {
  session: ChatbotSession
}

defineProps<Props>()

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case '対応中': return 'blue'
    case '終了': return 'green'
    case '中断': return 'yellow'
    case '中断（トーク開設）': return 'orange'
    case '中断（タイムアウト）': return 'red'
    default: return 'gray'
  }
}

const getTalkOpeningColor = (talkOpening: string) => {
  switch (talkOpening) {
    case 'なし': return 'gray'
    case '開設': return 'green'
    case '時間外': return 'yellow'
    case '流入制限': return 'red'
    default: return 'gray'
  }
}
</script>
