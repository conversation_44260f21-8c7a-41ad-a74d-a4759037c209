<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppUIStore } from "~/stores/app/ui";
const appUIStore = useAppUIStore();
const { isSubNavigationMini } = storeToRefs(appUIStore);
const userPermissions = usePermissions();

const links = [
  {
    name: "app-chatbot",
    icon: "i-heroicons-list-bullet",
    label: "チャットボット一覧",
    to: "/app/chatbot/",
    exact: true,
  },
  // {
  //   name: "app-chatbot-chatbot",
  //   icon: "i-fluent-emoji-high-contrast-robot",
  //   label: "設定",
  //   to: "/app/chatbot/chatbot",
  // },
  {
    name: "app-chatbot-scenario",
    icon: "i-heroicons-document-text",
    label: "シナリオ",
    to: "/app/chatbot/scenario",
  },
  {
    name: "app-chatbot-end-template",
    icon: "i-heroicons-document-check",
    label: "終了テンプレート",
    to: "/app/chatbot/end-template",
  },
  {
    name: "app-chatbot-logs",
    icon: "i-heroicons-clipboard-document-list",
    label: "ログ",
    to: "/app/chatbot/logs",
  },
];

const menuList = computed(() => {
  return links
    .filter((obj) => userPermissions.value?.includes("read:" + obj.name))
    .map((link) => {
      const menu = { ...link } as any;
      if (isSubNavigationMini.value) {
        delete menu.label;
        delete menu.badge;
      }
      return menu;
    });
});
</script>

<template>
  <div>
    <UVerticalNavigation :links="menuList" :ui="{ size: 'text-sm' }" />
  </div>
</template>
