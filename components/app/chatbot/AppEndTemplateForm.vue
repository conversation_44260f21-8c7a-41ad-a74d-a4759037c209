<template>
  <form @submit.prevent="handleSave" class="space-y-8">
    <!-- Basic Information -->
    <UCard>
      <template #header>
        <div class="flex items-center space-x-2">
          <UIcon name="i-heroicons-document-text" class="text-blue-500" />
          <h3 class="text-lg font-semibold">基本情報</h3>
        </div>
      </template>
      
      <div class="space-y-4">
        <UFormGroup label="終了テンプレート名" required>
          <UInput
            v-model="form.templateName"
            placeholder="例: 問題解決完了テンプレート"
            :error="errors.templateName"
          />
          <template #help>
            <span class="text-xs text-gray-500">
              管理項目用です。ユーザーには表示されません。管理しやすい名前で登録してください。
            </span>
          </template>
        </UFormGroup>

        <UFormGroup label="最終設問" required>
          <UTextarea
            v-model="form.finalQuestion"
            placeholder="チャットボットの最後に表示する設問を入力してください"
            :rows="3"
            :maxlength="100"
            :error="errors.finalQuestion"
          />
          <template #help>
            <div class="flex justify-between text-xs">
              <span class="text-gray-500">
                チャットボットの最後に表示する設問を、100文字以内で登録します。
              </span>
              <span :class="form.finalQuestion?.length > 100 ? 'text-red-500' : 'text-gray-500'">
                {{ form.finalQuestion?.length || 0 }}/100
              </span>
            </div>
          </template>
        </UFormGroup>
      </div>
    </UCard>

    <!-- Choices Configuration -->
    <UCard>
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-2">
            <UIcon name="i-heroicons-list-bullet" class="text-green-500" />
            <h3 class="text-lg font-semibold">選択肢設定</h3>
          </div>
          <UButton
            v-if="form.choices.length < 4"
            icon="i-heroicons-plus"
            variant="ghost"
            color="primary"
            size="sm"
            @click="addChoice"
          >
            選択肢を追加
          </UButton>
        </div>
      </template>

      <div class="space-y-6">
        <div v-for="(choice, index) in form.choices" :key="choice.id" class="border rounded-lg p-4">
          <div class="flex justify-between items-start mb-4">
            <h4 class="font-medium text-gray-900 dark:text-white">選択肢 {{ index + 1 }}</h4>
            <UButton
              v-if="form.choices.length > 1"
              icon="i-heroicons-trash"
              variant="ghost"
              color="red"
              size="sm"
              @click="removeChoice(index)"
            />
          </div>

          <div class="space-y-4">
            <UFormGroup label="選択肢テキスト" required>
              <UInput
                v-model="choice.text"
                placeholder="例: 解決しました。ありがとうございました。"
                :error="errors[`choice_${index}_text`]"
              />
            </UFormGroup>

            <UFormGroup label="応答メッセージ">
              <UTextarea
                v-model="choice.responseMessage"
                placeholder="この選択肢が選ばれた時の応答メッセージ（任意）"
                :rows="2"
              />
              <template #help>
                <span class="text-xs text-gray-500">
                  この選択肢が選ばれた時にユーザーに表示するメッセージです（任意）
                </span>
              </template>
            </UFormGroup>

            <UFormGroup label="1:1トーク開設">
              <UToggle
                v-model="choice.openOneOnOneTalk"
                :label="choice.openOneOnOneTalk ? '開設する' : '開設しない'"
              />
              <template #help>
                <span class="text-xs text-gray-500">
                  この選択肢が選ばれた時に1:1トークを開設するかどうかを設定します
                </span>
              </template>
            </UFormGroup>
          </div>
        </div>

        <div v-if="form.choices.length === 0" class="text-center py-8">
          <div class="text-gray-500 dark:text-gray-400">
            <UIcon name="i-heroicons-list-bullet" class="w-12 h-12 mx-auto mb-4" />
            <p class="text-lg font-medium">選択肢がありません</p>
            <p class="text-sm">「選択肢を追加」ボタンから選択肢を追加してください</p>
          </div>
        </div>
      </div>
    </UCard>
  </form>
</template>

<script setup lang="ts">
import type { EndTemplate, EndTemplateChoice } from '~/types'

interface Props {
  template?: EndTemplate | null
  saving?: boolean
}

interface Emits {
  save: [template: Partial<EndTemplate>]
  cancel: []
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
  saving: false
})

const emit = defineEmits<Emits>()

const isEdit = computed(() => !!props.template)

// Helper function to generate unique choice IDs
function generateChoiceId(): string {
  return 'choice_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
}

// Form data
const form = reactive({
  templateName: props.template?.templateName || '',
  finalQuestion: props.template?.finalQuestion || '',
  choices: props.template?.choices?.map(choice => ({ ...choice })) || [
    {
      id: generateChoiceId(),
      text: '',
      responseMessage: '',
      openOneOnOneTalk: false
    }
  ] as EndTemplateChoice[]
})

// Form errors
const errors = reactive({
  templateName: '',
  finalQuestion: '',
  ...Object.fromEntries(
    form.choices.map((_, index) => [`choice_${index}_text`, ''])
  )
})

// Validation
const isFormValid = computed(() => {
  return form.templateName.trim() &&
         form.finalQuestion.trim() &&
         form.finalQuestion.length <= 100 &&
         form.choices.length > 0 &&
         form.choices.every(choice => choice.text.trim())
})

// Expose canSave for parent components
defineExpose({
  canSave: isFormValid
})

// Choice management
const addChoice = () => {
  if (form.choices.length < 4) {
    form.choices.push({
      id: generateChoiceId(),
      text: '',
      responseMessage: '',
      openOneOnOneTalk: false
    })
  }
}

const removeChoice = (index: number) => {
  if (form.choices.length > 1) {
    form.choices.splice(index, 1)
  }
}

// Form validation
const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => {
    errors[key] = ''
  })

  let isValid = true

  if (!form.templateName.trim()) {
    errors.templateName = 'テンプレート名は必須です'
    isValid = false
  }

  if (!form.finalQuestion.trim()) {
    errors.finalQuestion = '最終設問は必須です'
    isValid = false
  } else if (form.finalQuestion.length > 100) {
    errors.finalQuestion = '最終設問は100文字以内で入力してください'
    isValid = false
  }

  form.choices.forEach((choice, index) => {
    if (!choice.text.trim()) {
      errors[`choice_${index}_text`] = '選択肢テキストは必須です'
      isValid = false
    }
  })

  return isValid
}

// Form submission
const handleSave = () => {
  if (!validateForm()) {
    return
  }

  const templateData: Partial<EndTemplate> = {
    templateName: form.templateName,
    finalQuestion: form.finalQuestion,
    choices: form.choices.map(choice => ({ ...choice }))
  }

  if (isEdit.value && props.template) {
    templateData.id = props.template.id
  }

  emit('save', templateData)
}

// Watch for prop changes
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    form.templateName = newTemplate.templateName
    form.finalQuestion = newTemplate.finalQuestion
    form.choices = newTemplate.choices?.map(choice => ({ ...choice })) || []
  }
}, { immediate: true })
</script>
