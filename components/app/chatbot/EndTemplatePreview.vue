<template>
  <div class="space-y-4">
    <!-- End Template Question -->
    <div class="flex items-start space-x-3">
      <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
      <div class="flex-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-700 dark:text-gray-300">
            {{ endTemplate.finalQuestion }}
          </p>
        </div>
        <div class="text-xs text-gray-500 mt-1 px-1">
          チャットボット
        </div>
      </div>
    </div>

    <!-- End Template Choices -->
    <div class="flex flex-col space-y-2 ml-11">
      <UButton
        v-for="(choice, index) in endTemplate.choices"
        :key="choice.id || `end-choice-${index}`"
        variant="outline"
        color="gray"
        class="w-full text-left justify-start p-3 h-auto min-h-[3rem] transition-all duration-200 hover:shadow-md bg-green-50 border-green-200 hover:bg-green-100"
        @click="handleEndChoiceClick(choice)"
      >
        <div class="flex flex-col items-start space-y-1 w-full">
          <!-- Choice Text -->
          <div class="text-sm font-medium text-left break-words whitespace-normal">
            {{ choice.text }}
          </div>

          <!-- Response Message Preview -->
          <div 
            v-if="choice.responseMessage"
            class="text-xs opacity-75 text-left break-words whitespace-normal"
          >
            → {{ choice.responseMessage }}
          </div>

          <!-- Action Info -->
          <div class="flex items-center space-x-2 text-xs opacity-60">
            <UIcon 
              :name="choice.openOneOnOneTalk ? 'i-heroicons-chat-bubble-left-right' : 'i-heroicons-stop'"
              class="w-3 h-3" 
            />
            <span>{{ choice.openOneOnOneTalk ? '1:1トークを開設' : '終了' }}</span>
          </div>
        </div>
      </UButton>
    </div>

    <!-- Template Info -->
    <div class="ml-11 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
      <div class="flex items-center space-x-2 mb-2">
        <UIcon name="i-heroicons-document-text" class="w-4 h-4 text-blue-600" />
        <span class="text-sm font-medium text-blue-800 dark:text-blue-200">
          終了テンプレート
        </span>
      </div>
      <div class="text-xs text-blue-700 dark:text-blue-300">
        <div>テンプレート名: {{ endTemplate.templateName }}</div>
        <div>選択肢数: {{ endTemplate.choices.length }}</div>
        <div>状態: {{ endTemplate.isActive ? '有効' : '無効' }}</div>
      </div>
    </div>

    <!-- Debug Info -->
    <div v-if="showDebugInfo" class="ml-11 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs">
      <div class="text-gray-600 dark:text-gray-400">
        <div>Template ID: {{ endTemplate.id }}</div>
        <div>Customer ID: {{ endTemplate.customerId }}</div>
        <div>Created: {{ formatDate(endTemplate.createdAt) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EndTemplate, EndTemplateChoice } from '~/types'

interface Props {
  endTemplate: EndTemplate
  showDebugInfo?: boolean
}

interface Emits {
  (event: 'choice-selected', choice: EndTemplateChoice): void
}

const props = withDefaults(defineProps<Props>(), {
  showDebugInfo: false
})

const emit = defineEmits<Emits>()

// Handle end choice click
const handleEndChoiceClick = (choice: EndTemplateChoice) => {
  emit('choice-selected', choice)
}

// Format date for display
const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString('ja-JP')
  } catch {
    return 'Invalid Date'
  }
}
</script>
