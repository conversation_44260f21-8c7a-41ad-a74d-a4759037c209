<template>
  <div class="flex flex-col w-full h-full">
    <!-- Development Banner -->
    <AppChatbotDevBanner />
    
    <AppPageHeader title="シナリオ一覧" />
    <div class="px-6 pt-6">
      <BaseTable
        title="シナリオ一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => { pagination.pageRangeDisplayed = value }"
        @update:page="(value: number) => { pagination.page = value; emit('page-change', value) }"
        :total="totalCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="$emit('create-scenario')"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="$emit('refresh-scenarios')"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedScenarios"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-text" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                シナリオがありません
              </span>
            </div>
          </template>

          <!-- Name Column -->
          <template #name-data="{ row }">
            <div class="flex flex-col">
              <span class="font-medium text-gray-900 dark:text-white">{{ row.name }}</span>
              <span v-if="row.description" class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                {{ row.description }}
              </span>
            </div>
          </template>

          <!-- Status Column -->
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="subtle"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>

          <!-- Questions Column -->
          <template #questions-data="{ row }">
            <div class="flex flex-col">
              <span class="text-sm font-medium">{{ row.questions.length }}個の設問</span>
              <span v-if="getFirstQuestion(row)" class="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                最初: {{ truncateText(getFirstQuestion(row)?.text || '', 30) }}
              </span>
            </div>
          </template>

          <!-- Updated At Column -->
          <template #updatedAt-data="{ row }">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              {{ formatDate(row.updatedAt) }}
            </div>
          </template>

          <!-- Actions Column -->
          <template #actions-data="{ row }">
            <div class="flex items-center space-x-2">
              <UButton
                icon="i-heroicons-eye"
                variant="ghost"
                color="gray"
                size="xs"
                @click="$emit('view-scenario', row)"
                :ui="{ rounded: 'rounded-full' }"
              />
              <UButton
                icon="i-heroicons-pencil-square"
                variant="ghost"
                color="blue"
                size="xs"
                @click="$emit('edit-scenario', row)"
                :ui="{ rounded: 'rounded-full' }"
              />
              <UDropdown :items="getDropdownItems(row)">
                <UButton
                  icon="i-heroicons-ellipsis-vertical"
                  variant="ghost"
                  color="gray"
                  size="xs"
                  :ui="{ rounded: 'rounded-full' }"
                />
              </UDropdown>
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <!-- Delete Confirmation Modal -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">シナリオの削除</h3>
        </template>
        
        <div class="space-y-4">
          <p>以下のシナリオを削除しますか？この操作は取り消せません。</p>
          <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded">
            <p class="font-medium">{{ scenarioToDelete?.name }}</p>
          </div>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-2">
            <UButton
              variant="ghost"
              color="gray"
              @click="cancelDelete"
            >
              キャンセル
            </UButton>
            <UButton
              color="red"
              :loading="deleteLoading"
              @click="confirmDelete"
            >
              削除する
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import type { Scenario, ScenarioQuestion } from '~/types'

interface Props {
  scenarios: Scenario[]
  loading?: boolean
  totalCount?: number
  pageSize?: number
  currentPage?: number
}

interface Emits {
  (event: 'create-scenario'): void
  (event: 'edit-scenario', scenario: Scenario): void
  (event: 'view-scenario', scenario: Scenario): void
  (event: 'delete-scenario', scenario: Scenario): void
  (event: 'toggle-scenario', scenario: Scenario): void
  (event: 'page-change', page: number): void
  (event: 'refresh-scenarios'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  totalCount: 0,
  pageSize: 10,
  currentPage: 1
})

const emit = defineEmits<Emits>()

// Delete modal state
const showDeleteModal = ref(false)
const scenarioToDelete = ref<Scenario | null>(null)
const deleteLoading = ref(false)

// Table configuration
const columns = [
  {
    key: 'name',
    label: 'シナリオ名',
    sortable: true
  },
  {
    key: 'status',
    label: 'ステータス'
  },
  {
    key: 'questions',
    label: '設問数'
  },
  {
    key: 'updatedAt',
    label: '更新日時',
    sortable: true
  },
  {
    key: 'actions',
    label: 'アクション'
  }
]

// Sorting
const sort = ref({ column: 'updatedAt', direction: 'desc' as const })

// Pagination for BaseTable
const pagination = reactive({
  page: props.currentPage,
  pageRangeDisplayed: props.pageSize
})

// Watch for prop changes
watch(() => props.currentPage, (newPage) => {
  pagination.page = newPage
})

watch(() => props.pageSize, (newPageSize) => {
  pagination.pageRangeDisplayed = newPageSize
})

// Computed properties
const paginatedScenarios = computed(() => {
  return props.scenarios
})

const pageFrom = computed(() => {
  return (props.currentPage - 1) * props.pageSize + 1
})

const pageTo = computed(() => {
  return Math.min(props.currentPage * props.pageSize, props.totalCount)
})

const getFirstQuestion = (scenario: Scenario): ScenarioQuestion | undefined => {
  return scenario.questions.find(q => q.isFirstQuestion) || scenario.questions[0]
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const getDropdownItems = (scenario: Scenario) => [
  [
    {
      label: scenario.isActive ? '無効化' : '有効化',
      icon: scenario.isActive ? 'i-heroicons-eye-slash' : 'i-heroicons-eye',
      click: () => emit('toggle-scenario', scenario)
    },
    {
      label: '削除',
      icon: 'i-heroicons-trash',
      click: () => initiateDelete(scenario),
      class: 'text-red-500 dark:text-red-400'
    }
  ]
]

const initiateDelete = (scenario: Scenario) => {
  scenarioToDelete.value = scenario
  showDeleteModal.value = true
}

const cancelDelete = () => {
  showDeleteModal.value = false
  scenarioToDelete.value = null
}

const confirmDelete = async () => {
  if (!scenarioToDelete.value) return
  
  deleteLoading.value = true
  try {
    emit('delete-scenario', scenarioToDelete.value)
    showDeleteModal.value = false
    scenarioToDelete.value = null
  } finally {
    deleteLoading.value = false
  }
}
</script>