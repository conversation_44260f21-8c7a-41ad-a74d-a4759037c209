<template>
  <div class="space-y-6">
    <!-- Development Banner -->
    <AppChatbotDevBanner />
    
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
          シナリオ詳細
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          シナリオの内容を確認します
        </p>
      </div>
      <div class="flex space-x-2">
        <UButton
          variant="ghost"
          color="gray"
          @click="$emit('back')"
        >
          戻る
        </UButton>
        <UButton
          v-if="scenario"
          icon="i-heroicons-pencil-square"
          color="primary"
          @click="$emit('edit', scenario)"
        >
          編集する
        </UButton>
      </div>
    </div>

    <!-- Scenario Info -->
    <UCard v-if="scenario">
      <template #header>
        <div class="flex justify-between items-start">
          <h2 class="text-lg font-medium">{{ scenario.name }}</h2>
          <UBadge
            :color="scenario.isActive ? 'green' : 'gray'"
            variant="subtle"
          >
            {{ scenario.isActive ? '有効' : '無効' }}
          </UBadge>
        </div>
      </template>
      
      <div class="space-y-4">
        <div v-if="scenario.description">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            説明
          </label>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ scenario.description }}
          </p>
        </div>

        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <label class="block font-medium text-gray-700 dark:text-gray-300">
              設問数
            </label>
            <p class="text-gray-600 dark:text-gray-400">
              {{ scenario.questions.length }}個
            </p>
          </div>
          <div v-if="scenario.updatedAt">
            <label class="block font-medium text-gray-700 dark:text-gray-300">
              最終更新
            </label>
            <p class="text-gray-600 dark:text-gray-400">
              {{ formatDate(scenario.updatedAt) }}
            </p>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Questions Flow -->
    <UCard v-if="scenario && scenario.questions.length > 0">
      <template #header>
        <h2 class="text-lg font-medium">設問フロー</h2>
      </template>

      <div class="space-y-6">
        <div
          v-for="(question, index) in scenario.questions"
          :key="question.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          :class="{ 'border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20': question.isFirstQuestion }"
        >
          <!-- Question Header -->
          <div class="flex items-start space-x-3 mb-4">
            <div class="flex-shrink-0">
              <span 
                class="flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium"
                :class="question.isFirstQuestion 
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                "
              >
                {{ index + 1 }}
              </span>
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-2">
                <h3 class="font-medium text-gray-900 dark:text-white">
                  設問{{ index + 1 }}
                </h3>
                <UBadge v-if="question.isFirstQuestion" color="yellow" variant="subtle">
                  最初の設問
                </UBadge>
              </div>
              <p class="text-gray-700 dark:text-gray-300">
                {{ question.text }}
              </p>
            </div>
          </div>

          <!-- Choices -->
          <div class="ml-11 space-y-3">
            <div
              v-for="(choice, choiceIndex) in question.choices"
              :key="choice.id"
              class="border border-gray-100 dark:border-gray-600 rounded p-3 bg-gray-50 dark:bg-gray-800/50"
            >
              <div class="flex items-start space-x-3">
                <span class="flex-shrink-0 w-6 h-6 bg-gray-200 dark:bg-gray-600 rounded text-xs font-medium flex items-center justify-center">
                  {{ String.fromCharCode(65 + choiceIndex) }}
                </span>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    {{ choice.text }}
                  </p>
                  
                  <div v-if="choice.responseMessage" class="mb-2">
                    <span class="text-xs text-gray-500 dark:text-gray-400">応答メッセージ:</span>
                    <p class="text-xs text-gray-600 dark:text-gray-300 mt-1">
                      {{ choice.responseMessage }}
                    </p>
                  </div>

                  <!-- Action Display -->
                  <div class="flex items-center space-x-2">
                    <UIcon 
                      :name="getActionIcon(choice.action.type)"
                      class="w-4 h-4 text-gray-400"
                    />
                    <span class="text-xs text-gray-600 dark:text-gray-300">
                      {{ getActionLabel(choice.action) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Empty State -->
    <UCard v-else-if="scenario && scenario.questions.length === 0">
      <div class="text-center py-8">
        <UIcon name="i-heroicons-question-mark-circle" class="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p class="text-lg font-medium text-gray-500 dark:text-gray-400">
          設問がありません
        </p>
        <p class="mt-2 text-sm text-gray-400">
          編集して設問を追加してください
        </p>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { Scenario, ScenarioAction } from '~/types'
import { SCENARIO_ACTION_LABELS } from '~/utils/scenarioConstants'

interface Props {
  scenario: Scenario | null
}

interface Emits {
  (event: 'back'): void
  (event: 'edit', scenario: Scenario): void
}

defineProps<Props>()
defineEmits<Emits>()

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('ja-JP', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getActionIcon = (actionType: string): string => {
  switch (actionType) {
    case 'show_end_survey':
      return 'i-heroicons-clipboard-document-list'
    case 'select_end_template':
      return 'i-heroicons-document-check'
    case 'open_talk':
      return 'i-heroicons-chat-bubble-left-right'
    case 'next_question':
      return 'i-heroicons-arrow-right'
    case 'end_scenario':
      return 'i-heroicons-stop'
    default:
      return 'i-heroicons-question-mark-circle'
  }
}

const getActionLabel = (action: ScenarioAction): string => {
  const baseLabel = SCENARIO_ACTION_LABELS[action.type] || action.type
  
  if (action.type === 'next_question' && action.nextQuestionId) {
    return `${baseLabel} (ID: ${action.nextQuestionId})`
  }
  
  if (action.type === 'select_end_template' && action.endTemplateId) {
    return `${baseLabel} (ID: ${action.endTemplateId})`
  }
  
  return baseLabel
}
</script>