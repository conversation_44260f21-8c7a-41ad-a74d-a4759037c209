<template>
  <div class="space-y-4">
    <!-- Question Message -->
    <div class="flex items-start space-x-3">
      <UIcon name="i-fluent-emoji-robot" class="text-2xl mt-1" />
      <div class="flex-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg rounded-bl-none p-3 shadow-sm border border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-700 dark:text-gray-300">
            {{ question.text }}
          </p>
        </div>
        <div class="text-xs text-gray-500 mt-1 px-1">
          チャットボット
        </div>
      </div>
    </div>

    <!-- Choice Buttons -->
    <div class="flex flex-col space-y-2 ml-11">
      <ScenarioChoiceButton
        v-for="(choice, index) in question.choices"
        :key="choice.id || `choice-${index}`"
        :choice="choice"
        :end-templates="endTemplates"
        @click="$emit('choice-selected', choice)"
      />
    </div>

    <!-- Question Info (for debugging/development) -->
    <div v-if="showDebugInfo" class="ml-11 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs">
      <div class="text-gray-600 dark:text-gray-400">
        <div>Question ID: {{ question.id }}</div>
        <div>First Question: {{ question.isFirstQuestion ? 'Yes' : 'No' }}</div>
        <div>Order: {{ question.order }}</div>
        <div>Choices: {{ question.choices.length }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ScenarioQuestion, ScenarioChoice, EndTemplate } from '~/types'

interface Props {
  question: ScenarioQuestion
  endTemplates?: EndTemplate[]
  showDebugInfo?: boolean
}

interface Emits {
  (event: 'choice-selected', choice: ScenarioChoice): void
}

const props = withDefaults(defineProps<Props>(), {
  endTemplates: () => [],
  showDebugInfo: false
})

defineEmits<Emits>()
</script>
