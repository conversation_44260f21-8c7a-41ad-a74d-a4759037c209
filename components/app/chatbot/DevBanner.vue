<template>
  <div v-if="showDevBanner" class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
    <div class="flex items-center">
      <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 mr-2" />
      <div>
        <p class="font-medium">開発モード - Mock データを使用中</p>
        <p class="text-sm">実際のAPIは未実装のため、サンプルデータを表示しています。</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDevConfig } from '~/composables/useDevConfig'

const { config, isDevelopment } = useDevConfig()

// Show banner when using mock data in development
const showDevBanner = computed(() => 
  isDevelopment && (config.USE_MOCK_SCENARIOS || config.USE_MOCK_END_TEMPLATES)
)
</script>