<template>
  <URadioGroup
    :legend="label || '<ラベル>'"
    :options="props.options"
    :disabled="disabled"
    :model-value="modelValue"
    @update:model-value="$emit('update:model-value', elementId, $event)"
  />
</template>

<script lang="ts" setup>
  const props = defineProps({
    elementId: String,
    label: {
      type: String,
      default: "<ラベル>",
    },
    options: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: String,
      default: "",
    },
  });
  const selected = ref();
</script>
