<template>
  <div class="flex flex-col space-y-1">
    <div class="text-sm">{{ label || "<ラベル>" }}</div>
    <USelect
      :options="options"
      :disabled="disabled"
      :model-value="modelValue"
      @update:model-value="$emit('update:model-value', elementId, $event)"
    />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    elementId: String,
    label: {
      type: String,
      default: "<ラベル>",
    },
    options: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: String,
      default: "",
    },
  });
  const selected = ref();
</script>
