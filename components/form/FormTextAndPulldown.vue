<template>
  <div class="flex flex-col space-y-1">
    <div class="text-sm">{{ label || "<ラベル>" }}</div>
    <USelectMenu
      :disabled="disabled"
      :model-value="modelValue"
      @update:model-value="$emit('update:model-value', elementId, $event?.label || $event)"
      :options="options"
      creatable
      searchable
      searchablePlaceholder="検索・テキスト追加"
    >
      <template #option="{ option }">
        <span class="truncate">{{ option }}</span>
      </template>

      <template #option-create="{ option }">
        <span class="flex-shrink-0">追加:</span>
        <span class="block truncate">{{ option.label }}</span>
      </template>
    </USelectMenu>
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    elementId: String,
    label: {
      type: String,
      default: "<ラベル>",
    },
    options: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: String,
      default: "",
    },
  });
  const selected = ref();
</script>
