<template>
  <div class="flex flex-col space-y-1">
    <div class="text-sm">{{ label || "<ラベル>" }}</div>
    <UInput
      :disabled="disabled"
      :model-value="modelValue"
      @update:model-value="$emit('update:model-value', elementId, $event)"
    />
  </div>
</template>

<script lang="ts" setup>
  defineProps({
    elementId: String,
    label: {
      type: String,
      default: "<ラベル>",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: String,
      default: "",
    },
  });
</script>
