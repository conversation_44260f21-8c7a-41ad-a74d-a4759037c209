<template>
  <div class="flex flex-col space-y-1">
    <div class="text-sm">{{ label || "<ラベル>" }}</div>
    <UCheckbox
      v-for="option in options"
      :name="label"
      :label="option"
      :value="option"
      :disabled="disabled"
      :model-value="Array.isArray(modelValue) ? modelValue?.includes(option) : false"
      @update:model-value="onUpdate($event, option)"
    />
  </div>
</template>

<script lang="ts" setup>
  const props = defineProps({
    elementId: String,
    label: {
      type: String,
      default: "<ラベル>",
    },
    options: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
  });

  const emits = defineEmits(["update:model-value"]);
  
  const onUpdate = (checked: boolean, option: string) => {
    const newModelValue = checked
      ? [...props.modelValue, option]
      : props.modelValue.filter((v) => v !== option);
    emits("update:model-value", props.elementId, newModelValue);
  };
</script>
