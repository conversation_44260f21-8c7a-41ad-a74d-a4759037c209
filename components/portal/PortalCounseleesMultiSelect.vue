<template>
  <USelectMenu
    :options="allCounseleesForPalette"
    v-model="selectedCounselees"
    value-attribute="counseleeId"
    option-attribute="fullname"
    placeholder="選択してください"
    :multiple="false"
    searchable
    searchable-placeholder="相談者名でフィルター..."
  >
    <template #label>
      <template v-if="selectedCounseleesObject.length">
        <div>
          <div class="flex items-center -space-x-2">
            <UAvatar
              v-for="counselor of selectedCounseleesObject"
              size="2xs"
              :src="counselor.pictureUrl"
              :alt="counselor.fullname"
              :ui="{
                wrapper: 'border',
                rounded: 'rounded-md',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
              }"
              class="hover:scale-125 transition-transform duration-200 hover:shadow-md hover:z-20"
            />
          </div>
        </div>
        <span class="truncate">{{ selectedCounseleesName }}</span>
      </template>
      <template v-else>
        <span class="text-gray-500 dark:text-gray-400 truncate">
          選択してください
        </span>
      </template>
    </template>
  </USelectMenu>
</template>

<script setup lang="ts">
  const portalStore = usePortalStore();
  const { allCounseleesForPalette } = storeToRefs(portalStore);
  const props = defineProps({
    modelValue: {
      type: Array as PropType<string[]> | PropType<string>,
      default: [],
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const selectedCounselees = computed({
    get() {
      return props.modelValue;
    },
    set(val: string) {
      // if val is already an array, remove it
      if (props.modelValue.includes(val)) {
        emits("update:modelValue", []);
      } else {
        emits("update:modelValue", val);
      }
    },
  });
  const selectedCounseleesObject = computed(() => {
    return allCounseleesForPalette.value?.filter((counselee) =>
      selectedCounselees.value.includes(counselee.counseleeId),
    );
  });

  const selectedCounseleesName = computed(() => {
    return allCounseleesForPalette.value
      ?.filter((counselee) =>
        selectedCounselees.value.includes(counselee.counseleeId),
      )
      .map((counselee) => counselee.fullname)
      .join(", ");
  });

  onMounted(() => {
    portalStore.fetchAllCounselees();
  });
</script>
