<template>
  <div
    class="bg-primary-600 rounded-t-2xl py-2.5 px-3 border-b dark:border-gray-700 font-semibold flex flex-inline items-center justify-between"
  >
    <div class="flex items-center space-x-2 flex-nowrap truncate">
      <UAvatar
        v-bind="{
          src: user?.basic?.customerImage,
          alt: user?.basic?.customerName,
        }"
        size="sm"
        loading="lazy"
        :ui="{
          rounded: 'rounded-lg',
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-xs font-semibold text-gray-700 dark:text-gray-800',
        }"
        :chip-color="getSocketStatusObject(webAppSocket?.status).color"
        chip-position="bottom-right"
        :chip-text="getSocketStatusObject(webAppSocket?.status).text"
      />
      <div class="truncate text-white text-lg">
        {{ $t(user?.basic?.customerName) }}
      </div>
      <slot name="name" />
    </div>
    <div class="flex items-center">
      <div class="text-sm font-normal truncate border-l pl-2">
        <div>
          {{ title }}
        </div>
      </div>
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useWebAppStore } from "~/stores/web-app";
import { storeToRefs } from "pinia";
const socketStore = useSocketStore();
const { webAppSocket } = storeToRefs(socketStore);
defineProps({
  user: {
    type: Object as PropType<any>,
    required: true,
  },
  title: {
    type: String as PropType<string>,
    default: "",
  },
});
</script>
