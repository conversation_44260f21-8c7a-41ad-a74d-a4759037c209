<template>
  <div>
    <UCard :ui="{ body: { padding: '!px-0 !pt-1 !pb-0' } }" class="relative">
      <UTextarea
        ref="inputBox"
        v-model="messageText"
        autoresize
        variant="none"
        :placeholder="$t('Message...')"
        rows="1"
        class="pl-1.5 pr-1 max-h-[300px] overflow-auto mb-10"
        :ui="{
          padding: {
            sm: 'pb-0',
          },
        }"
      />
      <div
        class="absolute bottom-0 w-full flex flex-inline justify-between px-1 pb-1"
      >
        <div class="flex flex-inline">
        </div>
        <div>
          <UButton
            size="md"
            variant="ghost"
            icon="i-heroicons-paper-airplane"
            color="primary"
            :label="$t('Send') + ` (${metaSymbol}+Enter)`"
            :trailing="false"
            @click="onSend"
          />
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
const { metaSymbol } = useShortcuts();
const inputBox = ref<{ textarea: HTMLTextAreaElement } | null>(null);

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
});
const emits = defineEmits(["send", "update:modelValue"]);
const onSend = () => {
  if (!messageText.value) {
    return;
  }
  emits("send", messageText.value);
  messageText.value = "";
  inputBox.value?.textarea?.focus();
};

const messageText = computed({
  get: () => props.modelValue || "",
  set: (value) => {
    emits("update:modelValue", value);
  },
});

defineShortcuts({
  meta_enter: {
    usingInput: true,
    handler: () => {
      onSend();
    },
  },
});

onMounted(() => {
  inputBox.value?.textarea?.focus();
});
</script>
