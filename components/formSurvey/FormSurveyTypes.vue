<script setup>
const props = defineProps({
  modelValue: {
    type: String,
  },
});

const emits = defineEmits(["update:modelValue"]);

const flatItems = computed(() => {
  const flatItems = [];
  items.forEach((group) => {
    group.forEach((item) => {
      if (item.types) {
        flatItems.push(...item.types.flat());
      } else {
        flatItems.push(item);
      }
    });
  });
  return flatItems;
});

const activeItem = computed({
  get() {
    return flatItems.value.find((item) => item.value === props.modelValue);
  },
  set(value) {
    emits("update:modelValue", value.value);
  },
});

const onSelect = (value) => {
  const item = flatItems.value.find((item) => item.value === value);
  activeItem.value = item;
};

const items = [
  [
    {
      value: "textGroup",
      label: "記述式テキスト",
      icon: "i-icon-park-add-text",
      click: () => {
        onSelect("text");
      },
      types: [
        [
          {
            value: "text",
            label: "短文回答",
            icon: "i-ic-baseline-short-text",
            click: () => {
              onSelect("text");
            },
          },
          {
            value: "textName",
            label: "名前",
            icon: "i-lets-icons-user-scan-fill",
            click: () => {
              onSelect("textName");
            },
          },
          {
            value: "textMail",
            label: "メールアドレス",
            icon: "i-ion-mail-outline",
            click: () => {
              onSelect("textMail");
            },
          },
          {
            value: "textAddress",
            label: "住所",
            icon: "i-mdi-address-marker-outline",
            click: () => {
              onSelect("textAddress");
            },
          },
          {
            value: "textArea",
            label: "長文回答",
            icon: "i-mdi-text-long",
            click: () => {
              onSelect("textArea");
            },
          },
        ],
      ],
    },
  ],
  [
    {
      value: "select",
      label: "選択形式",
      icon: "i-gg-select-r",
      click: () => {
        onSelect("selectSingle");
      },
      types: [
        [
          {
            value: "selectSingle",
            label: "ラジオボタン",
            icon: "i-ph-radio-button-fill",
            click: () => {
              onSelect("selectSingle");
            },
          },
          {
            value: "selectMulti",
            label: "チェックボックス",
            icon: "i-mdi-checkbox-outline",
            click: () => {
              onSelect("selectMulti");
            },
          },
          {
            value: "selectPulldown",
            label: "プルダウン",
            icon: "i-material-symbols-arrow-drop-down-circle-outline",
            click: () => {
              onSelect("selectPulldown");
            },
          },
        ],
      ],
    },
  ],
  [
    {
      value: "date",
      label: "日付",
      icon: "i-uiw-date",
      click: () => {
        onSelect("date");
      },
    },
  ],
];
</script>

<template>
  <UDropdown :items="items" :popper="{ placement: 'bottom-start' }">
    <UButton
      color="white"
      :label="activeItem?.label"
      trailing-icon="i-heroicons-chevron-down-20-solid"
      :leading-icon="activeItem?.icon"
    />
    <template #item="{ item }">
      <UDropdown
        v-if="item.types"
        :items="item.types"
        mode="hover"
        :popper="{
          offsetDistance: 60,
          offsetSkid: 60,
          placement: 'right',
          locked: true,
        }"
        class="w-full"
        :key="item.value"
      >
        <div class="flex flex-row items-center flex-1">
          <div class="flex flex-row items-center flex-1">
            <UIcon :name="item.icon" class="text-md font-base" />
            <div class="ml-2 text-sm">
              {{ item.label }}
            </div>
          </div>
          <div class="flex flex-row items-center">
            <UIcon
              name="i-heroicons-chevron-right-20-solid"
              class="text-md font-base"
            />
          </div>
        </div>
      </UDropdown>
      <div v-else class="flex flex-row items-center flex-1">
        <UIcon :name="item.icon" class="text-md font-base" />
        <div class="ml-2 text-sm">{{ item.label }}</div>
      </div>
    </template>
  </UDropdown>
</template>
