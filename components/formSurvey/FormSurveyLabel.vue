<template>
  <div class="w-full flex flex-row space-x-3">
    <div class="flex-auto">
      {{ schema.title }}
    </div>
    <div>
      <div
        v-if="schema.required"
        class="text-[9px] w-9 text-center bg-red-400 px-1.5 rounded-md text-white"
      >
        必須
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  schema: {
    title: string;
    required: boolean;
  };
}>();
</script>
