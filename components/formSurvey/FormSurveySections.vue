<script lang="ts" setup>
import draggable from "vuedraggable";
import { storeToRefs } from "pinia";
import { useSurveyFormStore } from "~/stores/app/survey-form";
const surveyFormStore = useSurveyFormStore();
const { formTemplate, activeElement } = storeToRefs(surveyFormStore);
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const drag = ref(false);
const dragOptions = computed(() => ({
  animation: 200,
  group: "description",
  disabled: false,
  ghostClass: "ghost",
  dragClass: "no-move",
}));

const emit = defineEmits(["close"]);
const formSurveyMenuRef = ref(null as any);
const onStartDrag = () => {
  drag.value = true;
};
const onEndDrag = () => {
  drag.value = false;
  nextTick(() => {
    const element = document.getElementById(activeElement.value._id);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });
      const menuElement = formSurveyMenuRef.value?.$el;
      if (menuElement) {
        menuElement.style.top = `${element.offsetTop}px`;
      }
    }
  });
};
</script>

<template>
  <UModal :model-value="props.isOpen" @close="emit('close')">
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        body: {
          padding: '!px-0 !py-0',
        },
      }"
    >
      <template #header>
        <div class="flex flex-row items-center justify-between">
          <div class="text-md font-bold">アイテムの並べ替え</div>
          <div class="flex flex-row items-center space-x-2">
            <UButton
              icon="i-iconamoon-close-bold"
              color="gray"
              square
              variant="ghost"
              :ui="{ rounded: 'rounded-full' }"
              @click="emit('close')"
            />
          </div>
        </div>
        <div class="text-xs text-gray-500">
          アイテムをドラッグして並べ替えることができます。
        </div>
      </template>

      <draggable
        class="dragArea list-group w-full relative flex-1 flex flex-col space-y-0"
        item-key="_id"
        ref="parent"
        :component-data="{
          tag: 'div',
          type: 'transition-group',
          name: !drag ? 'flip-list' : null,
        }"
        :list="formTemplate"
        v-bind="dragOptions"
        @start="onStartDrag"
        @end="onEndDrag"
        :key="formTemplate.length"
        direction="vertical"
      >
        <template #item="{ element, index }">
          <div :key="element._id">
            <div
              class="flex flex-row items-center justify-between space-x-2 px-5 py-2.5 bg-gray-50 dark:bg-gray-900 dark:border-gray-800 hover:bg-gray-100 border-b border-gray-200 cursor-pointer"
            >
              <div class="flex flex-row items-center space-x-2">
                <UIcon
                  name="i-akar-icons-drag-vertical"
                  class="text-gray-600 text-lg"
                />
                <div class="text-sm">
                  {{ element.title }}
                </div>
              </div>
              <div></div>
            </div>
          </div>
        </template>
      </draggable>
    </UCard>
  </UModal>
</template>
