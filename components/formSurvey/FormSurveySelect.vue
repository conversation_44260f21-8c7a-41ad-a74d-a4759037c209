<template>
    <FormSurveyElementCard v-bind="props">
      <div v-if="active && !render" class="flex flex-col space-y-4">
        <div class="flex flex-row items-center justify-between space-x-4">
          <UInput
            placeholder="質問のタイトル"
            v-model="schema.title"
            class="flex-1"
            size="md"
          />
  
          <FormSurveyTypes v-model="schema.type" />
        </div>
        <div>
            a
        </div>
      </div>
      <div v-else class="flex flex-col space-y-4">
        <UFormGroup :label="schema.title" :help="schema.description">
          <UInput />
          <template #label>
            <FormSurveyLabel :schema="schema" />
          </template>
        </UFormGroup>
      </div>
    </FormSurveyElementCard>
  </template>
  
  <script setup lang="ts">
  import type { SurveyFormElement } from "@/types";
  const props = defineProps<{
    schema: SurveyFormElement;
    active: boolean;
    render?: boolean;
  }>();
  
  const emits = defineEmits(["delete", "duplicate"]);
  </script>
  