<template>
  <UCard :ui="{ body: { padding: '!px-0 !py-1' }, shadow: 'shadow-lg' }" class="dark:bg-gray-800">
    <div class="flex flex-col items-center space-y-0">
      <UTooltip :popper="{ placement: 'right' }" text="質問を追加">
        <UButton
          icon="i-gala-add"
          size="xl"
          color="gray"
          square
          variant="ghost"
          @click="addNewElement"
        />
      </UTooltip>
      <UTooltip :popper="{ placement: 'right' }" text="タイトルを追加">
        <UButton
          icon="i-icon-park-outline-text"
          size="xl"
          color="gray"
          square
          variant="ghost"
          @click="surveyFormStore.addTitleAndDescription()"
        />
      </UTooltip>
      <UDivider />
      <UTooltip :popper="{ placement: 'right' }" text="アイテムの並べ替え">
        <UButton
          icon="i-solar-square-sort-vertical-broken"
          size="xl"
          color="gray"
          square
          variant="ghost"
          @click="showSectionsModal = true"
        />
      </UTooltip>
      <UTooltip :popper="{ placement: 'right' }" text="プレビュー">
        <UButton
          icon="i-solar-eye-scan-bold"
          size="xl"
          color="gray"
          square
          variant="ghost"
          @click="surveyFormStore.setActiveElementToNull()"
        />
      </UTooltip>
    </div>
    <Teleport to="body">
      <FormSurveySections
        :is-open="showSectionsModal"
        @close="showSectionsModal = false"
      />
    </Teleport>
  </UCard>
</template>

<script setup lang="ts">
import { useSurveyFormStore } from "~/stores/app/survey-form";
import { storeToRefs } from "pinia";
import { useDialogsStore } from "~/stores/dialogs";

const dialogsStore = useDialogsStore();
const surveyFormStore = useSurveyFormStore();
const { activeElement } = storeToRefs(surveyFormStore);
const addNewElement = () => {
  surveyFormStore.addElement();
};
const showSectionsModal = ref(false);
</script>
