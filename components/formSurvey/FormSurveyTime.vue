<template>
  <FormSurveyElementCard v-bind="props">
    <div v-if="active && !render" class="flex flex-col space-y-4">
      <div class="flex flex-row items-center justify-between space-x-4">
        <UFormGroup class="flex-1" label="質問のタイトル">
          <UInput
            placeholder="質問のタイトル"
            v-model="schema.title"
            class="flex-1"
            size="md"
          />
        </UFormGroup>
        <UFormGroup label="質問の種別">
          <FormSurveyTypes v-model="schema.type" />
        </UFormGroup>
      </div>
      <UFormGroup label="質問の説明">
        <UInput
          size="md"
          placeholder="質問の説明"
          v-model="schema.description"
        />
      </UFormGroup>
      <UFormGroup label="質問のヘルプ文">
        <UInput size="md" placeholder="質問のヘルプ文" v-model="schema.help" />
      </UFormGroup>
    </div>
    <div v-else>
      <UFormGroup
        :label="schema.title"
        :description="schema.description"
        :help="schema.help"
      >
        <div>
          <BaseDatePicker v-model="date" :disabled="false" :block="true" class="remove-inline-flex"/>
        </div>
        <template #label>
          <FormSurveyLabel :schema="schema" />
        </template>
      </UFormGroup>
    </div>
  </FormSurveyElementCard>
</template>

<script setup lang="ts">
import type { SurveyFormElement } from "@/types";
const props = defineProps<{
  schema: SurveyFormElement;
  active: boolean;
  render?: boolean;
}>();

const date = computed({
  get() {
    if(!props.schema.value) {
      return new Date();
    }
    return new Date(props.schema.value as string);
  },
  set(value: Date) {
    props.schema.value = value.toISOString();
  },
});

const emits = defineEmits(["delete", "duplicate"]);
</script>
