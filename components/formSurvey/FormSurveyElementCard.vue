<template>
  <UCard
    :ui="{
      base: `border-l-4 ${
        active && !render ? 'border-l-primary' : 'border-l-0'
      } ${
        borderTop ? 'border-t-4 border-t-primary' : ''
      } transition-colors duration-200`,
      body: { padding: `!px-4 !pb-5 !pt-${render ? 3 : 0}` },
    }"
    class="group/card transition-all duration-200 dark:bg-gray-800"
    :class="{
      'border border-primary': active && render,
    }"
  >
    <div
      v-if="!render"
      class="handle h-6 flex items-center justify-center py-1 hover:cursor-pointer"
    >
      <UIcon
        name="i-teenyicons-drag-horizontal-outline"
        class="hidden group-hover/card:block text-gray-400 text-xl"
      />
    </div>
    <div
      @click="surveyFormStore.setActiveElement(schema)"
      class="break-words whitespace-break-spaces"
    >
      <slot></slot>
    </div>

    <template v-if="active && !render">
      <UDivider class="pt-6 pb-3" />
      <div class="flex flex-row items-center justify-end">
        <UTooltip :popper="{ placement: 'bottom' }" text="複写">
          <UButton
            icon="i-ic-baseline-content-copy"
            color="gray"
            square
            variant="ghost"
            :ui="{ rounded: 'rounded-full' }"
            @click="onDuplicate"
          />
        </UTooltip>
        <UTooltip v-if="isAddNew" :popper="{ placement: 'bottom' }" text="削除">
          <UButton
            icon="i-ph-trash"
            color="gray"
            square
            variant="ghost"
            :ui="{ rounded: 'rounded-full' }"
            :disabled="!isAddNew"
            v-confirm="{
              title: 'アイテムの削除',
              message: `このアイテムを削除しますか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: onDelete,
            }"
          />
        </UTooltip>
        <div v-if="!hideRequired" class="border-l pl-3">
          <div class="flex flex-row items-center space-x-3 cursor-pointer">
            <div class="text-sm" @click="schema.required = !schema.required">
              必須
            </div>
            <UToggle v-model="schema.required" />
          </div>
        </div>
      </div>
    </template>
  </UCard>
</template>

<script setup lang="ts">
  import type { SurveyFormElement } from "@/types";
  import { useSurveyFormStore } from "~/stores/app/survey-form";
  const surveyFormStore = useSurveyFormStore();
  const props = defineProps<{
    schema: SurveyFormElement;
    active: boolean;
    borderTop?: boolean;
    render?: boolean;
    hideRequired?: boolean;
    isAddNew?: boolean;
  }>();
  const onDuplicate = () => {
    const newElement = surveyFormStore.duplicateElement(props.schema);
    nextTick(() => {
      surveyFormStore.setActiveElement(newElement);
    });
  };
  const onDelete = () => {
    surveyFormStore.deleteElement(props.schema);
  };
</script>
