<template>
  <FormSurveyElementCard v-bind="props">
    <div v-if="active && !render" class="flex flex-col space-y-4">
      <div class="flex flex-row items-center justify-between space-x-4">
        <UInput
          placeholder="質問のタイトル"
          v-model="schema.title"
          class="flex-1"
          size="md"
        />

        <FormSurveyTypes v-model="schema.type" />
      </div>
      <UInput size="md" placeholder="質問の説明" v-model="schema.description" />
    </div>
    <div v-else class="flex flex-col space-y-4">
      <UFormGroup
        :label="schema.title"
        :description="schema.description"
        :help="schema.help"
        :ui="{
          description: 'text-xs font-light text-gray-400 dark:text-gray-400',
        }"
      >
        <UTextarea autoresize v-model="textValue"/>

        <template #label>
          <FormSurveyLabel :schema="schema" />
        </template>
      </UFormGroup>
    </div>
  </FormSurveyElementCard>
</template>

<script setup lang="ts">
import type { SurveyFormElement } from "@/types";
const props = defineProps<{
  schema: SurveyFormElement;
  active: boolean;
  render?: boolean;
}>();

const emits = defineEmits(["delete", "duplicate"]);

const textValue = computed({
  get: () => {
    // check if the value is an array, join it with a comma
    if (Array.isArray(props.schema.value)) {
      return props.schema.value.join(", ");
    }
    return props.schema.value || "";
  },
  set: (value) => (props.schema.value = value),
});

</script>
