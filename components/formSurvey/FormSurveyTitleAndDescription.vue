<template>
  <FormSurveyElementCard v-bind="props" hideRequired>
    <div class="flex flex-col space-y-4">
      <div>
        <UInput
          v-if="active && !render"
          placeholder="タイトル"
          v-model="schema.title"
          :padded="active"
          :variant="active ? 'outline' : 'none'"
          size="xl"
          :ui="{ size: { xl: 'text-lg font-semibold' } }"
        />
        <div
          v-else
          class="md:text-lg font-semibold whitespace-break-spaces break-words"
        >
          {{ schema.title }}
        </div>
      </div>
      <UTextarea
        v-if="active && !render"
        autoresize
        placeholder="説明"
        v-model="schema.description"
        :padded="active"
        :variant="active ? 'outline' : 'none'"
      />
      <div v-else class="text-sm whitespace-break-spaces break-words">
        {{ schema.description }}
      </div>
    </div>
  </FormSurveyElementCard>
</template>

<script setup lang="ts">
import type { SurveyFormElement } from "@/types";
const props = defineProps<{
  schema: SurveyFormElement;
  active: boolean;
  render?: boolean;
  borderTop?: boolean;
}>();
</script>
