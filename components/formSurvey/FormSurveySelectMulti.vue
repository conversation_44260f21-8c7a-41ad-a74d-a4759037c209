<template>
  <FormSurveyElementCard v-bind="props">
    <div v-if="active && !render" class="flex flex-col space-y-4">
      <div class="flex flex-row items-center justify-between space-x-4">
        <UFormGroup class="flex-1" label="質問のタイトル">
          <UInput
            placeholder="質問のタイトル"
            v-model="schema.title"
            class="flex-1"
            size="md"
          />
        </UFormGroup>

        <UFormGroup label="質問の種別">
          <FormSurveyTypes v-model="schema.type" />
        </UFormGroup>
      </div>
      <UFormGroup label="質問の説明">
        <UInput
          size="md"
          placeholder="質問の説明"
          v-model="schema.description"
        />
      </UFormGroup>
      <UFormGroup label="質問のヘルプ文">
        <UInput size="md" placeholder="質問のヘルプ文" v-model="schema.help" />
      </UFormGroup>
      <div>
        <UFormGroup label="選択オプション">
          <div class="flex flex-col space-y-3 pl-2">
            <draggable
              class="dragArea list-group w-full flex flex-col"
              tag="transition-group"
              handle=".handle"
              :component-data="{
                tag: 'ul',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null,
              }"
              v-model="schema.values"
              v-bind="dragOptions"
            >
              <template #item="{ element, index }">
                <div v-if="schema.values" class="relative group">
                  <div>
                    <div
                      class="absolute group-hover:flex hidden -left-5 h-full items-center"
                    >
                      <UIcon
                        name="i-nimbus-drag-dots"
                        class="handle text-xl hover:cursor-pointer text-gray-500"
                      />
                    </div>
                    <div class="flex items-center space-x-2 py-2">
                      <UTooltip
                        :text="
                          schema.defaultValue?.includes(element)
                            ? 'デフォルト値'
                            : ''
                        "
                        :prevent="!schema.defaultValue?.includes(element)"
                      >
                        <UIcon
                          :name="
                            schema.defaultValue?.includes(element)
                              ? 'i-carbon-checkbox-checked'
                              : 'i-carbon-checkbox'
                          "
                          class="text-xl hover:cursor-pointer text-gray-500"
                          @click="onSetDefaultValue(element)"
                        />
                      </UTooltip>

                      <UInput
                        ref="optionRef"
                        v-model="schema.values[index]"
                        class="flex-1"
                        @change="onCheckOption(index)"
                        @keydown.enter="onAddOption(null)"
                      >
                      </UInput>
                      <UButton
                        v-if="schema.values.length > 1"
                        icon="i-heroicons-x-mark"
                        size="xs"
                        color="red"
                        variant="soft"
                        @click="schema.values?.splice(index, 1)"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </draggable>
            <div
              v-if="schema.hasOtherOption"
              class="flex items-center space-x-2"
            >
              <UIcon
                :name="
                  schema.defaultValue?.includes(OtherOptionKey)
                    ? 'i-carbon-checkbox-checked'
                    : 'i-carbon-checkbox'
                "
                class="text-xl hover:cursor-pointer text-gray-500"
                @click="onSetDefaultValue(OtherOptionKey)"
              />

              <UInput
                model-value="その他..."
                class="flex-1"
                disabled
                color="gray"
              >
              </UInput>
              <UButton
                icon="i-heroicons-x-mark"
                size="xs"
                color="red"
                variant="soft"
                @click="schema.hasOtherOption = false"
              />
            </div>
            <div class="flex items-center space-x-0">
              <UTooltip
                :text="
                  schema.defaultValue?.includes(OtherOptionKey)
                    ? 'デフォルト値'
                    : ''
                "
                :prevent="!schema.defaultValue?.includes(OtherOptionKey)"
              >
                <UIcon
                  name="i-carbon-checkbox"
                  class="text-xl hover:cursor-pointer text-gray-500"
                />
              </UTooltip>
              <UButton
                size="xs"
                color="gray"
                variant="link"
                label="選択肢を追加"
                @click="onAddOption(null)"
              />
              <template v-if="!schema.hasOtherOption">
                <div class="text-xs">または</div>
                <UButton
                  size="xs"
                  color="primary"
                  variant="link"
                  label="「その他」を追加"
                  @click="onAddOtherOption"
                />
              </template>
            </div>
          </div>
        </UFormGroup>
      </div>
    </div>
    <div v-else class="flex flex-col space-y-4">
      <UFormGroup
        :label="schema.title"
        :description="schema.description"
        :help="schema.help"
        :ui="{
          description: 'text-xs font-light text-gray-400 dark:text-gray-400',
        }"
        :name="schema._id"
      >
        <div class="flex flex-col space-y-1">
          <UCheckbox
            v-for="option in schema.values"
            :value="option"
            v-model="schema.value"
            :label="option"
          />
          <div class="flex flex-row items-center space-x-2">
            <UCheckbox
              v-if="schema.hasOtherOption"
              :value="OtherOptionKey"
              v-model="schema.value"
              label="その他"
              class="items-center flex"
            >
            </UCheckbox>
            <UInput
              ref="otherOptionRef"
              class="flex-1 border-b focus-within:border-b-2 focus-within:border-primary"
              variant="none"
              :padded="false"
              :disabled="!schema.value?.includes(OtherOptionKey)"
              v-if="schema.hasOtherOption"
              v-model="schema.otherOption"
            />
          </div>
        </div>
        <template #label>
          <FormSurveyLabel :schema="schema" />
        </template>
      </UFormGroup>
    </div>
  </FormSurveyElementCard>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import type { SurveyFormElement } from "@/types";

const { OtherOptionKey } = useFormSurveyElements();
const props = defineProps<{
  schema: SurveyFormElement;
  active: boolean;
  render?: boolean;
}>();

const drag = ref(false);
const dragOptions = computed(() => ({
  animation: 200,
  group: "formSurveySelectSingle",
  disabled: false,
  ghostClass: "ghost",
}));

const otherOptionRef = ref();
const optionRef = ref();
const onAddOption = (value: string | null) => {
  props.schema.values?.push(
    generateUniqueOption(value || "選択肢", props.schema.values || [])
  );

  nextTick(() => {
    optionRef.value?.input.focus();
    optionRef.value?.input.select();
  });
};

const onSetDefaultValue = (value: string) => {
  console.log(
    "🚀 ~ file: FormSurveySelectMulti.vue:234 ~ onSetDefaultValue ~ value:",
    value
  );
  // check if value is already in the default value array
  if ((props.schema.defaultValue as string[])?.includes(value)) {
    // remove value from default value array
    (props.schema.defaultValue as string[])?.splice(
      (props.schema.defaultValue as string[])?.indexOf(value),
      1
    );
  } else {
    // add value to default value array
    (props.schema.defaultValue as string[])?.push(value);
  }
};

const onAddOtherOption = () => {
  props.schema.hasOtherOption = true;
};
watch(
  () => props.schema.value,
  (value, oldValue) => {
    if (
      value?.includes(OtherOptionKey) &&
      !oldValue?.includes(OtherOptionKey)
    ) {
      nextTick(() => {
        otherOptionRef.value?.input.focus();
      });
    }
  }
);
const onCheckOption = (index: number) => {
  if (props.schema.values && props.schema.values[index].trim() === "") {
    props.schema.values.splice(
      index,
      1,
      generateUniqueOption("選択肢", props.schema.values || [])
    );
  } else {
    props.schema.values?.splice(
      index,
      1,
      generateUniqueOption(
        props.schema.values[index].trim(),
        props.schema.values.filter((obj, idx) => idx !== index) || []
      )
    );
  }
};

onMounted(() => {
  if (!props.schema.values) {
    props.schema.values = [generateUniqueOption("選択肢", [])];
  }

  if (
    !props.schema.defaultValue ||
    typeof props.schema.defaultValue !== "object"
  ) {
    props.schema.defaultValue = [];
  }

  props.schema.value = props.schema.defaultValue || props.schema.values[0];
});
</script>
