<template>
  <div
    class="relative p-2 flex flex-col space-y-4"
    :class="{
      'bg-gray-100 dark:bg-gray-800 w-full h-full': isOpen,
    }"
  >
    <div v-if="isOpen" class="pt-0 w-full">
      <div class="w-full">
        <UCard
          v-if="submiting"
          :ui="{
            ring: '',
            divide: 'divide-y divide-gray-100 dark:divide-gray-800',
          }"
        >
          <div class="flex flex-col items-center justify-center space-y-4 py-4">
            <div class="flex bg-gray-400/10 p-5 rounded-full w-fit">
              <Icon
                icon="line-md:uploading-loop"
                class="text-5xl text-gray-500"
              />
            </div>

            <div class="text-center">
              <div>アンケート回答を送信中です。</div>
              <div>しばらくお待ちください。</div>
            </div>
          </div>
        </UCard>
        <UCard
          v-else-if="submited"
          :ui="{
            ring: '',
            divide: 'divide-y divide-gray-100 dark:divide-gray-800',
          }"
        >
          <div class="flex flex-col items-center justify-center space-y-4 py-4">
            <div class="flex bg-green-400/30 p-5 rounded-full w-fit">
              <UIcon
                name="i-bi-send-check-fill"
                class="text-green-600 text-5xl"
              />
            </div>

            <div class="text-center">
              <div>アンケート回答を送信しました。</div>
              <div>ご協力いただき、誠にありがとうございます。</div>
            </div>
          </div>
        </UCard>
        <UCard
          v-else
          :ui="{
            ring: '',
            divide: 'divide-y divide-gray-100 dark:divide-gray-800',
          }"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <h3
                class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
              >
                送信内容
              </h3>
              <UButton
                color="gray"
                variant="ghost"
                icon="i-heroicons-x-mark-20-solid"
                class="-my-1"
                @click="isOpen = false"
              />
            </div>
          </template>

          <FormSurveyResultRender :formTemplate="props.formTemplate" />
          <UAlert
            v-if="submitError"
            icon="i-heroicons-exclamation-triangle"
            color="red"
            variant="subtle"
            title="回答処理に失敗しました"
            :description="submitError"
            class="mt-4"
          />
          <template #footer>
            <div class="grid grid-cols-2 gap-4 mb-10">
              <UButton
                color="gray"
                variant="solid"
                icon="i-mdi-close-circle-outline"
                class="px-8"
                label="閉じる"
                @click="isOpen = false"
                block
              />
              <UButton
                color="primary"
                variant="solid"
                icon="i-mdi-send-circle"
                class="px-8"
                label="送信"
                @click="onSubmit"
                block
              />
            </div>
          </template>
        </UCard>
      </div>
    </div>
    <div v-show="!isOpen">
      <!-- <UCard
        v-if="user"
        :ui="{
          base: `border-l-4 ${'border-l-0'} transition-colors duration-200`,
          body: { padding: `!px-4 !py-5` },
        }"
        class="group/card transition-all duration-200 mb-5"
      >
        <div class="flex flex-row space-x-4 items-center">
          <UAvatar size="xl" :src="user.pictureUrl" :alt="user.displayName" />
          <div class="">
            <strong>{{ user.displayName }}</strong
            >様、こんにちは<br />
            アンケートにご協力をお願いします。
          </div>
        </div>
      </UCard> -->

      <UForm
        :state="state"
        :schema="validateSchema"
        class="space-y-4"
        @submit="onPreview"
      >
        <template v-for="(element, index) in formTemplate" :key="element._id">
          <component
            :id="`element-${element._id}`"
            :is="FormElements[element.type]"
            :schema="element"
            :active="activeElement?._id === element._id"
            :borderTop="index === 0"
            render
          />
        </template>
        <div>
          <UAlert
            v-if="!isValid"
            icon="i-heroicons-exclamation-triangle"
            color="red"
            variant="subtle"
            title="入力内容に不備があります"
            description="入力内容を確認してください"
            class="mb-4"
          />

          <UButton block type="submit" @click="onValidate">確定</UButton>
        </div>
      </UForm>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  import type { SurveyFormElement } from "@/types";

  const props = defineProps<{
    formTemplate: SurveyFormElement[];
    activeElement: SurveyFormElement;
    user: any;
    submiting: boolean;
    submited: boolean;
    submitError: string;
  }>();
  const { FormElements: FormElements, OtherOptionKey } =
    useFormSurveyElements();

  const emits = defineEmits(["submit"]);
  const state = computed(() => {
    const stateForm = {} as Record<string, unknown>;
    props.formTemplate
      .filter((obj) => obj.type !== "titleAndDescription")
      .forEach((element) => {
        stateForm[element._id] = element.value;
      });
    return stateForm;
  });

  const validateSchema = computed(() => {
    return createYupSchema(props.formTemplate);
  });

  const isOpen = ref(false);
  const onPreview = () => {
    console.log("submit", state);
    isOpen.value = true;
  };

  const isValid = ref(true);
  const onValidate = () => {
    isValid.value = validateSchema.value.isValidSync(state.value);
  };
  const onSubmit = async () => {
    await emits("submit", props.formTemplate, state.value);
  };
</script>
