<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              パスワード変更
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="onClose"
            />
          </div>
        </template>
        <div class="space-y-6">
          <UFormGroup label="現在のパスワード" required name="password">
            <UInput
              v-model="state.password"
              icon="i-mdi-password-outline"
              size="lg"
              :type="showPassword.password ? 'text' : 'password'"
              autocomplete="off"
              :ui="{ icon: { trailing: { pointer: '' } } }"
            >
              <template #trailing>
                <UButton
                  :padded="false"
                  @click="showPassword.password = !showPassword.password"
                  color="gray"
                  variant="link"
                  :icon="
                    showPassword.password
                      ? 'i-heroicons-eye-slash-solid'
                      : 'i-heroicons-eye-solid'
                  "
                />
              </template>
            </UInput>
          </UFormGroup>
          <UFormGroup label="新しいパスワード" required name="newPassword">
            <UInput
              v-model="state.newPassword"
              icon="i-mdi-password"
              size="lg"
              :type="showPassword.newPassword ? 'text' : 'password'"
              autocomplete="off"
              :ui="{ icon: { trailing: { pointer: '' } } }"
            >
              <template #trailing>
                <UButton
                  :padded="false"
                  @click="showPassword.newPassword = !showPassword.newPassword"
                  color="gray"
                  variant="link"
                  :icon="
                    showPassword.newPassword
                      ? 'i-heroicons-eye-slash-solid'
                      : 'i-heroicons-eye-solid'
                  "
                />
              </template>
            </UInput>
          </UFormGroup>
          <UFormGroup
            label="新しいパスワード (確認)"
            required
            name="newPasswordConfirm"
          >
            <UInput
              v-model="state.newPasswordConfirm"
              icon="i-mdi-password-check"
              size="lg"
              :type="showPassword.newPasswordConfirm ? 'text' : 'password'"
              autocomplete="off"
              :ui="{ icon: { trailing: { pointer: '' } } }"
            >
              <template #trailing>
                <UButton
                  :padded="false"
                  @click="
                    showPassword.newPasswordConfirm =
                      !showPassword.newPasswordConfirm
                  "
                  color="gray"
                  variant="link"
                  :icon="
                    showPassword.newPasswordConfirm
                      ? 'i-heroicons-eye-slash-solid'
                      : 'i-heroicons-eye-solid'
                  "
                />
              </template>
            </UInput>
          </UFormGroup>
        </div>
        <UAlert
          v-if="errors['changePassword']"
          class="mt-6"
          icon="i-heroicons-exclamation-triangle-solid"
          color="red"
          variant="subtle"
          title="エラーが発生しました"
          :description="errors['changePassword']"
        />

        <template #footer>
          <div class="flex justify-end">
            <UButton
              label="変更"
              type="submit"
              class="px-10"
              :loading="loadings['changePassword']"
            />
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useAuthStore } from "~/stores/auth";

  const authStore = useAuthStore();
  const { loadings, errors } = storeToRefs(authStore);
  const { object, string, ref } = useYup();
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
  });
  const schema = object({
    password: string().min(8).required(),
    newPassword: string()
      .min(8)
      .required()
      .notOneOf([ref("password"), ""], t("New password must be different")),
    newPasswordConfirm: string()
      .required()
      .min(8)
      .oneOf([ref("newPassword"), ""], t("Passwords must match")),
  });
  const emit = defineEmits(["close"]);
  const state = reactive({
    password: "",
    newPassword: "",
    newPasswordConfirm: "",
  });

  const resetForm = () => {
    state.password = "";
    state.newPassword = "";
    state.newPasswordConfirm = "";
    errors.value["changePassword"] = "";
    showPassword.newPassword = false;
    showPassword.newPasswordConfirm = false;
    showPassword.password = false;
  };
  async function onSubmit() {
    const toast = useToast();
    const result = await authStore.changePassword(
      state.password,
      state.newPassword,
    );
    if (result) {
      toast.add({
        title: t("Password changed"),
        description: t("Your password has been changed successfully"),
        icon: "i-heroicons-check-circle",
      });
      // reset form
      onClose();
    }
  }

  const showPassword = reactive({
    password: false,
    newPassword: false,
    newPasswordConfirm: false,
  });

  const onClose = () => {
    resetForm();
    emit("close");
  };
</script>
