<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        v-if="user"
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              プロファイル変更
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div class="flex justify-stretch">
          <div class="flex flex-col space-y-4 w-2/3 pr-4">
            <div class="flex items-end space-x-3">
              <UFormGroup label="名前" class="w-full">
                <UInput
                  icon="i-carbon-label"
                  size="md"
                  :ui="{ icon: { trailing: { pointer: '' } } }"
                  v-model="state.fullName"
                  :disabled="!isAdmin"
                  :color="isAdmin ? undefined : 'gray'"
                >
                </UInput>
              </UFormGroup>
            </div>
            <div class="flex items-end space-x-3">
              <UFormGroup label="組織名" class="w-full">
                <UInput
                  icon="i-octicon-organization-24"
                  size="md"
                  :ui="{ icon: { trailing: { pointer: '' } } }"
                  v-model="user.organizationName"
                  disabled
                  color="gray"
                >
                </UInput>
              </UFormGroup>
            </div>
            <div class="flex items-end space-x-3">
              <UFormGroup label="メールアドレス" class="w-full">
                <UInput
                  icon="i-heroicons-envelope"
                  size="md"
                  :ui="{ icon: { trailing: { pointer: '' } } }"
                  v-model="user.email"
                  disabled
                  color="gray"
                >
                </UInput>
              </UFormGroup>
            </div>
          </div>
          <div
            class="relative border-l pl-3 dark:border-gray-700 w-1/3 flex flex-col items-center justify-center space-y-3 mt-2"
          >
            <UAvatar
              :ui="{
                rounded: 'rounded-xl',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-5xl uppercase text-gray-700 dark:text-gray-800',
                wrapper: 'border',
                size: {
                  '3xl': 'h-32 w-32',
                },
              }"
              :src="imageFile"
              :alt="state?.fullName"
              size="3xl"
              chip-position="top-right"
            >
              <UButton
                v-if="imageFile"
                class="absolute -top-3 -right-3 z-10"
                variant="soft"
                icon="i-heroicons-x-mark-solid"
                size="xs"
                color="red"
                square
                :ui="{ rounded: 'rounded-full' }"
                @click="onRemoveAvatar"
              >
              </UButton>
            </UAvatar>
            <div class="flex flex-col space-y-1">
              <input
                ref="fileInputEl"
                type="file"
                class="hidden"
                accept="image/*"
                @change="handleFileUpload"
              />
              <UButton
                variant="outline"
                icon="i-mdi-image-plus"
                size="sm"
                @click="fileInputEl?.click()"
              >
                アバター変更
              </UButton>
            </div>
          </div>
        </div>
        <template #footer>
          <div class="flex justify-end">
            <UButton
              label="変更"
              type="submit"
              class="px-10"
              :loading="loadings['updateProfile']"
            />
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import { useAuthStore } from "~/stores/auth";
  import type { User } from "~/types";
  import { CounselorRole } from "~/types/enums.d";

  const authStore = useAuthStore();
  const { loadings, user } = storeToRefs(authStore);
  const { object, string } = useYup();
  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
  });
  const schema = object({
    fullName: string().required(),
  });
  const emit = defineEmits(["close"]);
  const state = reactive({
    fullName: user.value?.fullName || "",
    profileImage: user.value?.profileImage || "",
  });
  const isAdmin = computed(() => user.value?.role === CounselorRole.ADMIN);

  const fileInputEl = ref<HTMLInputElement | null>(null);
  const imageFile = ref<string | null>(user.value?.profileImage || null);
  const handleFileUpload = (e: Event) => {
    const files = (e.target as HTMLInputElement).files as FileList;
    if (files?.length > 0) {
      const reader = new FileReader();
      reader.onload = (e) => {
        imageFile.value = e.target?.result as string;
        state.profileImage = imageFile.value;
      };
      reader.readAsDataURL(files[0]);
    }
  };
  const onRemoveAvatar = () => {
    imageFile.value = null;
    state.profileImage = "";
  };
  async function onSubmit() {
    const toast = useToast();
    const result = await authStore.updateProfile({
      ...user.value,
      fullName: state.fullName,
      profileImage: imageFile.value ?? state.profileImage,
    } as User);
    if (result) {
      toast.add({
        title: t("Update success"),
        description: t("Your profile has been updated successfully"),
        icon: "i-heroicons-check-circle",
      });
      // reset form
      emit("close");
    }
  }
</script>
