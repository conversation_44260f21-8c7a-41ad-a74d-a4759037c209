<template>
  <aside
    class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full bg-white border-r border-gray-200 md:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="overflow-y-auto py-5 h-full bg-white dark:bg-gray-800">

      <UVerticalNavigation class="mt-0" :links="menus" />
    </div>
    <div
      class="hidden absolute bottom-0 left-0 justify-left px-2 py-3 space-x-4 w-full lg:flex bg-white dark:bg-gray-800 z-20"
    >
      <AppAuthUser block class="w-full" />
    </div>
  </aside>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCustomersStore } from "~/stores/admin/customers";
  import { useCounselorsStore } from "~/stores/admin/counselors";
  const { customerAccountsCount } = storeToRefs(useCustomersStore());
  const { counselorCount } = storeToRefs(useCounselorsStore());

  const menus = computed(() => {
    return [
      {
        label: "カスタマーアカウント",
        icon: "i-mdi-company",
        badge: customerAccountsCount.value,
        to: "/admin",
        exact: true,
      },
      {
        label: "カウンセラーアカウント",
        icon: "i-heroicons-user-group",
        badge: counselorCount.value,
        to: "/admin/counserlor-account",
      },
      // {
      //   label: "権限一覧",
      //   icon: "i-eos-icons-role-binding",
      //   to: "/admin/permission",
      // },
    ];
  });
</script>
