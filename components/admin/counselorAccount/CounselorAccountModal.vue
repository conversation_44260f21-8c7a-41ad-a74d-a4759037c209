<template>
  <UModal :model-value="props.show" prevent-close>
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
              {{
                isViewOnly
                  ? t("Account detailt")
                  : isAddNew
                  ? t("Add new counselor")
                  : t("Edit counselor")
              }}
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <UTabs :items="counselorTabs" class="w-full">
          <template #default="{ item }">
            <div class="flex items-center gap-2 relative truncate">
              <UIcon :name="item.icon" class="w-4 h-4 flex-shrink-0" />

              <div class="flex items-center space-x-1">
                <span class="truncate">{{ item.organizationName ?? "" }}</span>
              </div>
            </div>
          </template>
          <template #item="{ item }">
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <div>
                    <p
                      class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      {{ item.label }}
                    </p>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      {{ item.description }}
                    </p>
                  </div>
                  <UFormGroup v-if="item.key === 'user-information'" name="profileImage">
                    <BaseImageInput
                      v-model="state.profileImage"
                      hide-text
                      inner-class="!h-14 !w-14"
                      :alt="state.fullName"
                      :disabled="isViewOnly"
                    />
                  </UFormGroup>
                  <div v-else-if="item.key === 'assigned-cases'">
                    <UButton
                      v-if="state.role !== CounselorAccountRole.ADMIN"
                      icon="i-heroicons-plus"
                      size="xl"
                      color="primary"
                      square
                      variant="soft"
                      :ui="{ rounded: 'rounded-full' }"
                      @click="assignNewCustomerRoles"
                    />
                  </div>
                </div>
              </template>
              <div v-if="item.key === 'user-information'" class="space-y-3">
                <UFormGroup :label="t('Organization')" name="organizationName" required>
                  <UInput v-model="state.organizationName" :variant="inputVariant" />
                </UFormGroup>
                <UFormGroup :label="t('Name')" name="fullName" required>
                  <UInput v-model="state.fullName" :variant="inputVariant" />
                </UFormGroup>
                <UFormGroup
                  :label="t('Mail address')"
                  name="email"
                  required
                  hint="❖この値は作成後変更できません"
                  :ui="{
                    hint: 'text-xs text-orange-400',
                  }"
                >
                  <UInput
                    v-model="state.email"
                    :variant="inputVariant"
                    :disabled="!isAddNew"
                    :color="isAddNew ? 'white' : 'gray'"
                  />
                </UFormGroup>
                <UFormGroup
                  :label="t('Counserlor ID')"
                  name="counselorId"
                  required
                  hint="❖この値は作成後変更できません"
                  :ui="{
                    hint: 'text-xs text-orange-400',
                  }"
                >
                  <UInput
                    v-model="state.counselorId"
                    :variant="inputVariant"
                    :disabled="!isAddNew"
                    :color="isAddNew ? 'white' : 'gray'"
                  />
                </UFormGroup>
                <UFormGroup :label="t('Role class')" name="role" required>
                  <USelectMenu
                    v-model="roleModel"
                    :options="roleOptions"
                    :variant="inputVariant"
                  >
                    <template #label>
                      <span
                        :class="[
                          `bg-${getCounselorRoleColor(state.role)}-500`,
                          'inline-block h-3 w-3 flex-shrink-0 rounded-full',
                        ]"
                        aria-hidden="true"
                      />
                      <span class="truncate">{{ t(state.role) }}</span>
                    </template>
                    <template #option="{ option: role }">
                      <span
                        :class="[
                          `bg-${role.color}-500`,
                          'inline-block h-3 w-3 flex-shrink-0 rounded-full',
                        ]"
                        aria-hidden="true"
                      />
                      <span class="truncate">{{ role.label }}</span>
                    </template>
                  </USelectMenu>
                </UFormGroup>
                <UDivider class="pt-4" />
                <div
                  v-if="!isAddNew && counselor.needReissueTempPassword"
                  class="flex flex-col space-y-2 pt-1"
                >
                  <UButton
                    :key="counselor.counselorId"
                    v-confirm="{
                      title: '一時パスワード再発行の確認',
                      message: `「${counselor.fullName}」のカウンセラーの一時パスワードを再発行してもよろしいですか？`,
                      confirmButtonText: 'はい、再発行する',
                      cancelButtonText: 'いいえ',
                      onConfirm: () => onReissueTempPassword(counselor),
                    }"
                    icon="i-heroicons-receipt-refund"
                    size="sm"
                    color="yellow"
                    square
                    variant="ghost"
                    label="一時パスワード再発行"
                    block
                  />
                  <!-- <UButton
                    :key="counselor.counselorId"
                    v-confirm="{
                      title: 'カウンセラーの削除',
                      message: `「${counselor.fullName}」のカウンセラーを削除してもよろしいですか？`,
                      confirmButtonText: 'はい、削除する',
                      cancelButtonText: 'いいえ',
                      onConfirm: () => onDeleteCounselor(counselor),
                    }"
                    icon="i-heroicons-trash"
                    size="sm"
                    color="red"
                    square
                    variant="ghost"
                    :label="t('Delete account')"
                    block
                  /> -->
                </div>
              </div>
              <div v-if="item.key === 'assigned-cases'" class="space-y-3">
                <div
                  v-if="state.role !== CounselorAccountRole.ADMIN"
                  class="flex flex-col space-y-4 min-h-[300px]"
                >
                  <div
                    v-if="!state.customerRoles?.length"
                    class="flex items-center justify-center h-[300px]"
                  >
                    <div class="text-center space-y-3">
                      <div class="font-thin">
                        {{ t("No assigned cases") }}
                      </div>
                      <UButton
                        v-if="state.role !== CounselorAccountRole.ADMIN"
                        icon="i-heroicons-plus"
                        size="sm"
                        color="primary"
                        square
                        variant="soft"
                        :label="t('Add new assignment')"
                        :ui="{ rounded: 'rounded-full' }"
                        class="px-6"
                        @click="assignNewCustomerRoles"
                      />
                    </div>
                  </div>
                  <div
                    v-else
                    :key="state.customerRoles?.length"
                    class="flex flex-col space-y-4"
                  >
                    <div
                      v-for="(_, index) in state.customerRoles"
                      :key="index"
                      class="space-y-4"
                    >
                      <div class="flex flex-inline items-end space-x-2">
                        <UFormGroup
                          :label="t('Assigned cases')"
                          :name="`role.${index}.customerName`"
                          class="flex-1 w-20"
                        >
                          <USelectMenu
                            v-model="state.customerRoles[index]"
                            :options="customerOptions"
                            option-attribute="name"
                            searchable
                            :searchable-placeholder="t('Search by name...')"
                            class="w-full"
                            :placeholder="t('Select a issue')"
                          />
                        </UFormGroup>
                        <UFormGroup :label="t('Role')" :name="`role.${index}.role`">
                          <USelectMenu
                            v-model="state.customerRoles[index].role"
                            :options="counselorDetailRoleOptions"
                            class="w-full min-w-[150px]"
                            :placeholder="t('Select a role')"
                          >
                            <template #label>
                              {{ t(state.customerRoles[index].role) }}
                            </template>
                            <template #option="{ option }">
                              {{ t(option) }}
                            </template>
                          </USelectMenu>
                        </UFormGroup>
                        <UButton
                          class="z-50"
                          icon="i-heroicons-x-mark"
                          size="sm"
                          color="red"
                          square
                          variant="soft"
                          @click="state.customerRoles.splice(index, 1)"
                        />
                      </div>
                      <UDivider />
                    </div>
                  </div>
                </div>
                <div
                  v-else
                  class="flex flex-col space-y-4 min-h-[300px] items-center justify-center text-orange-400"
                >
                  <UIcon name="i-grommet-icons-user-admin" class="text-5xl" />
                  <div class="text-orange-400">全てのカスタマーにアクセスできます</div>
                </div>
              </div>
            </UCard>
          </template>
        </UTabs>
        <UAlert
          v-if="error"
          icon="i-heroicons-exclamation-triangle"
          :description="error"
          title="エラーが発生しました"
          color="red"
          variant="subtle"
          class="mt-4"
        />
        <template #footer>
          <div class="text-right">
            <UButton type="submit" class="px-10">
              {{ isAddNew ? t("Add new") : t("Update") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
import { cloneDeep } from "lodash";
import { storeToRefs } from "pinia";
import { useCustomersStore } from "~/stores/admin/customers";
import type { Counselor, CustomerRole } from "~/types";
import { CounselorAccountRole, CounselorDetailRole } from "~/types/enums.d";
const customersStore = useCustomersStore();
const { customers } = storeToRefs(customersStore);
const { object, string, array } = useYup();
const { t } = useI18n();
const props = defineProps({
  show: Boolean,
  isAddNew: Boolean,
  isViewOnly: Boolean,
  counselor: {
    type: Object as PropType<Counselor>,
    default: () => ({}),
  },
  error: {
    type: String,
    default: "",
  },
});

const roleOptions = Object.values(CounselorAccountRole).map((role) => ({
  label: t(role),
  value: role,
  color: getCounselorRoleColor(role),
}));

const counselorDetailRoleOptions = Object.values(CounselorDetailRole);

const inputVariant = computed(() => {
  return props.isViewOnly ? "none" : "outline";
});

const customerOptions = computed(() => {
  return (
    customers.value
      // .filter((obj: any) => {
      //   return (
      //     !state.customerRoles.some((role) => role.id === obj.customerId) &&
      //     !obj.deletedAt
      //   );
      // })
      .filter((obj: any) => !obj.deletedAt)
      .map((customer: any) => ({
        name: customer.basic.customerName,
        id: customer.customerId,
        role: CounselorDetailRole.SUPERVISOR,
      }))
  );
});
const roleModel = computed({
  get: () => roleOptions.find((role) => role.value === state.role),
  set: (roleOption: any) => {
    state.role = roleOption.value;
  },
});
const schema = object<Counselor>().shape({
  counselorId: string().min(6).max(20).required(),
  organizationName: string().required(),
  fullName: string().required(),
  email: string().email().required(),
  profileImage: string().optional(),
  role: string().required().oneOf(Object.values(CounselorAccountRole)),
  customerRoles: array()
    .of(
      object().shape({
        id: string().required(),
        name: string().required(),
        role: string().required().oneOf(Object.values(CounselorDetailRole)),
      })
    )
    .optional(),
});

const initialState: Counselor = {
  counselorId: "",
  organizationName: "",
  fullName: "",
  email: "",
  role: CounselorAccountRole.ADMIN,
  customerRoles: [] as CustomerRole[],
  profileImage: "",
};
let state = reactive<Counselor>(cloneDeep(initialState));

watch(
  () => props.counselor,
  (counselor) => {
    if (counselor) {
      counselor.customerRoles = customers.value.reduce(
        (tempCustomerRoles: any, customer: any) => {
          const role = customer.roles?.find(
            (role: any) => role.counselorId === counselor.counselorId
          );
          if (role) {
            tempCustomerRoles.push({
              id: customer.customerId || "",
              name: customer.basic.customerName,
              role: role.counselorRole,
            });
          }

          return tempCustomerRoles;
        },
        [] as CustomerRole[]
      );
      state = reactive<Counselor>(cloneDeep(counselor));
    } else {
      state = reactive<Counselor>(cloneDeep(initialState));
    }
  },
  { immediate: true }
);

const emit = defineEmits(["close", "update", "addNew", "delete", "reissue-temporary-password"]);

function onSubmit() {
  if (props.isAddNew) {
    emit("addNew", cloneDeep(state));
  } else {
    emit("update", cloneDeep(state));
  }
}

const counselorTabs = [
  {
    key: "user-information",
    icon: "i-heroicons-identification",
    label: t("Counselor information"),
    description: t("Basic information of counselor account"),
  },
  {
    key: "assigned-cases",
    icon: "i-heroicons-squares-plus",
    label: t("Assigned cases"),
    description: t("Assigned cases of this counselor account"),
  },
];

const assignNewCustomerRoles = () => {
  state.customerRoles.unshift({
    id: "",
    name: "選択してください",
    role: CounselorDetailRole.SUPERVISOR,
  });
};

const onReissueTempPassword = (counselor: Counselor) => {
  emit("reissue-temporary-password", counselor);
};
</script>
