<template>
  <UModal
    :model-value="props.show"
    prevent-close
    :ui="{
      width: 'sm:max-w-4xl',
    }"
  >
    <UForm
      class="space-y-4"
      :schema="schema"
      :state="customerRoles"
      @submit="onSubmit"
    >
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
          body: {
            padding: '!p-0',
          },
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3
              class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
            >
              「{{ customer.basic?.customerName }}」のカウンセラーの紐付け
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div>
          <UTable :rows="customerRoles" :columns="columns">
            <template #name-data="{ row }">
              <div class="flex flex-inline space-x-2 items-center">
                <UAvatar
                  :ui="{
                    rounded: 'rounded-lg',
                    background: 'bg-gray-200 dark:bg-gray-400',
                    placeholder:
                      'text-md font-semibold text-gray-700 dark:text-gray-800',
                  }"
                  :src="row.profileImage"
                  :alt="row.fullName"
                  size="md"
                />
                <div>
                  <div class="flex flex-row space-x-2 items-center">
                    <div class="font-semibold">
                      {{ row.fullName }}
                    </div>
                    <UBadge
                      :label="row.role"
                      :color="getCounselorRoleColor(row.role)"
                      size="xs"
                      :ui="{
                        size: {
                          xs: 'text-[9px] px-1 py-0',
                        },
                      }"
                    />
                  </div>
                  <div class="text-xs">
                    {{ row.email }}
                  </div>
                </div>
              </div>
            </template>
            <template #role-data="{ row }">
              <USelect
                v-model="row.role"
                :options="counselorDetailRoleOptions"
                class="w-fit"
                placeholder="権限を選択"
                :disabled="row.isAdmin"
                :color="!row.role ? 'red' : row.isAdmin ? 'gray' : 'white'"
              >
              </USelect>
            </template>
            <template #actions-data="{ row }">
              <div class="flex items-center w-full justify-center">
                <UButton
                  v-if="!row.isAdmin"
                  :key="row.counselorId"
                  v-confirm="{
                    title: 'アカウントの削除',
                    message: `「${row.fullName}」アカウントを「${customer.basic?.customerName}」から削除してもよろしいですか？ `,
                    confirmButtonText: 'はい、削除する',
                    cancelButtonText: 'いいえ',
                    onConfirm: () => onRemoveCounserlor(row),
                  }"
                  icon="i-heroicons-trash-20-solid"
                  size="xs"
                  color="red"
                  variant="ghost"
                  :label="$t('Delete')"
                  :trailing="false"
                />
                <UBadge
                  v-else
                  :label="t(row.accountRole)"
                  :color="getCounselorRoleColor(row.accountRole)"
                />
              </div>
            </template>
          </UTable>
        </div>

        <template #footer>
          <div class="flex flex-row justify-between">
            <UButton
              type="button"
              color="gray"
              icon="i-heroicons-user-plus"
              class="px-10"
              variant="soft"
              @click="isOpenCounserlorsPalette = true"
            >
              カウンセラー追加
            </UButton>
            <UButton
              type="submit"
              class="px-10"
              :disabled="isHasAnyRolesHavenotSelect"
            >
              更新
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
  <AdminCounserlorsPalette
    :is-open="isOpenCounserlorsPalette"
    @close="isOpenCounserlorsPalette = false"
    @select="onSelectCounserlor"
  />
</template>

<script lang="ts" setup>
  import { cloneDeep } from "lodash";
  import type {
    Counselor,
    CounselorRoles,
    Customer,
    TableColumn,
  } from "~/types";
  import { CounselorAccountRole, CounselorDetailRole } from "~/types/enums.d";
  import { useCounselorsStore } from "~/stores/admin/counselors";
  const counselorsStore = useCounselorsStore();
  const { counselors } = storeToRefs(counselorsStore);

  const { t } = useI18n();
  const props = defineProps({
    show: Boolean,
    customer: {
      type: Object as PropType<Customer>,
      default: () => ({}),
    },
  });

  const columns: TableColumn[] = [
    {
      key: "organizationName",
      label: t("Organization"),
      sortable: true,
      class: "w-0 text-center",
    },
    {
      key: "fullName",
      label: t("Name"),
      sortable: true,
    },
    {
      key: "counselorId",
      label: t("Counserlor ID"),
      sortable: true,
    },
    {
      key: "role",
      label: t("Role"),
      sortable: true,
    },
    {
      key: "actions",
      class: "w-0",
    },
  ];

  interface RowRole {
    organizationName: string;
    fullName: string;
    counselorId: string;
    role: CounselorDetailRole;
    email: string;
    profileImage: string;
  }

  const customerRoles = ref<RowRole[]>([]);
  const isHasAnyRolesHavenotSelect = computed(() => {
    return customerRoles.value.some((customerRole) => {
      return !customerRole.role;
    });
  });
  const counselorDetailRoleOptions = Object.values(CounselorDetailRole).map(
    (role) => ({
      label: t(role),
      value: role,
    }),
  );

  // TODO: schemaを設定するとPOSTできない

  // const schema = object().shape({
  //   organizationName: string().optional(),
  //   fullName: string().optional(),
  //   counselorId: string().optional(),
  //   role: string().optional(),
  //   email: string().optional(),
  //   profileImage: string().optional(),
  // });

  watch(
    () => props.customer,
    (customer) => {
      if (customer) {
        const cloneCustomerRoles = cloneDeep(customer.roles);
        const fixCustomerRoles = cloneCustomerRoles?.map((customerRole) => {
          const findCounselor = counselors.value.find(
            (counselor) => counselor.counselorId === customerRole.counselorId,
          );

          return {
            counselorId: findCounselor?.counselorId,
            fullName: findCounselor?.fullName,
            organizationName: findCounselor?.organizationName,
            role: customerRole.counselorRole,
            email: findCounselor?.email,
            profileImage: findCounselor?.profileImage,
            isAdmin: findCounselor?.role === CounselorAccountRole.ADMIN,
            accountRole: findCounselor?.role,
          };
        }) as RowRole[];

        customerRoles.value = fixCustomerRoles;
      }
    },
    { immediate: true },
  );

  const emit = defineEmits(["close", "update"]);

  function onSubmit() {
    if(isHasAnyRolesHavenotSelect.value) {
      return;
    }
    const requestCustomerRoles = customerRoles.value.map((customerRole) => ({
      counselorId: customerRole.counselorId,
      counselorRole: customerRole.role,
    }));
    emit("update", requestCustomerRoles);
    emit("close");
  }

  const onRemoveCounserlor = (counselor: Counselor) => {
    const index = customerRoles.value?.findIndex(
      (c) => c.counselorId === counselor.counselorId,
    );
    if (index !== undefined && index > -1) {
      const tempCustomerRoles = cloneDeep(customerRoles.value || []);
      tempCustomerRoles.splice(index, 1);
      customerRoles.value = tempCustomerRoles;
    }
  };

  const isOpenCounserlorsPalette = ref(false);
  const onSelectCounserlor = (counselorRole: CounselorRoles) => {
    const findCounselor = counselors.value.find(
      (counselor) => counselor.counselorId === counselorRole.counselorId,
    );

    if (findCounselor) {
      const tempCustomerRoles = cloneDeep(customerRoles.value || []);
      tempCustomerRoles.push({
        counselorId: findCounselor?.counselorId ?? "",
        fullName: findCounselor?.fullName ?? "",
        organizationName: findCounselor?.organizationName ?? "",
        role: counselorRole?.counselorRole,
        email: findCounselor?.email ?? "",
        profileImage: findCounselor?.profileImage ?? "",
      });
      customerRoles.value = tempCustomerRoles;
    }
    isOpenCounserlorsPalette.value = false;
  };
</script>
