<template>
  <UModal
    :model-value="props.show"
    prevent-close
    :ui="{
      width: 'sm:max-w-4xl',
    }"
  >
    <UForm class="space-y-4" :schema="schema" :state="state" @submit="onSubmit">
      <UCard
        :ui="{
          ring: '',
          divide: 'divide-y divide-gray-100 dark:divide-gray-800',
        }"
      >
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
              {{ isAddNew ? t("Add new customer") : t("Edit customer") }}
            </h3>
            <UButton
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              class="-my-1"
              @click="emit('close')"
            />
          </div>
        </template>
        <div class="grid grid-cols-12 gap-6">
          <div
            class="space-y-4 col-span-6 bg-gray-50 dark:bg-gray-800 p-4 border rounded-lg dark:border-gray-600"
          >
            <UFormGroup :label="t('Customer name')" name="basic.customerName" required>
              <UInput v-model="state.basic.customerName" />
            </UFormGroup>
            <UFormGroup
              label="スラッグ"
              name="slug"
              required
              description="URLの末尾の部分を任意の文字列に指定できる機能"
            >
              <UInput v-model="state.slug" placeholder="this-is-slug" />
            </UFormGroup>
            <UFormGroup :label="t('Customer avatar')" name="basic.customerImage">
              <BaseImageInput v-model="state.basic.customerImage" />
            </UFormGroup>
            <UFormGroup :label="t('IP configs')" name="ipWhiteLists">
              <UCard>
                <div class="flex flex-col space-y-4">
                  <div v-if="state.ipWhiteLists.length" class="flex flex-col space-y-4">
                    <div
                      v-for="(ipConfig, index) in state.ipWhiteLists"
                      :key="index"
                      class="flex space-x-2 items-center"
                    >
                      <UInput
                        v-model="state.ipWhiteLists[index]"
                        class="flex-1"
                        placeholder="***********"
                      />
                      <UButton
                        class="z-50"
                        icon="i-heroicons-trash"
                        size="sm"
                        color="red"
                        square
                        variant="soft"
                        @click="state.ipWhiteLists.splice(index, 1)"
                      />
                    </div>
                  </div>
                  <div class="text-center">
                    <UButton
                      icon="i-heroicons-plus"
                      size="sm"
                      color="primary"
                      square
                      variant="soft"
                      @click="addNewIPConfig"
                    />
                  </div>
                </div>
              </UCard>
            </UFormGroup>
            <UFormGroup :label="t('Number of counselees')" name="counseleeLimit" required>
              <UInput v-model="state.counseleeLimit" type="number" />
            </UFormGroup>
            <UFormGroup :label="t('Contracted of lines')" name="contractedLines" required>
              <UInput v-model="state.contractedLines" type="number" />
            </UFormGroup>
            <UFormGroup label="相談受付開始日">
              <BaseDatePicker v-model="state.startDate" block color="white" :disabled="false" />
            </UFormGroup>
            <UFormGroup label="相談受付終了日">
              <BaseDatePicker v-model="state.endDate" block color="white" :disabled="false" />
            </UFormGroup>
          </div>
          <div
            class="space-y-4 col-span-6 pl-6 pr-4 border-l dark:border-gray-600 border-dashed"
          >
            <UFormGroup name="snsChanels" required>
              <UTabs
                :default-index="defaultSnsChannelIndex"
                :items="snsConfigsTabs"
                class="w-full"
              >
                <template #default="{ item }">
                  <div class="flex items-center gap-2 relative truncate">
                    <component :is="getSNSIconComponent(item.key)" class="h-5 w-5" />

                    <div class="flex items-center space-x-1">
                      <span class="truncate">{{ item.label }}</span>
                      <UIcon
                        v-if="state[item.key as SNSChannel].isActive"
                        name="i-heroicons-check-circle-solid"
                        class="text-primary-600"
                      />
                    </div>
                  </div>
                </template>
                <template #item="{ item }">
                  <UCard>
                    <template #header>
                      <div class="flex items-center justify-between">
                        <div>
                          <p
                            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
                          >
                            {{ item.label }}
                          </p>
                          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {{ item.description }}
                          </p>
                        </div>
                        <UToggle
                          v-model="state[item.key as SNSChannel].isActive"
                          :name="`snsChanels.${item.key}`"
                          on-icon="i-heroicons-check-20-solid"
                          off-icon="i-heroicons-x-mark-20-solid"
                        />
                      </div>
                    </template>
                    <div v-if="item.key === SNSChannel.LINE" class="space-y-3">
                      <div class="space-y-3">
                        <UFormGroup
                          label="相談プラットフォーム"
                          name="snsConfigs.setting.chatType"
                        >
                          <div class="flex flex-row space-x-1 w-full h-full">
                            <div v-for="(item, index) in chatTypes" class="w-full h-full">
                              <SettingsChatType
                                v-model="chatTypes[index]"
                                size="lg"
                                :active="
                                  state.line.chatType === item.value &&
                                  state[SNSChannel.LINE].isActive
                                "
                                @select="onSelectChatType(item)"
                              />
                            </div>
                          </div>
                        </UFormGroup>

                        <UFormGroup
                          v-if="state.line.chatType !== 'line'"
                          :label="urlLabel"
                        >
                          <UInput
                            :value="richmenuUrl"
                            :color="'gray'"
                            readonly
                            :ui="{ icon: { trailing: { pointer: '' } } }"
                          >
                            <template #trailing>
                              <UButton
                                color="gray"
                                variant="link"
                                :icon="coppied ? 'i-lets-icons-check-fill' : 'i-solar-copy-linear'"
                                :padded="false"
                                @click="onCopyURL(richmenuUrl)"
                                :class="coppied ? 'text-green-500' : 'text-gray-500'"
                              />
                            </template>
                          </UInput>
                        </UFormGroup>
                      </div>
                      <UDivider />

                      <div class="space-y-3">
                        <div class="font-semibold text-left mb-2">
                          Messaging APIチャネル
                        </div>
                        <UFormGroup
                          :label="t('Channel ID')"
                          name="snsConfigs.line.channelId"
                        >
                          <UInput
                            v-model="state.line.messaging.channelId"
                            :disabled="!state[SNSChannel.LINE].isActive"
                            :color="state[SNSChannel.LINE].isActive ? 'white' : 'gray'"
                          />
                        </UFormGroup>
                        <UFormGroup
                          :label="t('Channel secret')"
                          name="snsConfigs.line.channelSecret"
                        >
                          <BasePasswordInput
                            v-model="state.line.messaging.secret"
                            :disabled="!state[SNSChannel.LINE].isActive"
                            :color="state[SNSChannel.LINE].isActive ? 'white' : 'gray'"
                          />
                        </UFormGroup>

                        <UFormGroup
                          :label="t('Channel access token')"
                          name="snsConfigs.line.channelAccessToken"
                        >
                          <BasePasswordInput
                            v-model="state.line.messaging.accessToken"
                            :disabled="!state[SNSChannel.LINE].isActive"
                            :color="state[SNSChannel.LINE].isActive ? 'white' : 'gray'"
                          />
                        </UFormGroup>
                      </div>
                    </div>
                    <div v-else-if="item.key === SNSChannel.FACEBOOK" class="space-y-3">
                      <UFormGroup :label="t('Page ID')" name="snsConfigs.facebook.pageId">
                        <UInput
                          v-model="state.facebook.pageId"
                          :disabled="!state[SNSChannel.FACEBOOK].isActive"
                          :color="state[SNSChannel.FACEBOOK].isActive ? 'white' : 'gray'"
                        />
                      </UFormGroup>
                      <UFormGroup
                        :label="t('Page access token')"
                        name="snsConfigs.facebook.pageAccessToken"
                      >
                        <UInput
                          v-model="state.facebook.pageAccessToken"
                          type="password"
                          :disabled="!state[SNSChannel.FACEBOOK].isActive"
                          :color="state[SNSChannel.FACEBOOK].isActive ? 'white' : 'gray'"
                        />
                      </UFormGroup>
                    </div>
                    <div
                      v-else-if="item.key === SNSChannel.APPLICATION"
                      class="space-y-3"
                    >
                      <UFormGroup
                        :label="t('App access code')"
                        name="snsConfigs.application.appAccessCode"
                      >
                        <UInput
                          v-model="state.application.accessCode"
                          :disabled="!state.application.isActive || true"
                          :color="state.application.isActive ? 'gray' : 'gray'"
                        />
                      </UFormGroup>
                      <UFormGroup
                        :label="t('Browser access code')"
                        name="snsConfigs.application.browserAccessCode"
                      >
                        <BaseCodeInput
                          v-model="state.application.browserAccessCode"
                          :disabled="!state.application.isActive"
                        />
                      </UFormGroup>
                      <div class="flex items-center justify-between py-2">
                        <div>
                          <p class="text-sm leading-6 text-gray-900 dark:text-gray-100">
                            ブラウザアクセスコードの表示
                          </p>
                        </div>
                        <UToggle
                          v-model="state.application.showBrowserAccessCode"
                          name="state.application.showBrowserAccessCode"
                          on-icon="i-heroicons-check-20-solid"
                          off-icon="i-heroicons-x-mark-20-solid"
                        />
                      </div>

                      <UFormGroup v-if="state.application.isActive" label="相談URL">
                        <UInput
                          :value="counseleeURL"
                          :color="state.application.isActive ? 'white' : 'gray'"
                          readonly
                          :ui="{ icon: { trailing: { pointer: '' } } }"
                        >
                          <template #trailing>
                            <UButton
                              color="gray"
                              variant="link"
                              icon="i-majesticons-open-line"
                              :padded="false"
                              @click="onOpenURL(counseleeURL)"
                            />
                          </template>
                        </UInput>
                      </UFormGroup>
                    </div>
                  </UCard>
                </template>
              </UTabs>
            </UFormGroup>
            <div>
              <UFormGroup label="利用可能な機能" name="featureList">
                <template #description>
                  <span class="text-sm text-gray-500">
                    顧客が利用できる機能を選択してください。複数選択可能です。
                  </span>
                </template>
                <div class="grid grid-cols-3 gap-3 w-full">
                  <div v-for="(item, index) in featureList" :key="item.value" class="w-full">
                    <SettingsChatType
                      v-model="featureList[index]"
                      size="lg"
                      :active="state.featureList?.includes(item.value)"
                      @select="onSelectFeature(item)"
                    />
                  </div>
                </div>
                <div v-if="state.featureList && state.featureList.length > 0" class="mt-4">
                  <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    選択済み機能 ({{ state.featureList.length }}個):
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <UBadge
                      v-for="feature in state.featureList"
                      :key="feature"
                      color="primary"
                      variant="soft"
                      size="sm"
                    >
                      {{ getFeatureTitle(feature) }}
                    </UBadge>
                  </div>
                </div>
              </UFormGroup>
            </div>

            <UAlert
              v-if="error"
              icon="i-heroicons-exclamation-triangle"
              :description="error"
              title="エラーが発生しました"
              color="red"
              variant="subtle"
            />
          </div>
        </div>
        <template #footer>
          <div class="text-right">
            <UButton type="submit" class="px-10">
              {{ t("Submit") }}
            </UButton>
          </div>
        </template>
      </UCard>
    </UForm>
  </UModal>
</template>

<script lang="ts" setup>
import { cloneDeep } from "lodash";
import type { Customer, RequestCustomer, ChatbotConfig } from "~/types";
import { SNSChannel, CustomerFeature } from "~/types/enums.d";
const { object, string, number, boolean } = useYup();
const { t } = useI18n();
const props = defineProps({
  show: Boolean,
  isAddNew: Boolean,
  customer: {
    type: Object as PropType<Customer>,
    default: () => ({}),
  },
  error: {
    type: String,
    default: "",
  },
});

const defaultSnsChannelIndex = computed(() => {
  if (!props.customer) {
    return 0;
  }
  if (props.customer.line.isActive) {
    return 0;
  }

  if (props.customer.application.isActive) {
    return 1;
  }

  // if (props.customer.facebook.isActive) {
  //   return 2;
  // }

  return 0;
});

const { chatTypes, featureList } = useConstants();
const onSelectChatType = (chatType: any) => {
  if (!state[SNSChannel.LINE].isActive) return;
  state.line.chatType = chatType.value;
};
const onSelectFeature = (feature: any) => {
  // check if feature is in the list
  if (state.featureList?.includes(feature.value as CustomerFeature)) {
    state.featureList = state.featureList?.filter((item) => item !== feature.value);
  } else {
    if (!state.featureList) state.featureList = [];
    state.featureList?.push(feature.value as CustomerFeature);
  }
};

const getFeatureTitle = (feature: CustomerFeature) => {
  const item = featureList.value.find(f => f.value === feature);
  return item?.title || feature;
};
const snsConfigsSchema = object()
  .when("facebook", {
    is: true,
    then: () =>
      object().shape({
        facebook: object()
          .shape({
            isActive: boolean().required(),
            pageId: string().required(),
            pageAccessToken: string().required(),
          })
          .required(),
      }),
  })
  .when("line", {
    is: true,
    then: () =>
      object().shape({
        line: object()
          .shape({
            isActive: boolean().required(),
            chatType: string().required(),
            messaging: object().shape({
              channelId: string().required(),
              secret: string().required(),
              accessToken: string().required(),
            }),
          })
          .required(),
      }),
  })
  .when("application", {
    is: true,
    then: () =>
      object().shape({
        application: object()
          .shape({
            isActive: boolean().required(),
            appAccessCode: string().required(),
            browserAccessCode: string().required(),
          })
          .required(),
      }),
  });

const schema = object().shape({
  slug: string().required(),
  basic: object().shape({
    customerName: string().required(),
  }),
  counseleeLimit: number().min(1).required(),
  snsConfigs: snsConfigsSchema,
});

const initialState: RequestCustomer = {
  slug: "",
  basic: {
    customerName: "",
    customerImage: undefined,
  },
  line: {
    chatType: "line",
    isActive: false,
    messaging: {
      channelId: "",
      secret: "",
      accessToken: "",
    },
  },
  application: {
    isActive: false,
    accessCode: "",
    browserAccessCode: "",
    showBrowserAccessCode: true,
  },
  facebook: {
    isActive: false,
    pageId: "",
    pageAccessToken: "",
  },
  counseleeLimit: 10000,
  ipWhiteLists: [],
  startDate: new Date(),
  endDate: new Date(),
  contractedLines: 10,
  featureList: [],
  chatbot: {
    name: "",
    welcomeMessage: "",
    autoResponse: false,
    responseDelay: 30,
  },
};
let state = reactive<RequestCustomer>(cloneDeep(initialState));

watch(
  () => props.customer,
  (customer) => {
    if (customer) {
      Object.assign(state, customer);
    } else {
      state = reactive<RequestCustomer>(cloneDeep(initialState));
    }
  },
  { immediate: true }
);

const emit = defineEmits(["close", "update", "addNew"]);

function onSubmit() {
  if (props.isAddNew) {
    emit("addNew", cloneDeep(state));
  } else {
    emit("update", cloneDeep(state));
  }
  // emit("close");
}

const snsConfigsTabs = [
  {
    key: SNSChannel.LINE as SNSChannel,
    label: t(SNSChannel.LINE),
    description: t("Toggle to enable Line channel"),
  },
  {
    key: SNSChannel.APPLICATION as SNSChannel,
    label: t(SNSChannel.APPLICATION),
    description: t("Toggle to enable Application channel"),
  },
  {
    key: SNSChannel.FACEBOOK as SNSChannel,
    label: t(SNSChannel.FACEBOOK),
    description: t("Toggle to enable Facebook channel"),
    disabled: true,
  },
];

const addNewIPConfig = () => {
  state.ipWhiteLists.push("");
};

const counseleeURL = computed(() => {
  return state.application.isActive
    ? `${window.location.origin}/web-app/${state.slug}/code-input`
    : "";
});

const onOpenURL = (url: string) => {
  window.open(url, "_blank");
};

const urlLabel = computed(() => {
  switch (state.line.chatType) {
    case "line":
      return "URLなし";
    case "browser":
      return "外部ブラウザへのURL";
    case "liff":
      return "LIFFブラウザへのURL";
    default:
      return "";
  }
});

const richmenuUrl = computed(() => {
  const runtimeConfig = useRuntimeConfig();
  const loginChannelLiffId = runtimeConfig.public.NUXT_LINE_CHAT_LIFF_ID;
  return state.line.chatType === "browser"
    ? `${window.location.origin}/liff-app/chat/?openExternalBrowser=1&c=${props.customer.customerId}`
    : `https://liff.line.me/${loginChannelLiffId}/?c=${props.customer.customerId}`;
});

const coppied = ref(false);
const onCopyURL = (url: string) => {
  navigator.clipboard.writeText(url);
  coppied.value = true;
  setTimeout(() => {
    coppied.value = false;
  }, 1000);
};
</script>
