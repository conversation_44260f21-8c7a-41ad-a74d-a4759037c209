<template>
  <UCard>
    <template #header>
      <div class="flex items-center space-x-2">
        <UIcon name="i-heroicons-chat-bubble-left-right" class="text-blue-500" />
        <h3 class="text-lg font-semibold">チャットボット設定</h3>
      </div>
    </template>
    <div class="space-y-6">
      <!-- Basic Settings -->
      <div class="space-y-4">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">基本設定</h4>
        <UFormGroup label="チャットボット名" name="chatbot.name" required>
          <UInput 
            v-model="modelValue.name" 
            placeholder="例: サポートボット"
            @input="updateValue"
          />
        </UFormGroup>
        <UFormGroup label="ウェルカムメッセージ" name="chatbot.welcomeMessage" required>
          <UTextarea 
            v-model="modelValue.welcomeMessage"
            placeholder="こんにちは！何かお困りのことはありませんか？"
            :rows="3"
            @input="updateValue"
          />
        </UFormGroup>
      </div>

      <UDivider />

      <!-- Auto Response Settings -->
      <div class="space-y-4">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">自動応答設定</h4>
        <UFormGroup label="自動応答の有効化" name="chatbot.autoResponse">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              一定時間後に自動でメッセージを送信します
            </span>
            <UToggle
              v-model="modelValue.autoResponse"
              on-icon="i-heroicons-check-20-solid"
              off-icon="i-heroicons-x-mark-20-solid"
              @change="updateValue"
            />
          </div>
        </UFormGroup>
        <UFormGroup 
          v-if="modelValue.autoResponse"
          label="応答待機時間（秒）" 
          name="chatbot.responseDelay"
          required
        >
          <UInput 
            v-model="modelValue.responseDelay" 
            type="number"
            min="1"
            max="300"
            placeholder="30"
            @input="updateValue"
          />
          <template #help>
            <span class="text-xs text-gray-500">
              1秒から300秒（5分）まで設定可能です
            </span>
          </template>
        </UFormGroup>
      </div>

      <UDivider />

      <!-- Advanced Settings -->
      <div class="space-y-4">
        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">高度な設定</h4>
        <UFormGroup label="営業時間外メッセージ" name="chatbot.offHoursMessage">
          <UTextarea 
            v-model="modelValue.offHoursMessage"
            placeholder="申し訳ございませんが、現在営業時間外です。営業時間内にお問い合わせください。"
            :rows="2"
            @input="updateValue"
          />
        </UFormGroup>
        <UFormGroup label="キーワード自動応答" name="chatbot.keywordResponses">
          <div class="space-y-3">
            <div v-if="modelValue.keywordResponses && modelValue.keywordResponses.length > 0">
              <div 
                v-for="(response, index) in modelValue.keywordResponses" 
                :key="index"
                class="flex space-x-2 items-start"
              >
                <UInput
                  v-model="response.keyword"
                  placeholder="キーワード"
                  class="flex-1"
                  @input="updateValue"
                />
                <UInput
                  v-model="response.response"
                  placeholder="応答メッセージ"
                  class="flex-1"
                  @input="updateValue"
                />
                <UButton
                  icon="i-heroicons-trash"
                  size="sm"
                  color="red"
                  square
                  variant="soft"
                  @click="removeKeywordResponse(index)"
                />
              </div>
            </div>
            <UButton
              icon="i-heroicons-plus"
              size="sm"
              color="primary"
              variant="soft"
              @click="addKeywordResponse"
            >
              キーワード応答を追加
            </UButton>
          </div>
        </UFormGroup>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { ChatbotConfig } from '~/types'

const props = defineProps<{
  modelValue: ChatbotConfig
}>()

const emit = defineEmits<{
  'update:modelValue': [value: ChatbotConfig]
}>()

const updateValue = () => {
  emit('update:modelValue', props.modelValue)
}

const addKeywordResponse = () => {
  if (!props.modelValue.keywordResponses) {
    props.modelValue.keywordResponses = []
  }
  props.modelValue.keywordResponses.push({
    keyword: '',
    response: ''
  })
  updateValue()
}

const removeKeywordResponse = (index: number) => {
  if (props.modelValue.keywordResponses) {
    props.modelValue.keywordResponses.splice(index, 1)
    updateValue()
  }
}
</script>
