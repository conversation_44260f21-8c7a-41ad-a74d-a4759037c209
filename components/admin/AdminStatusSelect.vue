<script setup lang="ts">
  const statusList = [
    { label: "有効", value: "active" },
    { label: "無効", value: "inactive" },
  ];
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
  });

  const emits = defineEmits(["update:modelValue"]);
  const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
      emits("update:modelValue", value);
    },
  });

  const label = computed(() => {
    if (selected.value.length) {
      return statusList
        .filter((item) => selected.value.includes(item.value))
        .map((item) => item.label)
        .join(", ");
    }
    return "状態を選択";
  });
</script>

<template>
  <USelectMenu
    v-model="selected"
    :options="statusList"
    multiple
    placeholder="Select people"
    optionAttribute="label"
    valueAttribute="value"
  >
    <template #label>
      <span class="truncate">{{ label }}</span>
    </template>
  </USelectMenu>
</template>
