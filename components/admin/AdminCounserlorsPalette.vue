<template>
  <UModal :model-value="props.isOpen" @close="emit('close')">
    <UCommandPalette
      v-if="loadings['fetchCounselors']"
      loading
      :empty-state="{
        icon: 'i-heroicons-cloud-arrow-down-solid',
        label: t('Loading...'),
        queryLabel: t('Loading...'),
      }"
      :placeholder="t('Loading...')"
    />

    <UCommandPalette
      v-if="!loadings['fetchCounselors'] && counserlorsForPalette.length"
      ref="commandPaletteRef"
      :autoselect="false"
      :fuse="{ resultLimit: 100, fuseOptions: { threshold: 0.1 } }"
      :placeholder="t('Search by name...')"
      :ui="{
        container: 'max-h-[50vh] hidden-scrollbar',
        group: {
          command: {
            avatar: {
              size: '3xs',
            },
          },
        },
      }"
      @update:model-value="onSelect"
      :groups="[{ key: 'counserlor', commands: counserlorsForPalette }]"
    >
      <template #counserlor-icon="{ command }">
        <UAvatar
          v-bind="command.avatar"
          size="md"
          loading="lazy"
          :ui="{
            rounded: 'rounded-lg',
            background: 'bg-gray-300 dark:bg-gray-400',
            placeholder:
              'text-xs font-semibold text-gray-700 dark:text-gray-800',
            chip: {
              size: {
                xs: 'h-2 w-2',
              },
            },
          }"
        />
      </template>
      <template #counserlor-command="{ command }">
        <div class="flex items-center justify-between w-full space-x-4">
          <div>
            <div>{{ command.label }}</div>
            <div class="text-xs font-light">{{ command.mailAddress }}</div>
          </div>
        </div>
      </template>
      <template #counserlor-inactive="{ command }">
        <UBadge
          size="xs"
          variant="solid"
          :color="getCounselorRoleColor(command.role)"
        >
          {{ t(command.role) }}
        </UBadge>
      </template>
    </UCommandPalette>
  </UModal>
</template>

<script lang="ts" setup>
  import { storeToRefs } from "pinia";
  import { useCounselorsStore } from "~/stores/admin/counselors";
  const { t } = useI18n();
  const props = defineProps({
    isOpen: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(["close", "select"]);
  const counselorsStore = useCounselorsStore();
  const { loadings, counserlorsForPalette } = storeToRefs(counselorsStore);

  const commandPaletteRef = ref();

  function onSelect(option: any) {
    emit("select", option);
  }
</script>
