<template>
  <UDropdown
    v-if="user"
    :items="items"
    :ui="{ item: { disabled: 'cursor-text select-text' } }"
    :popper="{ placement: 'bottom-start' }"
  >
    <UAvatar :src="user.profileImage" :alt="user.fullName" />

    <template #account="{ item }">
      <div class="text-left">
        <p>
          {{ user?.fullName }}
        </p>
        <p class="truncate font-medium text-gray-900 dark:text-white">
          {{ item.label }}
        </p>
      </div>
    </template>

    <template #item="{ item }">
      <span class="truncate">{{ item.label }}</span>

      <UIcon
        :name="item.icon"
        class="flex-shrink-0 h-4 w-4 text-gray-400 dark:text-gray-500 ms-auto"
      />
    </template>
  </UDropdown>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAuthStore } from "~/stores/auth";

  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);

  const items = computed(() => {
    return [
      [
        {
          label: user.value?.email,
          icon: "i-heroicons-user-circle",
          disabled: true,
          slot: "account",
        },
      ],
      [
        {
          label: "Profile",
          icon: "i-heroicons-user",
        },
        {
          label: "Settings",
          icon: "i-heroicons-cog",
        },
        {
          label: "Sign out",
          icon: "i-heroicons-logout",
          click: () => authStore.logout(),
        },
      ],
    ];
  });
</script>
