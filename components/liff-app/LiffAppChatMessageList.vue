<template>
  <div
    ref="chatMessagesList"
    class="overflow-y-auto h-auto flex-1 hidden-scrollbar space-y-4 scroll-smooth"
  >
    <div v-if="loading" class="px-0 pt-20 pl-10">
      <div v-for="i in 2" :key="i" class="flex flex-col space-y-4 mb-4 z-10">
        <div class="flex flex-col items-end justify-end space-y-2">
          <USkeleton class="h-12 w-1/2 bg-gray-300" />
          <USkeleton class="h-4 w-32 bg-gray-300" />
        </div>
        <div class="space-y-2">
          <USkeleton class="h-12 w-2/3 bg-gray-300" />
          <USkeleton class="h-4 w-32 bg-gray-300" />
        </div>
      </div>
    </div>
    <LiffAppChatMessage
      v-for="(message, index) in props.messages"
      v-else
      :key="index"
      :data="message"
    />
  </div>
</template>

<script setup lang="ts">
  const chatMessagesList = ref();

  const props = defineProps<{
    messages: any[];
    loading: boolean;
  }>();

  watch(
    () => props.messages,
    () => {
      nextTick(() => {
        if (chatMessagesList.value) {
          // scroll to bottom
          chatMessagesList.value.scrollTop =
            chatMessagesList.value.scrollHeight;
        }
      });
    },
    { immediate: true, deep: true },
  );
</script>
