<template>
  <div>
    <div class="flex flex-row items-center">
      <UCard
        :ui="{
          body: {
            padding: '!px-0 !py-0  min-h-[52px] flex items-center',
          },
        }"
        class="relative flex-1"
      >
        <UTextarea
          v-model="messageText"
          ref="inputBox"
          autoresize
          variant="none"
          placeholder="メッセージ..."
          :rows="1"
          class="relative w-full min-h-[60px] pl-1.5 pr-10 max-h-[200px] overflow-auto hidden-scrollbar"
          size="xl"
          :ui="{
            padding: {
              sm: 'pb-0',
            },
          }"
        >
        </UTextarea>
        <UButton
          variant="solid"
          :square="true"
          icon="i-heroicons-arrow-up"
          :color="messageText ? 'primary' : 'gray'"
          :disabled="!messageText"
          :ui="{ rounded: 'rounded-lg' }"
          size="xl"
          class="absolute right-2 bottom-2 mt-2"
          @click="onSend"
        />
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
  const { metaSymbol } = useShortcuts();
  const inputBox = ref<{ textarea: HTMLTextAreaElement } | null>(null);

  const props = defineProps({
    modelValue: {
      type: String,
      default: "",
    },
  });
  const emits = defineEmits(["send", "update:modelValue"]);
  const onSend = () => {
    if (!messageText.value) {
      return;
    }
    emits("send", messageText.value);
    messageText.value = "";
    // inputBox.value?.textarea?.focus();
  };

  const messageText = computed({
    get: () => props.modelValue || "",
    set: (value) => {
      emits("update:modelValue", value);
    },
  });

  defineShortcuts({
    meta_enter: {
      usingInput: true,
      handler: () => {
        onSend();
      },
    },
  });

  onMounted(() => {
    inputBox.value?.textarea?.focus();
  });
</script>
