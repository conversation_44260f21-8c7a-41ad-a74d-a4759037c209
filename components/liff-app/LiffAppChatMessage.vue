<template>
  <div
    class="group flex flex-row items-end space-x-2"
    :class="{
      'justify-start flex-row-reverse space-x-reverse': isRightMessage,
    }"
  >
    <div v-if="showAvatar" class="z-0">
      <UIcon
        name="i-fluent-emoji-robot"
        v-if="data.sender === 'chatBot'"
        class="text-3xl ml-0.5 shadow-lg group-hover:shadow-xl group-hover:scale-125 transition-all duration-300 z-0"
      />
      <UAvatar
        v-else
        v-bind="{
          alt: customer?.basic?.customerName,
          src: customer?.basic?.customerImage,
        }"
        size="sm"
        loading="lazy"
        class="group-hover:shadow-xl group-hover:scale-125 transition-all duration-300 z-0"
        :ui="{
          background: 'bg-gray-300 dark:bg-gray-400',
          placeholder: 'text-xs font-semibold text-gray-700 dark:text-gray-800',
        }"
      />
    </div>
    <div class="max-w-[77%] flex flex-col" :class="[{ 'items-end': isRightMessage }]">
      <div
        class="text-[10px] px-2 font-semibold text-right cursor-pointer"
        :class="{
          'text-gray-500': isRightMessage,
          'text-primary-600': isChatBotMessage,
          '!text-left text-primary-500': !isRightMessage,
        }"
      >
        <span v-if="isChatBotMessage">
          {{ $t("chatBot") }}
        </span>
        <div v-else>
          {{ isRightMessage ? "自分" : "" }}
        </div>
      </div>

      <div
        class="w-fit px-2.5 py-2.5 rounded-xl text-sm break-words whitespace-break-spaces"
        :class="[
          isRightMessage
            ? 'bg-primary-300 dark:bg-primary-800 rounded-br-none'
            : 'bg-gray-200 dark:bg-gray-800 rounded-bl-none',
          {
            'bg-gray-300 dark:bg-gray-800': isChatBotMessage,
          },
        ]"
      >
        <div class="break-all whitespace-break-spaces">
          <a
            class="underline"
            :class="{
              'text-primary-700': !isRightMessage,
            }"
            v-if="isALink(data.content?.text)"
            :href="data.content?.text"
            target="_blank"
          >
            {{ data.content?.text }}
          </a>
          <span v-else>{{ data.content?.text }}</span>
        </div>
        <div v-if="data.survey" class="mt-2 border-primary-700 bg-gray-100 rounded-lg">
          <div class="font-semibold px-4 py-3 text-center truncate">
            {{ data.survey?.surveyName }}
          </div>
          <div class="text-primary-800 border-t py-1 px-4 flex items-center space-x-1">
            <UButton
              :to="surveyLink"
              target="_blank"
              color="primary"
              variant="ghost"
              icon="i-majesticons-open"
              trailing
              class="w-full"
              size="xs"
              block
              >アンケートを開く</UButton
            >
          </div>
        </div>
      </div>
      <div
        class="flex px-2 pt-1 font-light text-[9px]"
        :class="[{ 'justify-end': isRightMessage }]"
      >
        <!-- <span> {{ fromNow(new Date(data.createdAt || "")) }}</span> -->
        <div class="flex space-x-1 items-center" v-if="data.isSending">
          <Icon icon="eos-icons:loading" class="text-sm" />
          <span> 送信中</span>
        </div>
        <div class="flex space-x-1 items-center text-red-500" v-else-if="data.error">
          <Icon icon="bx:error" class="text-sm" />
          <span> {{ data.error }}</span>
        </div>
        <span v-else>
          {{ formatDateForMessage(new Date(data.createdAt || "")) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import type { CaseChatMessage } from "~/types";
import { useAuthStore } from "~/stores/auth";
import { Icon } from "@iconify/vue";
const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const liffAppStore = useLiffAppStore();
const { customer, counselee } = storeToRefs(liffAppStore);
const props = defineProps({
  data: {
    type: Object as PropType<CaseChatMessage>,
    required: true,
  },
});

const isRightMessage = computed(() => {
  return ["counselee"].includes(props.data?.sender);
});

const isChatBotMessage = computed(() => {
  return props.data?.sender === "chatBot";
});

const showAvatar = computed(() => {
  return true;
});
const router = useRouter();
const surveyLink = computed(() => {
  return (
    window.location.origin +
    "/survey/?sid=" +
    props.data.survey?.surveyId +
    "&c=" +
    customer.value?.customerId +
    "&u=" +
    counselee.value?.counseleeId
  );
});
</script>
