<template>
  <div class="pb-8 pt-2">
    <div
      v-if="schema?.img"
      class="px-4 w-full lg:w-3/4 flex justify-center mx-auto mb-6"
    >
      <img :src="schema?.img" />
    </div>
    <div class="text-center flex flex-col space-y-2 pb-20">
      <div class="text-3xl text-primary-600">{{ $t(schema?.title || "") }}</div>
      <div class="text-xl" v-html="$t(schema?.description || '')"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { WizardElement } from "@/types";

  const props = defineProps<{
    schema: WizardElement;
    active: boolean;
    render?: boolean;
  }>();

  const emits = defineEmits(["click:button"]);
</script>
