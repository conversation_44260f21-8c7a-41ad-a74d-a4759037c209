<template>
  <div class="py-8">
    <div class="text-center p-4">
      <div class="text-3xl text-primary-600">{{ $t(schema?.title || "") }}</div>
      <div>{{ $t(schema?.description || "") }}</div>
    </div>

    <div class="my-0 w-full max-w-screen-xl px-3 mx-auto">
      <div class="flex flex-wrap justify-center">
        <UInput
          size="xl"
          class="w-full max-w-3xl"
          :placeholder="schema.placeholder"
          v-model="schema.value"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { WizardElement } from "@/types";

  const props = defineProps<{
    schema: WizardElement;
    active: boolean;
    render?: boolean;
  }>();
</script>
