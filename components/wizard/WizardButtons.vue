<template>
  <div
    v-if="schema.buttons?.length"
    class="fixed bottom-0 sm:bottom-3 inset-x-0 flex sm:flex-row flex-col justify-center gap-2 bg-gray-400/75 sm:w-fit mx-auto px-4 py-3 sm:rounded-full border shadow-md"
  >
    <div
      v-if="loading"
      class="flex flex-row justify-center items-center gap-2 px-10"
    >
      <Icon icon="eos-icons:loading" class="text-2xl text-gray-50" />
      <div class="text-sm text-gray-50">
        {{ $t("処理中") }}
      </div>
    </div>
    <template v-else v-for="buttonElm in schema.buttons">
      <UButton
        @click="emits('click:button', buttonElm, schema)"
        :icon="buttonElm.icon"
        size="xl"
        :color="buttonElm.color"
        variant="solid"
        :label="$t(buttonElm.text)"
        :trailing="buttonElm.trailing"
        :ui="{ rounded: 'rounded-full' }"
        class="px-10 justify-center"
        :disabled="buttonElm.isNext && !isCanNext"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
  import type { WizardElement } from "@/types";
  import { Icon } from "@iconify/vue";
  const props = defineProps<{
    schema: WizardElement;
    isCanNext: boolean;
    loading: boolean;
  }>();

  const emits = defineEmits(["click:button"]);
</script>
