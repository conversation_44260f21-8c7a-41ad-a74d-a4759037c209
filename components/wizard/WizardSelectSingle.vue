<template>
  <div class="py-8">
    <div class="text-center p-4">
      <div class="text-3xl text-primary-600">{{ $t(schema?.title || "") }}</div>
      <div>{{ $t(schema?.description || "") }}</div>
    </div>

    <div class="my-0 w-full max-w-screen-xl px-3 mx-auto">
      <div class="flex flex-wrap justify-center">
        <div
          v-for="selectOption in schema?.values"
          class="group flex flex-col"
          :class="schema?.valueClass || `max-w-sm p-4 lg:w-1/4 sm:w-1/2`"
        >
          <BaseCardSelect
            v-bind="selectOption"
            v-model="schema.value"
            class="h-full flex flex-col items-center justify-center"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { WizardElement } from "@/types";

  const props = defineProps<{
    schema: WizardElement;
    active: boolean;
    render?: boolean;
  }>();
</script>
