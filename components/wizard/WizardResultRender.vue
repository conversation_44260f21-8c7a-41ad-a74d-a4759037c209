<template>
  <div class="flex flex-col space-y-2">
    <div
      v-for="data in stateConfirms"
      class="text-sm border-b pb-1 border-dashed dark:border-gray-600"
    >
      <div class="font-semibold">
        {{ data.title }}
      </div>
      <div class="font-light">
        <div v-if="Array.isArray(data.value)">
          <ul
            v-if="data.value.length"
            v-for="row in data.value"
            class="list-disc list-inside"
          >
            <li v-if="row === OtherOptionKey">
              その他：{{ data.otherOption }}
            </li>
            <li v-else>
              {{ row }}
            </li>
          </ul>
          <div v-else>未選択</div>
        </div>
        <div v-else>
          {{ data.value || "未入力" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { WizardFormElement } from "@/types";

  const props = defineProps<{
    formTemplate: WizardFormElement[];
  }>();
  const { OtherOptionKey } = useFormSurveyElements();
  const { FormElementTypes } = useWizard();

  const stateConfirms = computed(() => {
    const stateForm = [] as Record<string, unknown>[];
    props.formTemplate
      .filter(
        (obj) =>
          FormElementTypes.includes(obj.type) &&
          (Array.isArray(obj.value) ? obj.value.length : obj.value),
      )
      .forEach((element) => {
        let value = element.value;
        if (element.type === "date") {
          value = formatDate(new Date(element.value as string));
        }
        if (element.id === "schoolId") {
          value = getSchoolName(("" + element.value) as string);
        }
        stateForm.push({
          title: element.title,
          value: value,
          otherOption: element.otherOption,
        });
      });
    return stateForm;
  });
</script>
