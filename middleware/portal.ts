export default defineNuxtRouteMiddleware((to) => {
  const route = useRoute();
  const { isAuthenticated } = storeToRefs(usePortalStore());

  if (isAuthenticated.value && to?.name === "portal-slug-login") {
    return navigateTo("/portal/" + route.params.slug);
  }

  if (!isAuthenticated.value && to?.name !== "portal-slug-login") {
    abortNavigation();
    return navigateTo("/portal/" + route.params.slug + "/login");
  }
});
