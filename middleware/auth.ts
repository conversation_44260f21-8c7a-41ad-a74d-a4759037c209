import { storeToRefs } from "pinia";
import { useAuthStore } from "~/stores/auth";
import { CounselorRole } from "~/types/enums.d";
export default defineNuxtRouteMiddleware((to) => {
  const { authenticated, user } = storeToRefs(useAuthStore());
  const { currentCustomerId } = storeToRefs(useAppCustomersStore());

  const accessToken = useCookie("accessToken"); // get token from cookies
  if (to?.params?.customerId) {
    currentCustomerId.value = to?.params?.customerId as string;
  }
  if (accessToken.value) {
    authenticated.value = true;
  }

  if (accessToken.value && to?.name === "login") {
    if (user.value?.role === CounselorRole.ADMIN) {
      return navigateTo("/admin");
    } else {
      return navigateTo("/app?customerId=" + currentCustomerId.value);
    }
  }

  if (!accessToken.value && to?.name !== "login" && !to.meta?.noNeedAuth) {
    abortNavigation();
    return navigateTo("/login");
  }

  console.log("🚀 ~ defineNuxtRouteMiddleware ~ user:", user.value);
});
