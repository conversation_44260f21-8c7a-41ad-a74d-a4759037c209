import { useWebAppStore } from "~/stores/web-app";
import { storeToRefs } from "pinia";

export default defineNuxtRouteMiddleware(() => {
  const webAppStore = useWebAppStore();
  const { counselee } = storeToRefs(webAppStore);
  console.log("🚀 ~ defineNuxtRouteMiddleware ~ counselee:", counselee);
  const route = useRoute();
  if (!counselee.value) {
    abortNavigation();
    return navigateTo(`/web-app/${route.params.slug}/code-input`);
  }
});
