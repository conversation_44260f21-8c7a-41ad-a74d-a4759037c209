import { storeToRefs } from 'pinia'
import { useAuthStore } from '~/stores/auth'
import { CounselorRole } from '~/types/enums.d'
export default defineNuxtRouteMiddleware(() => {
  const { user } = storeToRefs(useAuthStore())
  // const accessToken = useCookie('accessToken') // get token from cookies

  if (user.value?.role === CounselorRole.ADMIN) {
    return navigateTo('/admin')
  } else {
    return navigateTo('/app')
  }
})
