import { CaseStatus } from "~/types/enums.d";
import type { User } from "~/types";

interface CaseStatusProcessDynamic {
  [key: string]: CaseStatus[];
}
const CaseStatusProcessable: CaseStatusProcessDynamic = {
  [CaseStatus.BEFORE_START]: [
    CaseStatus.OPEN,
    CaseStatus.IN_PROGRESS,
    CaseStatus.RESOLVED,
    CaseStatus.CANCELLED,
  ],
  [CaseStatus.OPEN]: [
    CaseStatus.IN_PROGRESS,
    CaseStatus.RESOLVED,
    CaseStatus.CANCELLED,
  ],
  [CaseStatus.IN_PROGRESS]: [
    CaseStatus.OPEN,
    CaseStatus.WAITING,
    CaseStatus.RESOLVED,
  ],
  [CaseStatus.WAITING]: [CaseStatus.IN_PROGRESS, CaseStatus.RESOLVED],
  [CaseStatus.RESOLVED]: [CaseStatus.IN_PROGRESS],
  [CaseStatus.CANCELLED]: [CaseStatus.OPEN],
};
export const getNextCaseStatusList = (status: CaseStatus) => {
  return CaseStatusProcessable[status] || [];
};

export const getNextCaseStatusOptions = (status: CaseStatus) => {
  const statusList = getNextCaseStatusList(status);
  const { t } = useI18n();
  const options = statusList.map((option) => {
    return {
      value: option,
      label: t(`caseStatus.${option}`),
      icon: "i-heroicons-circle-solid",
      color: getCaseStatusColor(option),
    };
  });

  return [
    options.filter((option) => option.value !== CaseStatus.CANCELLED),
    options.filter((option) => option.value === CaseStatus.CANCELLED),
  ].filter((option) => option.length > 0);
};

export const needAddAssignee = (
  currentStatus: CaseStatus,
  nextStatus: CaseStatus,
) => {
  console.log(
    "🚀 ~ file: caseStatus.ts:57 ~ needAddAssignee ~ nextStatus:",
    nextStatus,
  );
  console.log(
    "🚀 ~ file: caseStatus.ts:57 ~ needAddAssignee ~ currentStatus:",
    currentStatus,
  );
  switch (currentStatus) {
    case CaseStatus.BEFORE_START:
      return [CaseStatus.IN_PROGRESS, CaseStatus.RESOLVED].includes(nextStatus);
    case CaseStatus.OPEN:
      return [CaseStatus.IN_PROGRESS, CaseStatus.RESOLVED].includes(nextStatus);
    case CaseStatus.IN_PROGRESS:
      return false;
    case CaseStatus.WAITING:
      return false;
    case CaseStatus.RESOLVED:
      return false;
    case CaseStatus.CANCELLED:
      return false;
    default:
      return false;
  }
};

export const canUpdateCase = (
  currentStatus: CaseStatus,
  loginUser?: User,
  counselorInChargeId?: string,
  userPermissions?: any[],
) => {
  return (
    ![CaseStatus.OPEN].includes(currentStatus) &&
    (([CaseStatus.IN_PROGRESS, CaseStatus.WAITING].includes(
      currentStatus,
    ) &&
      userPermissions?.includes(
        "update:case-attributes-of-open-processing-cases-i-am-in-charge",
      ) &&
      counselorInChargeId === loginUser?.counselorId) ||
      userPermissions?.includes(
        "update:case-attributes-of-open-processing-cases-i-am-not-in-charge",
      ) ||
      (userPermissions?.includes("update:case-attributes-of-resolved-cases") &&
        currentStatus === CaseStatus.RESOLVED))
  );
};
