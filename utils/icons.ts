import { SNSChannel, TagFormType } from "~/types/enums.d";

import FacebookSvg from "~/assets/images/facebook.svg";
import LineSvg from "~/assets/images/line.svg";
import ApplicationSvg from "~/assets/images/application.svg";

export const getSNSIconComponent = (sns?: string) => {
  switch (sns) {
    case SNSChannel.FACEBOOK:
      return FacebookSvg;
    case SNSChannel.LINE:
      return LineSvg;
    case SNSChannel.APPLICATION:
      return ApplicationSvg;
    default:
      return ApplicationSvg;
  }
};

export const getFormTempalteIcon = (tagFormType?: string) => {
  switch (tagFormType) {
    case TagFormType.TEXT:
      return "i-ph-textbox";
    case TagFormType.CHECKBOX:
      return "i-mdi-checkbox-outline";
    case TagFormType.PULLDOWN:
      return "i-tabler-select";
    case TagFormType.RADIO:
      return "i-formkit-radio";
    case TagFormType.TEXT_AND_PULLDOWN:
      return "i-gala-select";
    default:
      return "i-heroicons-question-mark-circle";
  }
};
