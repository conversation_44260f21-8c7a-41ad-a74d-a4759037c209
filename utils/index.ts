import * as yup from "yup";
import type { SurveyFormElement } from "@/types";
import { CaseStatus, AgeDecade, CounselorRole } from "~/types/enums.d";
export const generateUniqueOption = (label: string, options: string[]) => {
  let newLabel = label;
  let count = 2;
  while (options.includes(newLabel)) {
    newLabel = `${label} ${count}`;
    count++;
  }
  return newLabel;
};

export const createYupSchema = (formTemplate: SurveyFormElement[]) => {
  let schema = {} as any;
  formTemplate.forEach((element) => {
    const { _id, required } = element;
    let validator;
    if (required) {
      // check type is not selectMulti
      if (element.type !== "selectMulti") {
        validator = yup.string();
        validator = validator.required("必須項目です");
      } else {
        validator = yup.array().min(1, "必須項目です");
      }
      schema[_id] = validator;
    }
  });

  return yup.object().shape(schema);
};

export const getSocketStatusObject = (
  status: "OPEN" | "CONNECTING" | "CLOSED",
) => {
  switch (status) {
    case "OPEN":
      return {
        color: "green",
        text: "接続",
      };
    case "CONNECTING":
      return {
        color: "yellow",
        text: "接続中",
      };
    case "CLOSED":
      return {
        color: "red",
        text: "NG",
      };
    default:
      return {
        color: "gray",
        text: "",
      };
  }
};
const { ageOptions } = useConstants();
export const getAgeDecadeLabel = (ageDecade: AgeDecade) => {
  const ageOption = ageOptions.find((option) => option.value === ageDecade);
  return ageOption?.label || "";
};

export const getLinkSurvey = (
  surveyId: string,
  customerId: string,
  counseleeId?: string,
) => {
  const router = useRouter();
  const routerData = router.resolve({
    name: "survey",
    query: {
      sid: surveyId,
      c: customerId,
      u: counseleeId,
    },
  });
  return routerData.href;
};

export const getCounselortRoleIcon = (role: string) => {
  switch (role) {
    case CounselorRole.ADMIN:
      return "eos-icons:admin";
    case CounselorRole.SUPERVISOR:
      return "mdi:account-star";
    case CounselorRole.GENERAL:
      return "mdi:account-settings";
    default:
      return "solar:eye-scan-bold";
  }
};

export const getSchoolName = (schoolId: string) => {
  const { SCHOOL_LIST } = useConstants();
  const school = SCHOOL_LIST.find((s) => s.schoolId === schoolId);
  return school?.schoolName || "不明";
};

export const isALink = (url: string) => {
  const pattern = new RegExp("^(http|https)://");
  if (url === "http://" || url === "https://" || url === "http") {
    return false;
  }
  return pattern.test(url);
};
