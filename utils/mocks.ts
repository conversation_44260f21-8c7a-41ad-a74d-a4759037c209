import dayjs from "dayjs";
import _random from "lodash/random";
import { faker } from "@faker-js/faker/locale/ja";
import {
  CaseStatus,
  CasePriority,
  ConsultantRole,
  SNSChannel,
} from "~/types/enums.d";

export const randomFromTo = (from: number, to: number) => {
  return _random(from, to);
};

export const randomCaseStatus = () => {
  const status = [
    CaseStatus.BEFORE_START,
    CaseStatus.OPEN,
    CaseStatus.IN_PROGRESS,
    CaseStatus.WAITING,
    CaseStatus.RESOLVED,
    CaseStatus.CANCELLED,
  ];
  return status[randomFromTo(0, status.length - 1)];
};

export const randomCasePriority = () => {
  const priority = [CasePriority.HIGH, CasePriority.NORMAL, CasePriority.LOW];
  return priority[randomFromTo(0, priority.length - 1)];
};

export const randomConsultantRole = () => {
  const role = [
    ConsultantRole.SUPERVISOR,
    ConsultantRole.GENERAL,
    ConsultantRole.GENERAL,
    ConsultantRole.GENERAL,
  ];
  return role[randomFromTo(0, role.length - 1)];
};

export const randomSNSChannel = () => {
  const sns = [
    SNSChannel.FACEBOOK,
    SNSChannel.LINE,
    SNSChannel.LINE,
    SNSChannel.LINE,
    SNSChannel.LINE,
    SNSChannel.APPLICATION,
    SNSChannel.APPLICATION,
  ];
  return sns[randomFromTo(0, sns.length - 1)];
};

export const casesMock = (total: number) => {
  console.log("casesMock", total);
  const cases = [];
  for (let i = 0; i < total; i++) {
    const _messages = [];
    for (let i = 0; i < 50; i++) {
      _messages.push({
        isUserMessage: i % 2 === 0,
        message: "これは古いメッセージです。",
      });
    }
    const status = randomCaseStatus();
    const caseObj = {
      id: i,
      assigneeName: [CaseStatus.BEFORE_START, CaseStatus.OPEN].includes(status)
        ? ""
        : faker.person.fullName(),
      status,
      openedAt: dayjs().subtract(randomFromTo(1, 5), "day").unix(),
      inProgressingAt: [CaseStatus.BEFORE_START, CaseStatus.OPEN].includes(
        status
      )
        ? undefined
        : dayjs().subtract(randomFromTo(1, 24), "hour").unix(),
      latestMessageAt: dayjs().subtract(randomFromTo(1, 3), "hour").unix(),
      clientName: faker.person.fullName(),
      userId: faker.string.numeric({ length: 5 }),
      times: randomFromTo(1, 3),
      priority: randomCasePriority(),
      snsChannel: randomSNSChannel(),
      userAvatar: faker.image.avatar(),
      chat: _messages,
      caseConveyed: faker.lorem.sentence(),
      memos: [
        faker.lorem.sentence(),
        faker.lorem.sentence(),
        faker.lorem.sentence(),
      ],
      selectedTags: [
        {
          id: "C-1",
          label: "話せる言語",
          icon: "i-mdi-checkbox-outline",
          tagId: "C-1",
          tagName: "話せる言語",
          formTemplateId: "checkbox",
          formTemplate:
            '{ "options": ["日本語", "英語", "ベトナム語", "韓国語"] }',
          customerId: "PNL",
          createdAt: "2023-11-29T08:06:30.524Z",
          group: "checkbox",
          refIndex: 4,
          value: null,
          no: 0,
        },
        {
          id: "P-1",
          label: "年齢",
          icon: "i-tabler-select",
          tagId: "P-1",
          tagName: "年齢",
          formTemplateId: "pulldown",
          formTemplate:
            '{ "options": ["10代", "20代", "30代", "40代", "50代", "60歳以上"] }',
          customerId: "PNL",
          createdAt: "2023-11-29T08:06:30.524Z",
          group: "pulldown",
          refIndex: 2,
          value: null,
          no: 1,
        },
      ],
    };
    cases.push(caseObj);
  }
  return cases;
};

export const consultantsMock = (total: number) => {
  console.log("consultantsMock", total);
  const consultants = [];
  for (let i = 0; i < total; i++) {
    const online = faker.datatype.boolean();
    const fullName = faker.person.fullName();
    const consultantObj = {
      id: i,
      name: fullName,
      avatar: faker.image.avatar(),
      numberOfActiveCases: online ? randomFromTo(0, 5) : 0,
      online,
      role: randomConsultantRole(),
      email: faker.internet.email(),
      organizationName: faker.company.name(),
      fullName,
      counselorId: faker.string.uuid(),
    };
    consultants.push(consultantObj);
  }
  return consultants;
};
