import type { User, CaseChatMessage } from "~/types";
import { useSocketStore } from "~/stores/socket";
export const useWebAppStore = defineStore("webAppStore", {
  persist: {
    paths: ["customer", "case", "counselee"],
    storage: window.localStorage,
  },
  state: () => ({
    customer: null as any | null,
    counselee: null as any | null,
    case: null as any | null,
    messages: [] as any[],
    loadings: {
      login: false,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,
    code: "",
  }),
  getters: {
    isAuthenticated: (state) => {
      return !!state.counselee;
    },
  },
  actions: {
    async joinChatByCode(
      slug: string,
      code: string,
    ): Promise<any | false | undefined> {
      try {
        this.errors.joinChatByCode = null;
        this.loadings.joinChatByCode = true;
        this.code = code;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/login/anonymous", {
          method: "POST",
          data: { browserAccessCode: code, slug },
        });

        this.customer = {
          ...this.customer,
          ...data.value?.body?.customer,
        };
        this.counselee = data.value?.body?.counselee;
        this.case = data.value?.body?.case;
        this.messages = [];

        if (data.value) {
          return data.value;
        }
      } catch (error: any) {
        this.errors.joinChatByCode =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.joinChatByCode = false;
      }
    },

    joinChatByWizard(customer: any, counselee: any, caseData: any) {
      this.customer = {
        ...this.customer,
        ...customer,
      };
      this.counselee = counselee;
      this.case = caseData;
      this.messages = [];
    },

    async fetchCustomerInfo(slug: string): Promise<any | false | undefined> {
      const route = useRoute();
      try {
        this.errors.fetchCustomerInfo = null;
        this.loadings.fetchCustomerInfo = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useCounseleeService("/counselee-chat/anonymous/" + slug, {
          method: "GET",
        });
        console.log("🚀 ~ fetchCustomerInfo ~ this.customer:", this.customer);

        if (data.value?.body?.id !== this.customer?.customerId && this.customer?.customerId) {
          this.endChat();
          window.location.href =
            "/web-app/" + route.params.slug + "/code-input";
        }

        this.customer = {
          ...(this.customer || {}),
          ...data.value?.body,
        };
        const appConfig = useAppConfig();
        appConfig.ui.primary = "cyan";
      } catch (error: any) {
        this.errors.fetchCustomerInfo = error?.response?.data;
        return false;
      } finally {
        this.loadings.fetchCustomerInfo = false;
      }
    },

    async sendMessage(message: string): Promise<any | false | undefined> {
      try {
        this.errors.sendMessage = null;
        this.loadings.sendMessage = true;
        this.messages.push({
          content: {
            text: message,
          },
          createdAt: new Date().toISOString(),
          sender: "counselee",
          isSending: true,
        });
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/anonymous", {
          method: "POST",
          data: {
            content: {
              text: message,
            },
            customerId: this.customer?.customerId,
            counseleeId: this.counselee?.counseleeId,
          },
        });
        // find the message and update it
        data.value?.body?.sendMessages?.forEach((msg: CaseChatMessage) => {
          const index = this.messages.findIndex(
            (message) =>
              message.isSending && message.content.text === msg.content.text,
          );
          if (index !== -1) {
            this.messages[index] = msg;
          } else {
            this.messages.push(msg);
          }
        });
      } catch (error: any) {
        this.errors.sendMessage =
          error?.response?.data?.error || error?.message;
        // find the message and update it
        const index = this.messages.findIndex(
          (msg: CaseChatMessage) =>
            msg.isSending && msg.content?.text === message,
        );
        if (index !== -1) {
          this.messages[index].error = this.errors.sendMessage;
          this.messages[index].isSending = false;
        }
        return false;
      } finally {
        this.loadings.sendMessage = false;
      }
    },

    receiveMessage(caseId: string, message: CaseChatMessage) {
      console.log("🚀 ~ receiveMessage ~ message:", message);
      if (this.case?.caseId === caseId) {
        // check if message already exists
        const isExist = this.messages.find(
          (msg) => msg.chatId === message.chatId,
        );
        if (!isExist) {
          this.messages.push(message);
        }
      }
    },

    async endChat(): Promise<any | false | undefined> {
      const socketStore = useSocketStore();
      try {
        this.errors.endChat = null;
        this.loadings.endChat = true;
        this.counselee = null;
        this.case = null;
        this.customer = null;
        socketStore.closeWebAppSocket();
      } catch (error: any) {
        this.errors.endChat = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.endChat = false;
      }
    },

    async fetchMessages(): Promise<any | false | undefined> {
      try {
        this.errors.fetchMessages = null;
        this.loadings.fetchMessages = true;
        // sleep 1s
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error: any) {
        this.errors.fetchMessages = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.fetchMessages = false;
      }
    },
  },
});
