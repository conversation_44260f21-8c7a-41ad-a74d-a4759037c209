import { useAppUIStore } from "~/stores/app/ui";
import { useCounselorsStore } from "~/stores/app/counselors";
import { useCasesStore } from "~/stores/app/cases";
import { useAppCustomersStore } from "~/stores/app/customers";
import { useWebAppStore } from "~/stores/web-app";

import { storeToRefs } from "pinia";
import type { Customer } from "~/types";

export const useSocketStore = defineStore("socketStore", {
  state: () => ({
    loadings: {
      login: false,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,
    appSocket: null as any,
    webAppSocket: null as any,
    liffAppSocket: null as any,
    customerIds: [] as string[],
    counselorId: "",
    counseleeId: "",
  }),
  getters: {},
  actions: {
    // @Websocket No3,4 カウンセラー又は管理者がログインした時のオンライン通知
    appConnectSocket(customerIds: string[], counselorId: string) {
      if (!this.appSocket || this.appSocket.status === 'CLOSED') {
        const runtimeConfig = useRuntimeConfig();
        this.customerIds = customerIds;
        this.counselorId = counselorId;
        const socketHandler = {
          autoReconnect: true,
          // heartbeat: {
          //   message: JSON.stringify({
          //     action: "online",
          //     type: "online",
          //     payload: {
          //       customerIds: customerIds,
          //       counselorId: counselorId,
          //       isLogin: true,
          //     },
          //   }),
          //   interval: 60000 * 5, // 5 minutes
          //   pongTimeout: 5000,
          // },
          onOpen: (e: any, open: any) => {
            console.log("onOpen: ", open);
          },
          onMessage: (e: any, msg: any) => {
            // console.log("onMessage: ", msg.data);
            // parse message data to json
            try {
              const data = JSON.parse(msg.data);
              console.log("🚀 ~ appConnectSocket ~ data:", data);
              const _self: any = this;
              if (_self[data.type]) {
                _self[data.type](data.payload);
              } else {
                console.error("no action");
              }
            } catch (error) {}
          },
          onError: (e: any, err: any) => {
            console.log("onError: ", err);
          },
          onClose: (e: any, close: any) => {
            console.log("onClose: ", close);
          },
        };
        const socketUrl = runtimeConfig.public.NUXT_SOCKET_BASE_URL;

        const appSocket = useWebSocket(
          socketUrl +
            `?customerIds=${customerIds.join(",")}&counselorId=${counselorId}`,
          socketHandler,
        );
        this.appSocket = appSocket;
      }
    },

    closeAppSocket() {
      this.sendOnlineStatus(false);
      this.appSocket?.close();
    },

    online(payload: any) {
      const counselorsStore = useCounselorsStore();
      console.log("online: ", payload);
      counselorsStore.setOnline([payload.counselorId], payload.isLogin);
    },

    // send online status to server
    sendOnlineStatus(isOnline: boolean) {
      const data = {
        action: "online",
        type: "online",
        payload: {
          customerIds: this.customerIds,
          counselorId: this.counselorId,
          isLogin: isOnline,
        },
      };
      this.appSocket?.send(JSON.stringify(data));
    },

    //type chat
    chat(payload: any) {
      const toast = useToast();
      const casesStore = useCasesStore();
      const customersStore = useAppCustomersStore();
      const { caseDetail } = storeToRefs(casesStore);
      const { customers, currentCustomer } = storeToRefs(customersStore);
      casesStore.receiveMessage(payload.caseId, payload.message);

      // check if case is not open
      if (
        caseDetail.value?.caseId !== payload.caseId &&
        payload.message?.sender === "counselee"
      ) {
        customersStore.updateUnReadMessages({
          ...payload.message,
          caseId: payload.caseId,
          customerId: payload.customerId,
        });
        const customer = customers.value.find(
          (obj) => obj.customerId === payload.customerId,
        );
        casesStore.updateUnReadMessages({
          ...payload.message,
          caseId: payload.caseId,
          customerId: payload.customerId,
        });
        toast.add({
          id: payload.caseId,
          avatar: {
            src: payload.message?.senderAvatar,
            alt: payload.message?.senderName,
          },
          title: `[${customer?.basic?.customerName}] ${payload.message?.senderName}さんの新着メッセージがあります`,
          description: payload.message?.content?.text,
          actions: [
            {
              label: "チャットを開く",
              click: () => {
                currentCustomer.value = customer as Customer;
                casesStore.fetchCaseDetail(payload.caseId);
                navigateTo(`/app/chats?caseId=${payload.caseId}`);
              },
              color: "primary",
              variant: "solid",
            },
          ],
        });
      }
    },

    counselorInChargeIdCount(payload: any) {
      const counselorsStore = useCounselorsStore();
      const casesStore = useCasesStore();
      if (payload.incrementCounselorId) {
        counselorsStore.updateHasCaseCount(payload.incrementCounselorId, 1);
      }
      if (payload.decrementCounselorId) {
        counselorsStore.updateHasCaseCount(payload.decrementCounselorId, -1);
      }

      casesStore.fetchCaseStatistics(payload.customerId);
    },

    inProgressCount(payload: any) {
      const customersStore = useAppCustomersStore();
      const casesStore = useCasesStore();

      customersStore.updateOpenCaseCount(
        payload.customerId,
        payload.isIncrement ? 1 : -1,
      );

      casesStore.fetchCaseStatistics(payload.customerId);
    },

    webAppConnectSocket(counseleeId: string) {
      console.log("🚀 ~ webAppConnectSocket ~ counseleeId:", counseleeId);
      const runtimeConfig = useRuntimeConfig();
      this.counseleeId = counseleeId;
      const socketHandler = {
        autoReconnect: true,
        onOpen: (e: any, open: any) => {
          console.log("webAppConnectSocket onOpen: ", open);
        },
        onMessage: (e: any, msg: any) => {
          // console.log("onMessage: ", msg.data);
          // parse message data to json
          try {
            const data = JSON.parse(msg.data);
            console.log("🚀 ~ webAppConnectSocket ~ data:", data);
            const _self: any = this;
            if (_self[data.type]) {
              _self["webapp_" + data.type](data.payload);
            } else {
              console.error("no action");
            }
          } catch (error) {}
        },
        onError: (e: any, err: any) => {
          console.log("webAppConnectSocket onError: ", err);
        },
        onClose: (e: any, close: any) => {
          console.log("webAppConnectSocket onClose: ", close);
        },
      };
      const socketUrl = runtimeConfig.public.NUXT_SOCKET_BASE_URL;
      const webAppSocket = useWebSocket(
        socketUrl + `?counseleeId=${counseleeId}`,
        socketHandler,
      );
      this.webAppSocket = webAppSocket;
    },
    closeWebAppSocket() {
      this.webAppSocket?.close();
    },
    //type chat
    webapp_chat(payload: any) {
      const webAppStore = useWebAppStore();
      webAppStore.receiveMessage(payload.caseId, payload.message);
    },
    liffAppConnectSocket(counseleeId: string) {
      const runtimeConfig = useRuntimeConfig();
      this.counseleeId = counseleeId;
      const socketHandler = {
        autoReconnect: true,
        onOpen: (e: any, open: any) => {
          console.log("liffAppConnectSocket onOpen: ", open);
        },
        onMessage: (e: any, msg: any) => {
          // console.log("onMessage: ", msg.data);
          // parse message data to json
          try {
            const data = JSON.parse(msg.data);
            console.log("🚀 ~ liffAppConnectSocket ~ data:", data);
            const _self: any = this;
            if (_self[data.type]) {
              _self["liffapp_" + data.type](data.payload);
            } else {
              console.error("no action");
            }
          } catch (error) {}
        },
        onError: (e: any, err: any) => {
          console.log("liffAppConnectSocket onError: ", err);
        },
        onClose: (e: any, close: any) => {
          console.log("liffAppConnectSocket onClose: ", close);
        },
      };
      const socketUrl = runtimeConfig.public.NUXT_SOCKET_BASE_URL;
      const liffAppSocket = useWebSocket(
        socketUrl + `?counseleeId=${counseleeId}`,
        socketHandler,
      );
      this.liffAppSocket = liffAppSocket;
    },
    //type chat
    liffapp_chat(payload: any) {
      const liffAppStore = useLiffAppStore();
      liffAppStore.receiveMessage(payload.message);
    },

    // emit message to server
    emitMessageToAppSocket(sendJsons: SendJsonSocket[]) {
      sendJsons.forEach((sendJson) => {
        if (this.appSocket) {
          this.appSocket.send(JSON.stringify(sendJson));
        }
      });
    },

    emitMessageToCounseleeSocket(sendJsons: SendJsonSocket[]) {
      sendJsons.forEach((sendJson) => {
        if (this.webAppSocket) {
          this.webAppSocket.send(JSON.stringify(sendJson));
        }

        if (this.liffAppSocket) {
          this.liffAppSocket.send(JSON.stringify(sendJson));
        }
      });
    },
  },
});
