import type { User, AuthLoginPayload } from "~/types";
import { useAuthService } from "~/composables/useAuthService";
import { useAppUIStore } from "~/stores/app/ui";
import { useCounselorsStore } from "./admin/counselors";
import dayjs from "dayjs";
export type UpdatePasswordRequest = {
  password: string;
  newPassword: string;
  confirmNewPassword: string;
};

import { useSocketStore } from "~/stores/socket";

export const useAuthStore = defineStore("auth", {
  persist: {
    paths: [
      "user",
      "authenticated",
      "forgotPasswordTime",
      "forgotPasswordRequested",
      "forgotPasswordUserName",
    ],
  },
  state: () => ({
    user: null as User | null,
    authenticated: false,
    loadings: {
      login: false,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,

    forgotPasswordTime: null as string | null,
    forgotPasswordUserName: null as string | null,
    forgotPasswordRequested: false,
    canSendOTPAfter: 30, // seconds
    canResetPasswordAfter: 300, // seconds
  }),
  getters: {
    isLoggedIn: (state) => {
      return !!state.user;
    },
    getCustomerRole: (state) => (customerId: string) => {
      return state.user?.customers?.find(
        (customer) => customer.customerId === customerId,
      )?.role;
    },
  },
  actions: {
    async login(
      authPayload: AuthLoginPayload,
    ): Promise<User | false | undefined> {
      try {
        this.errors.login = null;
        this.loadings.login = true;
        const {
          data,
          isLoading,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/login/auth", {
          method: "POST",
          data: authPayload,
        });

        this.loadings.login = isLoading.value;
        this.authenticated = true;
        this.user = {
          counselorId: authPayload.username,
          customers: [],
        };
        if (data.value) {
          const accessToken = useCookie("accessToken");
          accessToken.value = data.value.idToken;
          this.authenticated = true;
          return data.value;
        }
      } catch (error: any) {
        console.log("🚀 ~ error:", error);
        this.errors.login = error?.response?.data?.message || error.message;
        return false;
      } finally {
        this.loadings.login = false;
      }
    },
    async logout() {
      try {
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/counselor/logout", {
          method: "POST",
          data: {
            username: this.user?.counselorId,
          },
        });
      } catch (error: any) {
        console.log("logout error:", error);
      }
      const socketStore = useSocketStore();
      const accessToken = useCookie("accessToken");
      accessToken.value = null;
      this.user = null;
      this.authenticated = false;
      socketStore.closeAppSocket();
      navigateTo("/login", { replace: true, external: true});
    },

    async setPassword(
      passwordRequest: UpdatePasswordRequest,
    ): Promise<User | false | undefined> {
      try {
        this.errors.setPassword = null;
        this.loadings.setPassword = true;

        const {
          data,
          isLoading,
        }: {
          data: Ref<User>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/change-pwd", {
          method: "POST",
          data: {
            username: this.user?.fullName,
            password: passwordRequest.password,
            newPassword: passwordRequest.newPassword,
          },
        });

        this.user = data.value;
        this.loadings.setPassword = isLoading.value;

        if (data.value) {
          const accessToken = useCookie("accessToken");
          accessToken.value = data.value.accessToken;
          this.authenticated = true;
          return data.value;
        }
      } catch (error: any) {
        this.errors.setPassword = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.setPassword = false;
      }
    },

    async changePassword(
      currentPassword: string,
      newPassword: string,
    ): Promise<Boolean> {
      try {
        this.errors.changePassword = null;
        this.loadings.changePassword = true;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAuthService("/login/change-pwd", {
          method: "POST",
          data: {
            username: this.user?.counselorId,
            password: currentPassword,
            newPassword: newPassword,
          },
        });
        return true;
      } catch (error: any) {
        this.errors.changePassword =
          error?.response?.data?.message || error.message;
        return false;
      } finally {
        this.loadings.changePassword = false;
      }
    },

    async updateProfile(counselor: User): Promise<Boolean> {
      try {
        this.errors.updateProfile = null;
        this.loadings.updateProfile = true;
        console.log(counselor);

        const {
          data,
          isLoading,
        }: {
          data: Ref<User>;
          isLoading: Ref<boolean>;
        } = await useAuthService(
          `/counselor/profile/${counselor.counselorId}`,
          {
            method: "PATCH",
            data: {
              fullName: counselor.fullName ?? "",
              profileImage: counselor.profileImage ?? "",
            },
          },
        );
        this.user = counselor;
        const counselorsStore = useCounselorsStore();
        const { counselors } = storeToRefs(counselorsStore);
        counselors.value = counselors.value.map((item) => {
          if (item.counselorId === counselor.counselorId) {
            return {
              ...item,
              fullName: counselor.fullName || "",
              profileImage: counselor.profileImage || "",
            };
          }
          return item;
        });
        return true;
      } catch (error: any) {
        this.errors.updateProfile = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.updateProfile = false;
      }
    },

    async fetchUserMe(): Promise<User | false | undefined> {
      const appUIStore = useAppUIStore();
      const userRoles = useRoles();
      try {
        // appUIStore.toggleAppLoading(true);
        this.errors.fetchUserMe = null;
        this.loadings.fetchUserMe = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/counselor/" + this.user?.counselorId, {
          method: "GET",
        });

        if (data.value) {
          this.user = data.value;
          userRoles.value = [data.value.role];
          return data.value;
        }
      } catch (error: any) {
        this.errors.fetchUserMe = error?.response?.data?.error;
        return false;
      } finally {
        // appUIStore.toggleAppLoading(false);
        this.loadings.fetchUserMe = false;
      }
    },

    async forgotPassword(username: string, email: string): Promise<boolean | undefined> {
      try {
        this.errors.forgotPassword = null;
        this.loadings.forgotPassword = true;
        this.forgotPasswordRequested = false;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/login/forgot-password", {
          method: "POST",
          data: {
            username,
            email
          },
        });

        if (data.value) {
          this.forgotPasswordTime = dayjs().format();
          this.forgotPasswordRequested = true;
          this.forgotPasswordUserName = username;
          return data.value;
        }
      } catch (error: any) {
        this.errors.forgotPassword =
          error?.response?.data?.message || error.message;
        return false;
      } finally {
        // appUIStore.toggleAppLoading(false);
        this.loadings.forgotPassword = false;
      }
    },

    async resetPassword(
      username: string,
      otp: string,
      newPassword: string,
    ): Promise<boolean | undefined> {
      try {
        this.errors.resetPassword = null;
        this.loadings.resetPassword = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useAuthService("/login/confirm-forgot-password", {
          method: "POST",
          data: {
            username,
            otp: otp,
            newPassword,
          },
        });

        if (data.value) {
          this.forgotPasswordTime = null;
          return data.value;
        }
      } catch (error: any) {
        this.errors.resetPassword =
          error?.response?.data?.message || error.message;
        return false;
      } finally {
        // appUIStore.toggleAppLoading(false);
        this.loadings.resetPassword = false;
      }
    },
  },
});
