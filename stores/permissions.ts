import { cloneDeep } from "lodash";
import { storeToRefs } from "pinia";
import { Counselor<PERSON><PERSON> } from "~/types/enums.d";
const PermissionsMatrix = {
  [CounselorRole.ADMIN]: [
    // menu
    "read:app",
    "read:app-reports",
    "read:app-reports-consultation-logs",
    "read:app-reports-cases",
    "read:app-reports-summary",
    "read:app-reports-counselees",
    "read:app-chats",
    "read:app-surveys",
    "read:app-wizards",
    "read:app-chatbot",
    "read:app-chatbot-chatbot",
    "read:app-chatbot-scenario",
    "read:app-chatbot-scenario-create",
    "read:app-chatbot-scenario-id",
    "read:app-chatbot-scenario-id-edit",
    "read:app-chatbot-end-template",
    "read:app-chatbot-logs",
    "read:app-chatbot-detail-[id]",
    "read:app-chatbot-create",
    "read:app-chatbot-edit-[id]",
    "read:app-settings",
    "read:admin",
    "read:admin-counserlor-account",
    "read:admin-permission",

    // menu
    "read:counselor_detail",

    // settings
    "read:app-settings-accounts",
    "read:app-settings-automatic-chats",
    "read:app-settings-counseling-term",
    "read:app-settings-segment-delivery",
    "read:app-settings-monitoring-keywords",
    "read:app-settings-tags",
    "read:app-settings-sample-messages",
    "read:app-settings-segment-delivery-uuid",

    "update:app-settings-accounts",
    "update:app-settings-automatic-chats",
    "update:app-settings-counseling-term",
    "update:app-settings-monitoring-keywords",
    "update:app-settings-tags",
    "update:app-settings-sample-messages",
    "update:case-counselor-i-am-in-charge",
    "update:case-counselor-i-am-not-in-charge",
    "update:case-status",
    "update:case-status-of-open-cases",
    "update:case-attributes-of-resolved-cases",
    "update:case-attributes-of-open-cases",
    "update:case-attributes-of-open-processing-cases-i-am-in-charge",
    "update:case-attributes-of-open-processing-cases-i-am-not-in-charge",

    "create:app-settings-segment-delivery",
    "create:app-settings-monitoring-keywords",
    "create:app-settings-tags",
    "create:app-settings-sample-messages",

    "download:app-reports",
  ],
  [CounselorRole.SUPERVISOR]: [
    // menu
    "read:app",
    "read:app-reports",
    "read:app-reports-consultation-logs",
    "read:app-reports-cases",
    "read:app-reports-summary",
    "read:app-reports-counselees",
    "read:app-chats",
    "read:app-surveys",
    "read:app-wizards",
        "read:app-chatbot",
    "read:app-settings",
    // menu
    "read:counselor_detail",
    // settings
    "read:app-settings-accounts",
    "read:app-settings-automatic-chats",
    "read:app-settings-counseling-term",
    "read:app-settings-segment-delivery",
    "read:app-settings-monitoring-keywords",
    "read:app-settings-tags",
    "read:app-settings-sample-messages",
    "read:app-settings-segment-delivery-uuid",

    "update:app-settings-accounts",
    "update:app-settings-automatic-chats",
    "update:app-settings-counseling-term",
    "update:app-settings-monitoring-keywords",
    "update:app-settings-tags",
    "update:app-settings-sample-messages",
    "update:case-counselor-i-am-in-charge",
    "update:case-counselor-i-am-not-in-charge",
    "update:case-status",
    "update:case-status-of-open-cases",
    "update:case-attributes-of-resolved-cases",
    "update:case-attributes-of-open-processing-cases-i-am-in-charge",
    "update:case-attributes-of-open-processing-cases-i-am-not-in-charge",

    "create:app-settings-segment-delivery",
    "create:app-settings-monitoring-keywords",
    "create:app-settings-tags",
    "create:app-settings-sample-messages",
    "download:app-reports",
  ],
  [CounselorRole.GENERAL]: [
    "read:counselor_detail",
    "read:app",
    "read:app-reports",
    "read:app-settings",
    "read:app-chats",
    // settings
    "read:app-settings-accounts",
    "read:app-settings-automatic-chats",
    "read:app-settings-counseling-term",
    "read:app-settings-segment-delivery",
    "read:app-settings-monitoring-keywords",
    "read:app-settings-tags",
    "read:app-settings-sample-messages",
    "create:app-settings-sample-messages-personal",
    "update:app-settings-sample-messages-personal",
    "update:case-counselor-i-am-in-charge",
    "update:case-status-of-open-cases",
    "update:case-attributes-of-open-processing-cases-i-am-in-charge",
  ],
  [CounselorRole.VIEWER]: ["read:app"],
};

export const usePermissionsStore = defineStore("permissionsStore", {
  state: () => ({}),
  getters: {
    permissionsList: () => {
      const permissions = [] as string[];
      Object.keys(PermissionsMatrix).forEach((role: CounselorRole) => {
        permissions.push(...PermissionsMatrix[role]);
      });
      // remove duplicates
      return Array.from(new Set(permissions));
    },
    permissionsTableRows() {
      return this.permissionsList.map((permission: any) => {
        return {
          permission,
          description: permission,
          admin: PermissionsMatrix[CounselorRole.ADMIN].includes(permission),
          supervisor:
            PermissionsMatrix[CounselorRole.SUPERVISOR].includes(permission),
          general:
            PermissionsMatrix[CounselorRole.GENERAL].includes(permission),
          viewer: PermissionsMatrix[CounselorRole.VIEWER].includes(permission),
        };
      });
    },
  },
  actions: {
    async setPermissionsAndRoles(customerId: string) {
      const authStore = useAuthStore();
      const { user } = storeToRefs(authStore);
      try {
        const userPermissions = usePermissions();
        const userRoles = useRoles();

        const _userRoles = [];
        _userRoles.push(user.value?.role);
        if (user.value?.customers) {
          const customer = user.value.customers.find(
            (obj) => obj.customerId === customerId,
          );
          _userRoles.push(customer?.role);
        }
        userRoles.value = cloneDeep(_userRoles);

        // set permissions
        const _permissions = [] as string[];
        _userRoles.forEach((role: CounselorRole) => {
          if (PermissionsMatrix[role])
            _permissions.push(...PermissionsMatrix[role]);
        });

        userPermissions.value = cloneDeep(_permissions);
      } catch (error: any) {
        console.log("🚀 ~ setPermissionsAndRoles ~ error:", error);
        return false;
      } finally {
      }
    },
  },
});
