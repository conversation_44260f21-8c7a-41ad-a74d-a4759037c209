import _groupBy from "lodash/groupBy";
import type { PaginationResponse, SampleMessage } from "~/types";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppCustomersStore } from "./customers";

export const useSampleMessagesStore = defineStore("sampleMessagesStore", {
  state: () => ({
    sapmleMessages: [] as SampleMessage[],
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalSampleMessagesCount: 0,
    sortConditions: {} as Sort,
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalSampleMessagesCount || 0,
      );
    },
    sampleMessagesForPalette: (state) => (isShared: boolean) => {
      const sharedSampleMessages = state.sapmleMessages.filter(
        (message) =>
          (message.type === "common" && isShared) ||
          (!isShared && message.type === "personal"),
      );

      const groupedSampleMessages = Object.entries(
        _groupBy(sharedSampleMessages, "group"),
      ).map(([key, value]) => {
        return {
          key,
          label: value[0].group,
          icon: "i-heroicons-trash",
          commands: value.map((message) => ({
            id: message.textTemplateId,
            label: message.title,
            icon: "i-mi-message",
            text: message.text,
          })),
        };
      });

      return groupedSampleMessages;
    },
  },
  actions: {
    updateConditions(pagination?: Pagination, sortConditions?: Sort) {
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },
    async fetchSampleMessages() {
      try {
        this.loadings.fetchSampleMessages = true;
        this.errors.fetchSampleMessages = null;
        const router = useRouter();

        const { currentCustomer } = storeToRefs(useAppCustomersStore());
        const {
          data,
        }: {
          data: Ref<PaginationResponse<SampleMessage>>;
        } = await useAppService(
          `/text-templates/counselor/${currentCustomer.value.customerId}`,
          {
            method: "GET",
            params: {
              ...this.sortConditions,
              ...this.pagination,
            },
          },
        );

        this.sapmleMessages = data.value.items;
        this.totalSampleMessagesCount = data.value?.totalCount;
        this.pagination.page = data.value?.page || 1;
        // router.push({
        //   query: {
        //     ...this.pagination,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //   },
        // });
        return true;
      } catch (error) {
        this.errors.fetchSampleMessages = error;
      } finally {
        this.loadings.fetchSampleMessages = false;
      }
    },

    async createSampleMessage(sampleMessage: SampleMessage) {
      const { currentCustomer } = storeToRefs(useAppCustomersStore());
      const appUIStore = useAppUIStore();
      const customerId = currentCustomer.value.customerId;
      try {
        this.loadings.createSampleMessage = true;
        appUIStore.toggleAppLoading(true);
        this.errors.createSampleMessage = null;

        const {
          data,
        }: {
          data: Ref<SampleMessage>;
        } = await useAppService(`/text-templates/text-template`, {
          method: "POST",
          data: {
            ...sampleMessage,
            customerId,
          },
        });

        await this.fetchSampleMessages();
      } catch (error) {
        this.errors.createSampleMessage = error;
        return false;
      } finally {
        this.loadings.createSampleMessage = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async deleteSampleMessage(sampleMessage: SampleMessage) {
      try {
        this.loadings.deleteSampleMessage = {
          [sampleMessage.textTemplateId!]: true,
        };
        this.errors.deleteSampleMessage = null;
        const {
          data,
        }: {
          data: Ref<SampleMessage>;
        } = await useAppService(
          `/text-templates/${sampleMessage.textTemplateId}`,
          {
            method: "DELETE",
          },
        );
        await this.fetchSampleMessages();
      } catch (error) {
        this.errors.deleteSampleMessage = error;
        return false;
      } finally {
        this.loadings.deleteSampleMessage = {
          [sampleMessage.textTemplateId!]: false,
        };
      }
    },

    async updateSampleMessage(sampleMessage: SampleMessage) {
      try {
        this.loadings.updateSampleMessage = {
          [sampleMessage.textTemplateId!]: true,
        };
        this.errors.updateSampleMessage = null;
        const {
          data,
        }: {
          data: Ref<SampleMessage>;
        } = await useAppService(
          `/text-templates/${sampleMessage.textTemplateId}`,
          {
            method: "PATCH",
            data: sampleMessage,
          },
        );

        this.sapmleMessages = this.sapmleMessages.map((item) => {
          if (item.textTemplateId === data.value.textTemplateId) {
            return sampleMessage;
          }
          return item;
        });
        const index = this.sapmleMessages.findIndex(
          (obj) => obj.textTemplateId === sampleMessage.textTemplateId,
        );
        this.sapmleMessages.splice(index, 1, sampleMessage);
        return true;
      } catch (error) {
        this.errors.updateSampleMessage = error;
        return false;
      } finally {
        this.loadings.updateSampleMessage = {
          [sampleMessage.textTemplateId!]: false,
        };
      }
    },
  },
});
