import type { Survey } from "@/types";
import { useAppService } from "~/composables/useAppService";
import SurveyTemplates from "~/stores/app/survey-form-templates.json";
import { useAppUIStore } from "~/stores/app/ui";

export const useSurveysStore = defineStore("surveysStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    surveys: [] as Survey[],
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalSurveysCount: 0,
    surveyTemplates: SurveyTemplates as Survey[],
    surveyDetail: {} as Survey,
    sortConditions: {} as Sort,
    allSurveys: [] as Survey[],
    selectedSurveysIds: [] as string[],
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) *
          (state.pagination.pageRangeDisplayed || 0) +
        1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalSurveysCount || 0,
      );
    },

    surveyTemplatesForPalette(state) {
      return state.surveyTemplates.map((template, index) => {
        return {
          ...template,
          label: template.surveyName,
          id: index,
        };
      });
    },
    getSurveyById: (state) => (surveyId: string) => {
      console.log("🚀 ~ surveyId:", surveyId);
      const survey = state.allSurveys.find((survey) => {
        return survey.surveyId === surveyId;
      });
      console.log("🚀 ~ survey:", survey);

      return survey;
    },
    selectedSurveys: (state) => {
      return state.allSurveys.filter((survey) => {
        return state.selectedSurveysIds.includes(survey.surveyId);
      });
    },
  },
  actions: {
    async fetchSurveys(customerId: string) {
      console.log("🚀 ~ fetchSurveys ~ customerId:", customerId);
      try {
        this.loadings.fetchSurveys = true;
        this.errors.fetchSurveys = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey/surveys", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.pagination,
            customerId,
          },
        });
        this.surveys = data.value?.items;
        this.totalSurveysCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchSurveys ~ e:", e);
        this.errors.fetchSurveys = e;
      } finally {
        this.loadings.fetchSurveys = false;
      }
    },

    async fetchAllSurveys(customerId: string) {
      try {
        this.loadings.fetchAllSurveys = true;
        this.errors.fetchAllSurveys = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey/surveys", {
          method: "GET",
          params: {
            customerId,
          },
        });
        this.allSurveys = data.value?.items;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchAllSurveys ~ e:", e);
        this.errors.fetchAllSurveys = e;
      } finally {
        this.loadings.fetchAllSurveys = false;
      }
    },

    async deleteSurvey(survey: Survey) {
      try {
        this.loadings.deleteSurvey = {
          [survey.surveyId as string]: true,
        };
        this.errors.deleteSurvey = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey/" + survey.surveyId, {
          method: "DELETE",
        });
        this.surveys = this.surveys.filter(
          (obj) => obj.surveyId !== survey.surveyId,
        );
        this.totalSurveysCount = this.totalSurveysCount - 1;
        return true;
      } catch (error) {
        this.errors.deleteSurvey = error;
        return false;
      } finally {
        this.loadings.deleteSurvey = {
          [survey.surveyId as string]: false,
        };
      }
    },

    async createSurvey(survey: Survey) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.createSurvey = true;
        this.errors.createSurvey = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey/surveys", {
          method: "POST",
          data: survey,
        });
        return true;
      } catch (error) {
        this.errors.createSurvey = error;
        return false;
      } finally {
        this.loadings.createSurvey = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async fetchSurvey(surveyId: string) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.fetchSurvey = true;
        this.errors.fetchSurvey = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/survey/" + surveyId, {
          method: "GET",
        });
        this.surveyDetail = data.value;
        return data.value;
      } catch (error) {
        this.errors.fetchSurvey = error;
        return false;
      } finally {
        this.loadings.fetchSurvey = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async updateSurvey(surveyId: string, survey: Survey) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.updateSurvey = true;
        this.errors.updateSurvey = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey/" + surveyId, {
          method: "PATCH",
          data: survey,
        });
        return true;
      } catch (error) {
        this.errors.updateSurvey = error;
        return false;
      } finally {
        this.loadings.updateSurvey = false;
        appUIStore.toggleAppLoading(false);
      }
    },
  },
});
