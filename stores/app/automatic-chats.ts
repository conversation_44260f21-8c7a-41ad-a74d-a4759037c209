import type { CustomerSetting } from "~/types";
import { useAppService } from "~/composables/useAppService";
import type { Customer } from "~/types";
import { useAppUIStore } from "~/stores/app/ui";
export const useAutomaticChatsStore = defineStore("automaticChatsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    settings: {
      followWord: "",
      useFollowWord: false,
      beforeSurveyWord: "",
      useBeforeSurveyWord: false,
      beforeWorkTimeWord: "",
      useBeforeWorkTimeWord: false,
      notStartedWord: "",
      useNotStartedWord: false,
      crowdedWord: "",
      useCrowdedWord: false,
      afterWorkTimeWord: "",
      useAfterWorkTimeWord: false,
      closeDayWord: "",
      useCloseDayWord: false,
      notTextMessageWord: "",
      useNotTextMessageWord: false,
      watchWords: [],
      chatType: "line",
    } as unknown as CustomerSetting,
    activeSettingAutoMessage: {} as SettingsAutoMessage,
    chatType: "line",
  }),

  getters: {},

  actions: {
    async updateSettings(customerId: string) {
      console.log(this.settings);
      try {
        this.loadings.updateSettings = true;
        this.errors.updateSettings = null;
        const {
          data,
        }: {
          data: Ref<Customer>;
        } = await useAppService("/customer/setting/" + customerId, {
          method: "PATCH",
          data: {
            ...this.settings,
          },
        });
        return true;
      } catch (error: any) {
        console.log("🚀 ~ fetchSettings ~ error:", error.response);

        this.errors.updateSettings =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.updateSettings = false;
      }
    },

    async fetchSettings(customerId: string) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.fetchSettings = true;
        this.errors.fetchSettings = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<Customer>;
        } = await useAppService("/customer/" + customerId, {
          method: "GET",
        });
        if (data.value.setting) {
          this.settings = data.value.setting;
        }
        if(data.value?.line?.chatType){
          this.chatType = data.value.line?.chatType;
        }
        return true;
      } catch (error: any) {
        this.errors.fetchSettings =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.fetchSettings = false;
        appUIStore.toggleAppLoading(false);
      }
    },
  },
});
