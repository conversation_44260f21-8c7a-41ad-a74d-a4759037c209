import type { ReportExport } from "@/types";
import { useAppService } from "~/composables/useAppService";
import { ReportExportType } from "~/types/enums";

export const useReportsStore = defineStore("reportsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    reports: [] as ReportExport[],
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalReportsCount: 0,
    sortConditions: {} as Sort,
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalReportsCount || 0,
      );
    },
  },
  actions: {
    updateConditions(pagination?: Pagination, sortConditions?: Sort) {
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },
    async fetchReports() {
      try {
        this.loadings.fetchReports = true;
        this.errors.fetchReports = null;
        const router = useRouter();
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService(`/export/exports`, {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.pagination,
          },
        });
        this.reports = data.value?.items;
        this.totalReportsCount = data.value?.totalCount;
        this.pagination.page = data.value?.page || 1;
        // router.push({
        //   query: {
        //     ...this.pagination,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //   },
        // });
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        this.errors.fetchReports = e;
      } finally {
        this.loadings.fetchReports = false;
      }
    },
    async exportConsultationLogs(
      period: {
        type: string;
        value: {
          from: string | null;
          to: string | null;
        };
      },
      columns: {
        value: string;
        label: string;
        selected: boolean;
        group?: string;
        survey?: any;
        wizard?: any;
      }[],
      exportType: ReportExportType,
      // surveyId?: string,
      targetCaseIds?: string[],
    ) {
      const toast = useToast();
      try {
        this.loadings.exportConsultationLogs = true;
        this.errors.exportConsultationLogs = null;
        const from = new Date(period.value.from ?? new Date());
        const to = new Date(period.value.to ?? new Date());
        const surveyGroup = columns
          .filter((obj) => obj.group === "surveyOutputColumnNames")
          .map(({ value, survey }) => ({
            value,
            surveyId: survey?.surveyId,
            surveyName: survey?.surveyName,
          }));
        // group by surveyId, surveyName, join all value
        const surveys = surveyGroup.reduce((acc: any[], cur: any) => {
          const index = acc.findIndex((obj) => obj.surveyId === cur.surveyId);
          if (index === -1) {
            acc.push({
              surveyId: cur.surveyId,
              surveyName: cur.surveyName,
              outputColumns: [cur.value],
            });
          } else {
            acc[index]?.outputColumns.push(cur.value);
          }
          return acc;
        }, []);

        const wizardGroup = columns
          .filter((obj) => obj.group === "wizardOutputColumnNames")
          .map(({ value, wizard }) => ({
            value,
            wizardId: wizard?.wizardId,
            wizardName: wizard?.wizardName,
          }));
        const wizards = wizardGroup.reduce((acc: any[], cur: any) => {
          const index = acc.findIndex((obj) => obj.wizardId === cur.wizardId);
          if (index === -1) {
            acc.push({
              wizardId: cur.wizardId,
              wizardName: cur.wizardName,
              outputColumns: [cur.value],
            });
          } else {
            acc[index]?.outputColumns.push(cur.value);
          }
          return acc;
        }, []);

        const tagGroup = columns
          .filter((obj) => obj.group === "tagOutputColumnNames")
          .map(({ value }) => value);

        const {
          data,
        }: {
          data: any;
        } = await useAppService(`/export/exports`, {
          method: "POST",
          data: {
            targetCounseleeId: "", // TODO フェーズ1以降でカウンセラーのフィルターを追加
            outputColumnNames: columns
              .filter(
                (obj) =>
                  ![
                    "surveyOutputColumnNames",
                    "wizardOutputColumnNames",
                    "tagOutputColumnNames",
                  ].includes(obj.group ?? ""),
              )
              .map(({ value }) => value),
            exportType,
            startDate: formatDate(from, "YYYY-MM-DD"),
            endDate: formatDate(to, "YYYY-MM-DD"),
            surveys: surveys.length > 0 ? surveys : undefined,
            wizards: wizards.length > 0 ? wizards : undefined,
            targetCaseIds,
            tagOutputColumnNames: tagGroup,
          },
        });
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return true;
      } catch (e: any) {
        console.log("🚀 ~ file: cases.ts:27 ~ exportConsultationLogs ~ e:", e);
        this.errors.exportConsultationLogs =
          e?.response?.data?.message || e.message;
        toast.add({
          id: "exportConsultationLogs",
          title: "エラー",
          description: this.errors.exportConsultationLogs,
          color: "red",
          icon: "i-mdi-alert-outline",
        });
      } finally {
        this.loadings.exportConsultationLogs = false;
      }
    },
  },
});
