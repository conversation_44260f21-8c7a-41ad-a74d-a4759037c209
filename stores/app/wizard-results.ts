import type { WizardR<PERSON>ult, WizardResultsSearchConditions } from "@/types";

export const useAppWizardResultsStore = defineStore("appWizardResultsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    wizardResults: [] as WizardResult[],
    searchConditions: {} as WizardResultsSearchConditions,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalWizardResultsCount: 0,
    sortConditions: {} as Sort,

    wizardResult: {} as WizardResult,
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * state.pagination.pageRangeDisplayed,
        state.totalWizardResultsCount,
      );
    },
  },
  actions: {
    async fetchWizardResults(customerId: string) {
      const router = useRouter();
      try {
        this.loadings.fetchWizardResults = true;
        this.errors.fetchWizardResults = null;

        // router.push({
        //   query: {
        //     ...this.searchConditions,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //     ...this.pagination,
        //     counseleeId: Array.isArray(this.searchConditions.counseleeId)
        //       ? this.searchConditions.counseleeId.join(",")
        //       : this.searchConditions.counseleeId
        //       ? this.searchConditions.counseleeId
        //       : undefined,
        //   },
        // });
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard-result/wizard-results/advanced", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.searchConditions,
            customerId,
            wizardId: Array.isArray(this.searchConditions.wizardId)
              ? this.searchConditions.wizardId.join(",")
              : this.searchConditions.wizardId,
            counseleeId: Array.isArray(this.searchConditions.counseleeId)
              ? undefined
              : this.searchConditions.counseleeId
              ? this.searchConditions.counseleeId
              : undefined,
            ...this.pagination,
          },
        });
        this.wizardResults = data.value?.items;
        this.totalWizardResultsCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchWizardResults ~ e:", e);
        this.errors.fetchWizardResults = e;
      } finally {
        this.loadings.fetchWizardResults = false;
      }
    },

    resetSearchConditions() {
      this.searchConditions = {
        wizardId: [],
        counseleeId: [],
        startDate: "",
        endDate: "",
      };
    },

    updateConditions(
      searchConditions?: WizardResultsSearchConditions,
      pagination?: Pagination,
      sortConditions?: Sort,
    ) {
      if (searchConditions) {
        this.searchConditions = searchConditions;
      }
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },

    async fetchWizardResult(wizardResultId: string) {
      try {
        this.loadings.fetchWizardResult = true;
        this.errors.fetchWizardResult = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard-result/" + wizardResultId, {
          method: "GET",
        });
        this.wizardResult = data.value;
        return this.wizardResult;
      } catch (e) {
        console.log("🚀 ~ fetchWizardResult ~ e:", e);
        this.errors.fetchWizardResult = e;
      } finally {
        this.loadings.fetchWizardResult = false;
      }
    },
  },
});
