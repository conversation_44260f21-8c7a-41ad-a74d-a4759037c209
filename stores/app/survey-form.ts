import type { SurveyFormElement } from "@/types";
import { useAppService } from "~/composables/useAppService";
import { v4 as uuidv4 } from "uuid";
import { cloneDeep } from "lodash";

export const useSurveyFormStore = defineStore("surveyFormStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    formTemplate: [] as SurveyFormElement[],
    activeElement: {} as SurveyFormElement,
  }),

  getters: {},
  actions: {
    initialFormTemplate() {
      this.formTemplate = [
        {
          _id: uuidv4(),
          id: uuidv4(),
          type: "titleAndDescription",
          title: "アンケートのタイトル",
          description: "",
          required: false,
        },
      ];
      this.activeElement = this.formTemplate[0];
    },

    setFormTemplate(formTemplate: SurveyFormElement[]) {
      this.formTemplate = formTemplate;
      this.activeElement = formTemplate[0];
    },

    setActiveElement(element: SurveyFormElement) {
      this.activeElement = element;
    },

    setActiveElementToNull() {
      this.activeElement = {} as SurveyFormElement;
    },

    addElement() {
      const element = {
        _id: uuidv4(),
        id: uuidv4(),
        type: "text",
        title: "無題の質問",
        description: "",
        required: false,
      };

      // push element to the next of the active element
      const index = this.formTemplate.findIndex(
        (obj) => obj._id === this.activeElement._id
      );
      this.formTemplate.splice(index + 1, 0, element);
      this.activeElement = element;
    },

    addTitleAndDescription() {
      const element = {
        _id: uuidv4(),
        id: uuidv4(),
        type: "titleAndDescription",
        title: "アンケートのタイトル",
        description: "",
        required: false,
      };

      // push element to the next of the active element
      const index = this.formTemplate.findIndex(
        (obj) => obj._id === this.activeElement._id
      );
      this.formTemplate.splice(index + 1, 0, element);
      this.activeElement = element;
    },
    deleteActiveElement() {
      const index = this.formTemplate.findIndex(
        (obj) => obj._id === this.activeElement._id
      );
      if (index !== -1) {
        this.formTemplate.splice(index, 1);
      }

      // set active element to the previous element
      if (this.formTemplate.length > 0 && index > 0) {
        this.activeElement = this.formTemplate[index - 1];
      }

      // if the deleted element is the first element, set active element to the next element
      if (this.formTemplate.length > 0 && index === 0) {
        this.activeElement = this.formTemplate[index];
      }
    },

    deleteElement(element: SurveyFormElement) {
      const index = this.formTemplate.findIndex(
        (obj) => obj._id === element._id
      );
      if (index !== -1) {
        this.formTemplate.splice(index, 1);

        // set active element to the previous element
        if (this.formTemplate.length > 0 && index > 0) {
          this.activeElement = this.formTemplate[index - 1];
        }

        // if the deleted element is the first element, set active element to the next element
        if (this.formTemplate.length > 0 && index === 0) {
          this.activeElement = this.formTemplate[index];
        }
      }
    },

    duplicateElement(element: SurveyFormElement): SurveyFormElement {
      const index = this.formTemplate.findIndex(
        (obj) => obj._id === element._id
      );
      if (index !== -1) {
        let newElement = cloneDeep(element);
        newElement._id = uuidv4();
        newElement.id = uuidv4();

        this.formTemplate.splice(index + 1, 0, newElement);

        return newElement;
      }

      return element;
    },
  },
});
