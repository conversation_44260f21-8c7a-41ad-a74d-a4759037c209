import { cloneDeep } from "lodash";
import { storeToRefs } from "pinia";
import type { CounselingTerm } from "~/types";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import { useAppUIStore } from "~/stores/app/ui";
dayjs.extend(isBetween);

import JapaneseHolidays from "japanese-holidays";

interface JapaneseHolidayAttribute {
  key: string;
  highlight: {
    color: string;
    fillMode: string;
  };
  dates: Date[];
  popover: {
    label: string;
  };
}

export const useCounselingTermsStore = defineStore("counselingTermsStore", {
  persist: [
    {
      paths: ["numberOfMonths"],
      storage: localStorage,
    },
  ],
  state: () => ({
    loadings: {
      removeTargetDay: {} as LoadingDynamic,
    } as LoadingDynamic,
    errors: {
      removeTargetDay: {} as ErrorDynamic,
    } as ErrorDynamic,
    days: [0, 1, 2, 3, 4, 5, 6],
    counselingTerms: [] as CounselingTerm[],
    workingDayTimes: [] as CounselingTerm[],
    specialWorkingDayTimes: [] as any[],
    specialWorkingDayTimesFiltered: [] as any[],
    selectedDayWorkingTimes: {} as CounselingTerm,
    selectedDayWorkingTimesSpecial: [] as any[],
    currentCalendarPage: {
      year: dayjs().year(),
      month: dayjs().month() + 1,
    } as any,
    calendarKey: 0,
    newStartOfWorkingTimesYearMonth: {} as any,
    attributes: [] as any[],
    numberOfMonths: 1,
    calendarDayClicked: {} as any,

    todayCounselingTerm: {} as any,
  }),

  getters: {
    calendarEndMonth: (state) => {
      const { year, month } = state.currentCalendarPage;
      const current = dayjs(`${year}-${month}-01`);
      const end = current.add(state.numberOfMonths - 1, "month");
      return {
        year: end.year(),
        month: end.month() + 1,
      };
    },
    specialWorkingDayTimesSorted: (state) => {
      return state.specialWorkingDayTimes
        .filter((obj) => {
          return (
            formatDate(new Date(obj?.date), "YYYYMM") ===
            parseYearMonthToString(
              state.currentCalendarPage.year,
              state.currentCalendarPage.month,
            )
          );
        })
        .sort((a, b) => {
          const aDate = new Date(a.date);
          const bDate = new Date(b.date);
          return aDate.getTime() - bDate.getTime();
        });
    },
  },

  actions: {
    filterWorkingDayTimesByYearMonth(year: number, month: number) {
      this.specialWorkingDayTimesFiltered = this.specialWorkingDayTimes
        .filter((obj) => {
          // check if date is in selected year and month pr next month
          const checkFrom = dayjs(`${year}-${month}-01`);

          const checkTo = dayjs(
            `${this.calendarEndMonth.year}-${this.calendarEndMonth.month}-01`,
          ).endOf("month");
          const date = dayjs(obj.date);
          const isBetween = date.isBetween(checkFrom, checkTo, "day", "[]");
          return isBetween;
        })
        .sort((a, b) => {
          const aDate = new Date(a.date);
          const bDate = new Date(b.date);
          return aDate.getTime() - bDate.getTime();
        });
    },

    async updateWorkingDayTimes() {
      try {
        this.loadings.updateWorkingDayTimes = true;
        this.errors.updateWorkingDayTimes = null;
        // sleep 1s
        await new Promise((resolve) => setTimeout(resolve, 1000));
        return true;
      } catch (error) {
        this.errors.updateWorkingDayTimes = error;
        return false;
      } finally {
        this.loadings.updateWorkingDayTimes = false;
      }
    },

    updateAttribute() {
      console.log("updateAttribute");
      const { currentCalendarPage, workingDayTimes, specialWorkingDayTimes } =
        this;
      const { year, month } = currentCalendarPage;
      const workingDays = {
        key: "workingDays",
        // Object
        dot: "green",
        dates: [] as Date[],
      };
      const regularHolidays = {
        // Object
        key: "regularHolidays",
        dot: "red",
        dates: [] as Date[],
      };
      const specialHolidays = {
        // Object
        key: "specialHolidays",
        highlight: {
          color: "red",
          fillMode: "light",
        },
        dates: [] as Date[],
      };
      const specialWorkingDays = {
        // Object
        key: "specialWorkingDays",
        highlight: {
          color: "green",
          fillMode: "light",
        },
        dates: [] as Date[],
      };

      const japaneseHolidays: JapaneseHolidayAttribute[] = [];

      // for from start of month to end of next month
      const start = new Date(year, month - 1, 1);
      const end = new Date(
        this.calendarEndMonth.year,
        this.calendarEndMonth.month,
        0,
      );

      for (let date = start; date <= end; date.setDate(date.getDate() + 1)) {
        const dayOfWeek = date.getDay();
        // get dayOfweek by number
        // const dayOfWeek = date.getDay();

        // check in selectedDayWorkingTimesSpecial

        const specialWorkingDay = specialWorkingDayTimes.find(
          (obj) => obj.date === formatDate(new Date(date), "YYYY-MM-DD"),
        );
        let _attr;
        if (specialWorkingDay) {
          if (specialWorkingDay.isOpen) {
            specialWorkingDays.dates.push(new Date(date));
          } else {
            specialHolidays.dates.push(new Date(date));
          }
        } else {
          // check in selectedDayWorkingTimes
          const allWorkingDays = workingDayTimes.filter(
            (obj) => obj.day?.key === dayOfWeek && obj.day?.isOpen,
          );
          if (allWorkingDays.length > 0) {
            workingDays.dates.push(new Date(date));
          } else {
            regularHolidays.dates.push(new Date(date));
          }
        }

        // check in japanese holidays
        const japaneseHoliday = JapaneseHolidays.isHoliday(date);
        if (japaneseHoliday) {
          japaneseHolidays.push({
            key: japaneseHoliday,
            bar: {
              style: {
                backgroundColor: "fuchsia",
              },
            },
            dates: [new Date(date)],
            popover: {
              label: japaneseHoliday,
            },
          });
        }
      }

      this.attributes = [
        workingDays,
        regularHolidays,
        specialHolidays,
        specialWorkingDays,
      ].concat(japaneseHolidays);
    },

    async fetchCounselingTerms(customerId: string) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.fetchCounselingTerms = true;
        this.errors.fetchCounselingTerms = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<CounselingTerm[]>;
        } = await useAppService("counseling-term/counseling-terms", {
          method: "GET",
          params: {
            customerId: customerId,
          },
        });
        this.counselingTerms = cloneDeep(data.value);
        this.setWorkingDayTimes(data.value);
        this.setSpecialWorkingDayTimes(data.value);
        return data.value;
      } catch (e) {
        this.errors.fetchCounselingTerms = e;
        return false;
      } finally {
        this.loadings.fetchCounselingTerms = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async fetchTodayCounselingTerm(customerId: string) {
      try {
        const route = useRoute();
        this.loadings.fetchTodayCounselingTerm = true;
        this.errors.fetchTodayCounselingTerm = null;
        const today = dayjs().format("YYYY-MM-DD");
        const {
          data,
        }: {
          data: Ref<CounselingTerm[]>;
        } = await useAppService("counseling-term/date", {
          method: "GET",
          params: {
            customerId: customerId,
            date: today,
          },
        });

        if (route.query.customerId === customerId) {
          this.todayCounselingTerm = cloneDeep(data.value);
          this.loadings.fetchTodayCounselingTerm = false;
          return data.value;
        } else {
          console.log("fetchTodayCounselingTerm old customerId: ", customerId);
        }
      } catch (e) {
        this.errors.fetchTodayCounselingTerm = e;
        this.loadings.fetchTodayCounselingTerm = false;
        return false;
      } finally {
      }
    },

    setWorkingDayTimes(counselingTerms: CounselingTerm[]) {
      this.workingDayTimes = counselingTerms
        ?.filter((obj: any) => obj.day)
        .map((obj) => {
          return {
            ...obj,
            day: {
              ...obj.day,
              openRanges: obj.day.open.map((open: any) => {
                return [
                  {
                    hours: parseTimeToObject(open.startTime).hours,
                    minutes: parseTimeToObject(open.startTime).minutes,
                  },
                  {
                    hours: parseTimeToObject(open.endTime).hours,
                    minutes: parseTimeToObject(open.endTime).minutes,
                  },
                ];
              }),
            },
          };
        });
    },

    setSpecialWorkingDayTimes(counselingTerms: CounselingTerm[]) {
      this.specialWorkingDayTimes = counselingTerms
        ?.filter((obj: any) => obj.target)
        .map((obj) => {
          return {
            ...obj.target,
            counselingTermId: obj.counselingTermId,
          };
        });
    },

    async updateDay(
      customerId: string,
      key: number[],
      isOpen: boolean,
      open: any,
    ) {
      try {
        this.loadings.updateDay = true;
        this.errors.updateDay = null;
        const {
          data,
        }: {
          data: Ref<CounselingTerm[]>;
        } = await useAppService(
          "counseling-term/counseling-terms/" + customerId + "/day",
          {
            method: "POST",
            data: {
              key,
              isOpen,
              open,
            },
          },
        );
        data.value.forEach((obj) => {
          const index = this.workingDayTimes.findIndex(
            (workingDayTime) => workingDayTime.day?.key === obj.day.key,
          );
          obj = {
            ...obj,
            day: {
              ...obj.day,
              openRanges: obj.day.open.map((open: any) => {
                return [
                  {
                    hours: parseTimeToObject(open.startTime).hours,
                    minutes: parseTimeToObject(open.startTime).minutes,
                  },
                  {
                    hours: parseTimeToObject(open.endTime).hours,
                    minutes: parseTimeToObject(open.endTime).minutes,
                  },
                ];
              }),
            },
          };
          if (index !== -1) {
            this.workingDayTimes[index] = obj;
          } else {
            this.workingDayTimes.push(obj);
          }

          const indexMain = this.counselingTerms.findIndex(
            (counselingTerm) => counselingTerm.day?.key === obj.day.key,
          );
          if (indexMain !== -1) {
            this.counselingTerms[indexMain] = obj;
          } else {
            this.counselingTerms.push(obj);
          }
        });
        return true;
      } catch (e) {
        console.log("🚀 ~ e:", e);
        this.errors.updateDay = e;
        return false;
      } finally {
        this.loadings.updateDay = false;
      }
    },

    async updateTargetDay(
      customerId: string,
      date: string[],
      isOpen: boolean,
      term: any,
    ) {
      try {
        this.loadings.updateTargetDay = true;
        this.errors.updateTargetDay = null;
        const {
          data,
        }: {
          data: Ref<CounselingTerm[]>;
        } = await useAppService(
          "counseling-term/counseling-terms/" + customerId + "/target",
          {
            method: "POST",
            data: {
              date,
              isOpen,
              term,
            },
          },
        );
        data.value.forEach((obj) => {
          const index = this.specialWorkingDayTimes.findIndex(
            (specialWorkingDayTime) =>
              specialWorkingDayTime.date === obj.target.date,
          );
          obj = {
            ...obj.target,
            counselingTermId: obj.counselingTermId,
          };
          if (index !== -1) {
            this.specialWorkingDayTimes[index] = obj;
          } else {
            this.specialWorkingDayTimes.push(obj);
          }
        });
        return true;
      } catch (e) {
        this.errors.updateTargetDay = e;
        return false;
      } finally {
        this.loadings.updateTargetDay = false;
      }
    },

    async removeTargetDay(counselingTermId: string) {
      try {
        this.loadings.removeTargetDay[counselingTermId] = true;
        this.errors.removeTargetDay[counselingTermId] = null;
        const {
          data,
        }: {
          data: Ref<CounselingTerm[]>;
        } = await useAppService("counseling-term/" + counselingTermId, {
          method: "DELETE",
        });
        this.specialWorkingDayTimes = this.specialWorkingDayTimes.filter(
          (obj) => obj.counselingTermId !== counselingTermId,
        );
        return data.value;
      } catch (e) {
        console.log("🚀 ~ removeTargetDay ~ e:", e);
        this.errors.removeTargetDay[counselingTermId] = e;
        return false;
      } finally {
        this.loadings.removeTargetDay[counselingTermId] = false;
      }
    },
  },
});
