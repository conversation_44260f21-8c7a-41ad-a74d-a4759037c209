import _groupBy from "lodash/groupBy";
import type { CaseTag, PaginationResponse } from "~/types";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppService } from "~/composables/useAppService";
import { useAppCustomersStore } from "./customers";

export const useTagsStore = defineStore("tagsStore", {
  state: () => ({
    caseTags: [] as CaseTag[],
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalTagsCount: 0,
    sortConditions: {} as Sort,
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalTagsCount || 0,
      );
    },
    caseTagsForPalette: (state) => {
      const { t } = useI18n();
      const groupedCaseTags = Object.entries(
        _groupBy(state.caseTags, "formType"),
      ).map(([key, value]) => {
        return {
          key,
          label: t(key),
          icon: getFormTempalteIcon(key),
          commands: value.map((item) => ({
            id: item.tagId,
            label: item.tagName,
            icon: getFormTempalteIcon(key),
            ...item,
          })),
        };
      });

      return groupedCaseTags;
    },
  },
  actions: {
    updateConditions(pagination?: Pagination, sortConditions?: Sort) {
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },
    async fetchTags() {
      try {
        const { currentCustomer } = useAppCustomersStore();
        this.loadings.fetchTags = true;
        this.errors.fetchTags = null;
        const router = useRouter();

        const {
          data,
        }: {
          data: Ref<PaginationResponse<CaseTag>>;
        } = await useAppService(
          `/tags/tags?customerId=${currentCustomer.customerId}`,
          {
            method: "GET",
            params: {
              ...this.sortConditions,
              ...this.pagination,
            },
          },
        );

        this.caseTags = data.value.items;
        this.totalTagsCount = data.value?.totalCount;
        this.pagination.page = data.value?.page || 1;
        // router.push({
        //   query: {
        //     ...this.pagination,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //   },
        // });
        return true;
      } catch (error) {
        this.errors.fetchTags = error;
        return false;
      } finally {
        this.loadings.fetchTags = false;
      }
    },

    async createCaseTag(caseTag: CaseTag) {
      const appUIStore = useAppUIStore();
      try {
        const { currentCustomer } = useAppCustomersStore();

        this.loadings.createCaseTag = true;
        appUIStore.toggleAppLoading(true);
        this.errors.createCaseTag = null;
        const {
          data,
        }: {
          data: Ref<CaseTag>;
        } = await useAppService(`/tags/tags`, {
          method: "POST",
          data: { ...caseTag, customerId: currentCustomer.customerId },
        });
        await this.fetchTags();
        return true;
      } catch (error) {
        this.errors.createCaseTag = error;
        return false;
      } finally {
        this.loadings.createCaseTag = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async deleteTag(caseTag: CaseTag) {
      try {
        const { currentCustomer } = useAppCustomersStore();
        this.loadings.deleteTag = {
          [caseTag.tagId as string]: true,
        };
        this.errors.deleteTag = null;
        const {
          data,
        }: {
          data: Ref<CaseTag>;
        } = await useAppService(`/tags/${caseTag.tagId}`, {
          method: "DELETE",
          data: { ...caseTag, customerId: currentCustomer.customerId },
        });
        const index = this.caseTags.findIndex(
          (obj) => obj.tagId === caseTag.tagId,
        );
        await this.fetchTags();
        return true;
      } catch (error) {
        this.errors.deleteTag = error;
        return false;
      } finally {
        this.loadings.deleteTag = {
          [caseTag.tagId as string]: false,
        };
      }
    },

    async updateTag(caseTag: CaseTag) {
      try {
        const { currentCustomer } = useAppCustomersStore();
        this.loadings.updateTag = {
          [caseTag.tagId as string]: true,
        };
        this.errors.updateTag = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService(`/tags/${caseTag.tagId}`, {
          method: "PATCH",
          data: { ...caseTag, customerId: currentCustomer.customerId },
        });
        const index = this.caseTags.findIndex(
          (obj) => obj.tagId === caseTag.tagId,
        );
        this.caseTags.splice(index, 1, caseTag);
        return true;
      } catch (error) {
        this.errors.updateTag = error;
        return false;
      } finally {
        this.loadings.updateTag = {
          [caseTag.tagId as string]: false,
        };
      }
    },
  },
});
