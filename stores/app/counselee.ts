import { Gender, AgeDecade } from "~/types/enums.d";

import { useAppService } from "~/composables/useAppService";
import { useCasesStore } from "~/stores/app/cases";

export const useCounseleeStore = defineStore("counseleeStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    allCounselees: [] as any[],
  }),

  getters: {
    allCounseleesForPalette(state) {
      return state.allCounselees.map((counselee) => {
        return {
          ...counselee,
          id: counselee.counseleeId,
          avatar: {
            src: counselee.pictureUrl,
            alt: counselee.fullname,
          },
          label: counselee.fullname,
        };
      });
    },
  },

  actions: {
    async updateGender(counseleeId: string, gender: Gender) {
      try {
        this.loadings.updateGender = true;
        this.errors.updateGender = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselee/" + counseleeId + "/gender", {
          method: "PATCH",
          data: {
            gender,
          },
        });
        const casesStore = useCasesStore();
        casesStore.updateHandlingCase(counseleeId, {
          key: "gender",
          value: gender,
        });

        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateGender ~ e:", e);
        this.errors.updateGender = e;
        return false;
      } finally {
        this.loadings.updateGender = false;
      }
    },

    async updateAgeDecade(counseleeId: string, ageDecade: AgeDecade) {
      try {
        this.loadings.updateAgeDecade = true;
        this.errors.updateAgeDecade = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselee/" + counseleeId + "/age-decade", {
          method: "PATCH",
          data: {
            ageDecade,
          },
        });
        const casesStore = useCasesStore();
        casesStore.updateHandlingCase(counseleeId, {
          key: "ageDecade",
          value: ageDecade,
        });

        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateAgeDecade ~ e:", e);
        this.errors.updateAgeDecade = e;
        return false;
      } finally {
        this.loadings.updateAgeDecade = false;
      }
    },

    async updateName(counseleeId: string, name: string) {
      try {
        this.loadings.updateName = true;
        this.errors.updateName = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselee/" + counseleeId + "/fullname", {
          method: "PATCH",
          data: {
            fullname: name,
          },
        });
        const casesStore = useCasesStore();
        casesStore.updateHandlingCase(counseleeId, {
          key: "counseleeName",
          value: name,
        });

        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateName ~ e:", e);
        this.errors.updateName = e;
        return false;
      } finally {
        this.loadings.updateName = false;
      }
    },

    async fetchAllCounselees(customerId: string) {
      try {
        this.loadings.fetchAllCounselees = true;
        this.errors.fetchAllCounselees = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselee/counselees", {
          method: "GET",
          params: {
            customerId,
          },
        });
        this.allCounselees = data.value;
        return data.value;
      } catch (e) {
        console.log("🚀 ~ fetchAllCounselees ~ e:", e);
        this.errors.fetchAllCounselees = e;
        return false;
      } finally {
        this.loadings.fetchAllCounselees = false;
      }
    },
  },
});
