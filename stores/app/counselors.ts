import _orderBy from "lodash/orderBy";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppService } from "~/composables/useAppService";
import type { AppCounselor } from "~/types";

export const useCounselorsStore = defineStore("counselorsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    counselors: [] as <PERSON><PERSON><PERSON><PERSON><PERSON>lor[],
    showDetailModal: false as boolean,
    detailCounselor: {} as AppCounselor,
  }),

  getters: {
    counselorsForNavigation: (state) => {
      const appUIStore = useAppUIStore();
      const userPermissions = usePermissions();
      return _orderBy(
        state.counselors.filter(counselor => !counselor.isAdmin).map((counselor) => {
          return {
            ...counselor,
            isOnline: counselor.isOnline || false,
            hasCaseCount: counselor.hasCaseCount || 0,
          };
        }),
        ["isOnline", "hasCaseCount"],
        ["desc", "desc"],
      ).map((counselor) => {
        const obj = {
          ...counselor,
          avatar: {
            src: counselor.profileImage,
            alt: counselor.fullName,
            chipColor: counselor.isOnline ? "green" : "gray",
            chipPosition: "bottom-right",
          },
          label: counselor.fullName,
          badge: counselor.hasCaseCount,
          click: () => {},
          disabled: !userPermissions.value.includes("read:counselor_detail"),
        };
        if (appUIStore.isSubNavigationMini) {
          // delete obj.label
          delete obj.badge;
        }
        obj.click = () => {
          if (userPermissions.value.includes("read:counselor_detail")) {
            state.detailCounselor = obj;
            state.showDetailModal = true;
          }
        };
        return obj;
      });
    },

    counselorsForPalette: (state) => {
      return _orderBy(
        state.counselors.map((counselor) => {
          return {
            ...counselor,
            isOnline: counselor.isOnline || false,
            hasCaseCount: counselor.hasCaseCount || 0,
          };
        }),
        ["isOnline", "hasCaseCount"],
        ["desc", "desc"],
      ).map((counselor) => {
        const obj = {
          ...counselor,
          id: counselor.counselorId,
          avatar: {
            src: counselor.profileImage,
            alt: counselor.fullName,
            chipColor: counselor.isOnline ? "green" : "gray",
            chipPosition: "bottom-right",
          },
          label: counselor.fullName,
          shortcuts: counselor.hasCaseCount ? [counselor.hasCaseCount] : [],
        };
        return obj;
      });
    },

    casesOfDetailCounselorByCustomer: (state) => {
      return state.detailCounselor?.cases?.filter(
        (c: any) =>
          state.detailCounselor.customers &&
          c.customerId ===
            state.detailCounselor.customers[
              state.detailCounselor.selectedCustomer || 0
            ].customerId,
      );
    },

    numberOfCasesByCustomer: (state) => (customerId: string) => {
      return state.detailCounselor?.cases?.filter(
        (c: any) => c.customerId === customerId,
      ).length;
    },
  },

  actions: {
    async fetchCounselors(customerId: string, loading = true) {
      try {
        const route = useRoute();
        this.loadings.fetchCounselors = loading;
        this.errors.fetchCounselors = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselor/customer/", {
          method: "GET",
          params: {
            customerId,
          },
        });

        if (route.query.customerId === customerId) {
          this.counselors = data.value;
          this.loadings.fetchCounselors = false;
        } else {
          console.log("fetchCounselors old customerId: ", customerId);
        }
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchCounselors ~ e:", e);
        this.errors.fetchCounselors = e;
        this.loadings.fetchCounselors = false;
      } finally {
      }
    },
    async fetchCounselor(counselorId: string) {
      try {
        this.loadings.fetchCounselor = true;
        this.errors.fetchCounselor = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/counselor/" + counselorId, {
          method: "GET",
        });
        this.detailCounselor = data.value;
        this.detailCounselor.selectedCustomer = 0;
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchCounselor ~ e:", e);
        this.errors.fetchCounselor = e;
        return false;
      } finally {
        this.loadings.fetchCounselor = false;
      }
    },

    setOnline(counselorIds: string[], isOnline: boolean) {
      this.counselors = this.counselors.map((counselor) => {
        if (counselorIds.includes(counselor.counselorId)) {
          counselor.isOnline = isOnline;
        }
        return counselor;
      });
    },

    updateHasCaseCount(counselorId: string, change: number) {
      this.counselors = this.counselors.map((counselor) => {
        if (counselor.counselorId === counselorId) {
          counselor.hasCaseCount = counselor.hasCaseCount
            ? counselor.hasCaseCount + change
            : 1;
        }
        return counselor;
      });
    },
  },
});
