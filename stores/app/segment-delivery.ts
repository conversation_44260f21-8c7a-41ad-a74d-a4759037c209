import { useAppService } from "~/composables/useAppService";
import type { SegmentDelivery, SegmentDeliverySearchConditions } from "~/types";
import dayjs from "dayjs";
export const useSegmentDeliveryStore = defineStore("segmentDeliveryStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    data: [] as any[],
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    searchConditions: {
      includeBeforeStart: true,
      includeInProgress: true,
      includeResolved: true,
      includeError: false,
    } as SegmentDeliverySearchConditions,
    sortConditions: {} as Sort,
    totalSegmentDeliveriesCount: 0,
    segmentDelivery: {
      segmentName: "",
      content: { text: "" },
      filter: {
        startDate: "",
        endDate: "",
        statuses: [],
        ageDecades: [],
        genders: [],
      },
    } as SegmentDelivery,
    targetStatistics: {
      targetsCount: null,
      totalCount: null,
    } as any,
    targetList: [] as any[],
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) *
          (state.pagination.pageRangeDisplayed || 0) +
        1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalSegmentDeliveriesCount || 0,
      );
    },
    isSendable: (state) => {
      return (
        state.segmentDelivery.content.text?.length > 0 &&
        state.targetStatistics.targetsCount > 0
      );
    },
  },
  actions: {
    resetTarget() {
      this.targetStatistics = {
        targetsCount: null,
        totalCount: null,
      };
      this.targetList = [];
    },
    resetFilter() {
      this.segmentDelivery.filter = {
        startDate: "",
        endDate: "",
        statuses: [],
        ageDecades: [],
        genders: [],
      };
    },
    resetSearchConditions() {
      this.searchConditions = {
        startDate: "",
        endDate: "",
        segmentId: "",
        segmentName: "",
        includeBeforeStart: true,
        includeError: false,
        includeInProgress: true,
        includeResolved: true,
      };
    },
    selectSurvey(survey: any) {
      this.segmentDelivery.survey = {
        surveyId: survey?.surveyId,
        surveyName: survey?.surveyName,
      };
    },

    updateConditions(
      searchConditions?: SegmentDeliverySearchConditions,
      pagination?: Pagination,
      sortConditions?: Sort,
    ) {
      if (searchConditions) {
        this.searchConditions = searchConditions;
      }
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },

    async fetchData() {
      const router = useRouter();
      try {
        this.loadings.fetchData = true;
        this.errors.fetchData = null;
        // router.push({
        //   query: {
        //     ...this.searchConditions,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //     ...this.pagination,
        //     includeBeforeStart:
        //       this.searchConditions.includeBeforeStart?.toString(),
        //     includeInProgress:
        //       this.searchConditions.includeInProgress?.toString(),
        //     includeResolved: this.searchConditions.includeResolved?.toString(),
        //     includeError: this.searchConditions.includeError?.toString(),
        //   },
        // });
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/segment/segments/advanced", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.searchConditions,
            ...this.pagination,
          },
        });
        this.data = data.value?.items;
        this.totalSegmentDeliveriesCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        this.errors.fetchData = e;
      } finally {
        this.loadings.fetchData = false;
      }
    },

    async fetchTargets() {
      try {
        this.loadings.fetchTargets = true;
        this.errors.fetchTargets = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/segment/counselees", {
          method: "GET",
          params: {
            ...this.segmentDelivery.filter,
            statuses: this.segmentDelivery.filter.statuses?.join(","),
            ageDecades: this.segmentDelivery.filter.ageDecades?.join(","),
            genders: this.segmentDelivery.filter.genders?.join(","),
          },
        });
        this.targetList = data.value?.items;
        this.targetStatistics = {
          targetsCount: data.value?.targetTotalCount,
          totalCount: data.value?.totalCount,
        };
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        this.errors.fetchTargets = e;
      } finally {
        this.loadings.fetchTargets = false;
      }
    },

    async createSegmentDelivery() {
      try {
        this.loadings.createSegmentDelivery = true;
        this.errors.createSegmentDelivery = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/segment/segments", {
          method: "POST",
          data: this.segmentDelivery,
        });
        this.resetTarget();
        this.resetFilter();
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        this.errors.createSegmentDelivery = e;
      } finally {
        this.loadings.createSegmentDelivery = false;
      }
    },
  },
});
