import { CaseStatus, CaseRisk, Gender, AgeDecade } from "~/types/enums.d";
import type {
  CaseStatistics,
  PaginationResponse,
  CaseSearchConditions,
  AppCounselor,
  Case,
  CaseChatMessage,
} from "~/types";
import { useChatsStore } from "~/stores/app/chats";
import { useAppService } from "~/composables/useAppService";
import { useAuthStore } from "~/stores/auth";
import { storeToRefs } from "pinia";
import { cloneDeep, orderBy } from "lodash";
import { useAppUIStore } from "~/stores/app/ui";
import { useAppCustomersStore } from "~/stores/app/customers";
import { v4 as uuidv4 } from "uuid";

export const useCasesStore = defineStore("casesStore", {
  persist: [
    {
      paths: ["searchConditions"],
      storage: window.localStorage,
    },
  ],
  state: () => ({
    loadings: {
      sendMessage: {} as LoadingDynamic,
    } as LoadingDynamic,
    errors: {
      sendMessage: {} as ErrorDynamic,
    } as ErrorDynamic,
    cases: [] as Case[],
    caseDetail: {} as Case,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalCasesCount: 0,
    isOpenCaseDetail: false,
    caseStatistics: {} as CaseStatistics,
    updateAssigneeLoading: {} as LoadingDynamic,
    updateStatusLoading: {} as LoadingDynamic,
    searchConditions: {
      counselorInChargeId: [],
      caseStatus: [
        CaseStatus.BEFORE_START,
        CaseStatus.OPEN,
        CaseStatus.IN_PROGRESS,
      ],
    } as unknown as CaseSearchConditions,
    sortConditions: {} as Sort,
    handlingCases: [] as Case[],
    handlingCasesTotalCount: 0,
    readCaseId: "" as string,

    casesSelection: {
      selected: [] as string[],
      cases: [] as Case[],
      pagination: {
        pageRangeDisplayed: 10,
        page: 1,
      } as Pagination,
      totalCasesCount: 0,
      searchConditions: {
        counselorInChargeId: [],
        caseStatus: [
          CaseStatus.BEFORE_START,
          CaseStatus.OPEN,
          CaseStatus.IN_PROGRESS,
        ],
      } as unknown as CaseSearchConditions,
      sortConditions: {} as Sort,
    },
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) *
          (state.pagination.pageRangeDisplayed || 0) +
        1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalCasesCount || 0,
      );
    },

    pageFromCasesSelection: (state) => {
      return (
        (state.casesSelection.pagination.page - 1) *
          (state.casesSelection.pagination.pageRangeDisplayed || 0) +
        1
      );
    },

    pageToCasesSelection: (state) => {
      return Math.min(
        state.casesSelection.pagination.page *
          (state.casesSelection.pagination.pageRangeDisplayed || 0),
        state.casesSelection.totalCasesCount || 0,
      );
    },

    handlingCasesForNavigation: (state) => {
      const appUIStore = useAppUIStore();
      const casesStore = useCasesStore();
      const customersStore = useAppCustomersStore();
      const router = useRouter();
      return orderBy(state.handlingCases, [], []).map((_case) => {
        const obj = {
          ..._case,
          avatar: {
            src: _case.counseleeImage,
            alt: _case.counseleeName,
          },
          label: _case.counseleeName,
          badge: `${_case.count}回目` || undefined,
          active: _case.caseId === state.caseDetail?.caseId,
          click: () => {
            state.caseDetail = _case;
            router.push({
              query: {
                caseId: _case.caseId,
                customerId: customersStore.currentCustomer.customerId,
              },
            });
            casesStore.readMessage(_case.caseId);
            customersStore.readMessage(
              customersStore.currentCustomer.customerId,
              _case.caseId,
            );
          },
        };
        if (appUIStore.isSubNavigationMini) {
          // delete obj.label
          delete obj.badge;
        }
        return obj;
      });
    },
  },

  actions: {
    async fetchCases(customerId: string, loading = true) {
      const toast = useToast();
      const router = useRouter();
      try {
        const route = useRoute();
        this.loadings.fetchCases = loading;
        this.errors.fetchCases = null;
        if (loading) {
          this.cases = [];
          this.totalCasesCount = 0;
        }
        const {
          data,
        }: {
          data: Ref<PaginationResponse<any>>;
        } = await useAppService("/case/cases/advanced", {
          method: "GET",
          params: {
            ...this.searchConditions,
            ...this.sortConditions,
            ...this.pagination,
            customerId,
            caseStatus: this.searchConditions.caseStatus.length
              ? this.searchConditions.caseStatus?.join(",")
              : undefined,
            counselorInChargeId:
              this.searchConditions.counselorInChargeId?.join(","),
          },
        });
        // router.push({
        //   query: {
        //     ...this.searchConditions,
        //     ...this.pagination,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //   },
        // });

        //update case detail if it's in the list (for auto refresh)
        if (!loading) {
          // this.cases = this.cases.map((item) => {
          //   const newItem =
          //     data.value.items.find((obj) => obj.caseId === item.caseId) ||
          //     item;
          //   return {
          //     ...item,
          //     chat: newItem.chat,
          //   };
          // });
          // update chat for case detail
          if (this.caseDetail.caseId) {
            this.syncChat(this.caseDetail.caseId);
            // const newData = data.value.items.find(
            //   (obj) => obj.caseId === this.caseDetail.caseId,
            // );
            // if (newData) {
            //   this.caseDetail.chat = newData.chat;
            // }
          }
        } else {
          // check if customerId is the same as current customer in route
          if (route.query.customerId === customerId) {
            this.cases = data.value.items;
            this.totalCasesCount = data.value.totalCount;
            this.loadings.fetchCases = false;
          } else {
            console.log("old customerId: ", customerId);
          }
        }
      } catch (e: any) {
        // exclude details.code = “EMFILE”
        if (e?.details?.code !== "EMFILE") {
          toast.add({
            id: "fetchCasesError",
            title: "エラー",
            description: e?.response?.data?.message || e.message,
            icon: "i-heroicons-exclamation-circle",
            color: "red",
          });
        }

        this.errors.fetchCases = e;
        this.loadings.fetchCases = false;
      } finally {
      }
    },

    async syncChat(caseId: string) {
      try {
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/case/" + caseId, {
          method: "GET",
        });
        // update chat for case detail
        if (this.caseDetail?.caseId === caseId) {
          this.caseDetail.chat = data.value?.chat;
        }
      } catch (e: any) {
      } finally {
      }
    },

    async resetSearchConditions() {
      this.searchConditions = {
        counselorInChargeId: [],
        caseStatus: [],
      } as unknown as CaseSearchConditions;
    },
    updateConditions(
      searchConditions?: CaseSearchConditions,
      pagination?: Pagination,
      sortConditions?: Sort,
    ) {
      if (searchConditions) {
        this.searchConditions = searchConditions;
      }
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },
    async fetchCaseStatistics(customerId: string, loading = true) {
      try {
        const route = useRoute();
        this.loadings.fetchCaseStatistics = loading;
        this.errors.fetchCaseStatistics = null;
        const {
          data,
        }: {
          data: Ref<CaseStatistics>;
        } = await useAppService("/customer/statistics/", {
          method: "GET",
          params: {
            customerId,
          },
        });
        if (route.query.customerId === customerId) {
          this.caseStatistics = data.value;
          this.loadings.fetchCaseStatistics = false;
        } else {
          console.log("fetchCaseStatistics old customerId: ", customerId);
        }

        return data.value;
      } catch (e) {
        this.errors.fetchCaseStatistics = e;
        this.loadings.fetchCaseStatistics = false;
        return false;
      } finally {
      }
    },

    async updateCounselorAndStatus(
      _case: Case,
      counselor: AppCounselor,
      nextStatus: CaseStatus,
    ) {
      try {
        this.updateAssigneeLoading[_case.caseId] = true;
        this.errors.updateCounselorInCharge = null;
        this.updateStatusLoading[_case.caseId] = true;
        this.errors.updateStatus = null;

        const {
          data,
        }: {
          data: Ref<CaseStatistics>;
        } = await useAppService("/case/" + _case.caseId + "/status-counselor", {
          method: "PATCH",
          data: {
            counselorInChargeId: counselor.counselorId,
            status: nextStatus,
          },
        });
        this.cases.map((item) => {
          if (item.caseId === _case.caseId && counselor) {
            item.counselorInChargeId = counselor.counselorId;
            item.counselorInChargeName = counselor.fullName;
            item.counselorInChargeImage = counselor.profileImage;
            item.status = nextStatus;
          }
          return item;
        });

        // update case detail
        if (this.caseDetail.caseId === _case.caseId) {
          this.caseDetail.counselorInChargeId = counselor.counselorId;
          this.caseDetail.counselorInChargeName = counselor.fullName;
          this.caseDetail.counselorInChargeImage = counselor.profileImage;
          this.caseDetail.status = nextStatus;
        }
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchCases ~ e:", e);
        this.errors.updateCounselorInCharge = e;
        this.errors.updateStatus = e;
        return false;
      } finally {
        this.updateAssigneeLoading[_case.caseId] = false;
        this.updateStatusLoading[_case.caseId] = false;
      }
    },

    async updateCounselorInCharge(_case: Case, counselor: AppCounselor) {
      try {
        this.updateAssigneeLoading[_case.caseId] = true;
        this.errors.updateCounselorInCharge = null;

        const {
          data,
        }: {
          data: Ref<CaseStatistics>;
        } = await useAppService(
          "/case/" + _case.caseId + "/case-counselor-in-charge-id",
          {
            method: "PATCH",
            data: {
              counselorInChargeId: counselor.counselorId,
            },
          },
        );
        this.cases.map((item) => {
          if (item.caseId === _case.caseId && counselor) {
            item.counselorInChargeId = counselor.counselorId;
            item.counselorInChargeName = counselor.fullName;
            item.counselorInChargeImage = counselor.profileImage;
          }
          return item;
        });

        // update case detail
        if (this.caseDetail.caseId === _case.caseId) {
          this.caseDetail.counselorInChargeId = counselor.counselorId;
          this.caseDetail.counselorInChargeName = counselor.fullName;
          this.caseDetail.counselorInChargeImage = counselor.profileImage;
        }
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchCases ~ e:", e);
        this.errors.updateCounselorInCharge = e;
        return false;
      } finally {
        this.updateAssigneeLoading[_case.caseId] = false;
      }
    },

    async updateStatus(_case: Case, nextStatus?: CaseStatus) {
      const toast = useToast();
      try {
        this.updateStatusLoading[_case.caseId] = true;
        this.errors.updateStatus = null;

        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/case/" + _case.caseId + "/status", {
          method: "PATCH",
          data: {
            status: nextStatus,
          },
        });
        this.cases.map((item) => {
          if (item.caseId === _case.caseId && nextStatus) {
            item.status = nextStatus;
          }
          return item;
        });
        // update case detail
        if (this.caseDetail.caseId === _case.caseId) {
          this.caseDetail.status = nextStatus || this.caseDetail.status;
        }
        return true;
      } catch (e: any) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        toast.add({
          title: "エラー",
          description: e?.response?.data?.message || e.message,
          icon: "i-heroicons-exclamation-circle",
          color: "red",
        });
        this.errors.updateStatus = e;
        return false;
      } finally {
        this.updateStatusLoading[_case.caseId] = false;
      }
    },

    chatWithUser(_case: Case) {
      try {
        const chatsStore = useChatsStore();
        this.loadings.chatWithUser = true;
        this.errors.chatWithUser = null;
        chatsStore.addChattingUser({
          userId: _case.userId,
          userName: _case.clientName,
          userAvatar: _case.userAvatar,
        });
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:", e);
        this.errors.chatWithUser = e;
      } finally {
        this.loadings.chatWithUser = false;
      }
    },

    async fetchCaseDetail(caseId: string) {
      const customersStore = useAppCustomersStore();
      try {
        this.loadings.fetchCaseDetail = true;
        this.errors.fetchCaseDetail = null;
        // sleep 1s
        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/case/" + caseId, {
          method: "GET",
        });
        this.caseDetail = data.value;
        customersStore.readMessage(
          customersStore.currentCustomer.customerId,
          caseId,
        );
      } catch (e) {
        this.errors.fetchCaseDetail = e;
      } finally {
        this.loadings.fetchCaseDetail = false;
      }
    },

    async sendMessage(caseId: string, content: { text: string }, survey: any) {
      const authStore = useAuthStore();
      const { user } = storeToRefs(authStore);
      const chatId = uuidv4();
      try {
        this.loadings.sendMessage[caseId] = true;
        this.errors.sendMessage[caseId] = null;
        let requestData = {
          senderId: user.value?.counselorId,
          senderName: user.value?.fullName,
        } as any;

        if (survey) {
          requestData.survey = {
            surveyId: survey.surveyId,
            surveyName: survey.surveyName,
          };
        }

        if (content.text) {
          requestData.content = content;
        }
        if (!this.caseDetail?.chat) {
          this.caseDetail.chat = [];
        }

        this.caseDetail.chat.push({
          ...requestData,
          chatId,
          senderAvatar: user.value?.profileImage,
          sender: "counselor",
          isSending: true,
        });
        const {
          data,
        }: {
          data: Ref<CaseChatMessage>;
        } = await useAppService("/case/" + caseId + "/chat", {
          method: "PATCH",
          data: requestData,
        });

        const index = this.caseDetail.chat.findIndex(
          (message) => message.isSending && message.chatId === chatId,
        );
        if (index !== -1) {
          this.caseDetail.chat[index] = data.value;
        }
        return data.value;
      } catch (e: any) {
        const errorMessage = e?.response?.data?.message || e.message;
        this.errors.sendMessage[caseId] = errorMessage;
        const index = this.caseDetail.chat?.findIndex(
          (msg: CaseChatMessage) => msg.chatId === chatId,
        );
        if (index !== -1 && this.caseDetail.chat) {
          this.caseDetail.chat[index].error = errorMessage;
          this.caseDetail.chat[index].isSending = false;
        }
        return false;
      } finally {
        this.loadings.sendMessage[caseId] = false;
      }
    },

    receiveMessage(caseId: string, message: CaseChatMessage) {
      this.cases.map((item) => {
        if (item.caseId === caseId) {
          // check if chat already has this message
          const isExist = item.chat?.find(
            (chat) => chat.chatId === message.chatId,
          );
          if (!isExist) {
            item.chat?.push(message);
          }
        }
        return item;
      });

      if (this.caseDetail.caseId === caseId) {
        // check if chat already has this message
        const isExist = this.caseDetail.chat?.find(
          (chat: any) => chat.chatId === message.chatId,
        );
        if (!isExist) {
          this.caseDetail.chat?.push(message);
        }
      }
    },

    async updateConveyed(counseleeId: string, conveyed: string) {
      try {
        this.loadings.updateConveyed = true;
        this.errors.updateConveyed = null;
        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/counselee/" + counseleeId + "/conveyed", {
          method: "PATCH",
          data: {
            conveyed,
          },
        });
        this.cases.map((item) => {
          if (item.counseleeId === counseleeId) {
            item.conveyed = conveyed;
          }
          return item;
        });

        this.updateHandlingCase(counseleeId, {
          key: "conveyed",
          value: conveyed,
        });

        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateConveyed ~ e:", e);
        this.errors.updateConveyed = e;
        return false;
      } finally {
        this.loadings.updateConveyed = false;
      }
    },

    async updateCaseRisk(caseId: string, risk: CaseRisk) {
      try {
        this.loadings.updateCaseRisk = true;
        this.errors.updateCaseRisk = null;
        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/case/" + caseId + "/risk", {
          method: "PATCH",
          data: {
            risk,
          },
        });
        this.cases.map((item) => {
          if (item.caseId === caseId) {
            item.risk = risk;
          }
          return item;
        });
        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateCaseRisk ~ e:", e);
        this.errors.updateCaseRisk = e;
        return false;
      } finally {
        this.loadings.updateCaseRisk = false;
      }
    },

    async updateCaseMemos(caseId: string, memos: string[]) {
      try {
        this.loadings.updateCaseMemos = true;
        this.errors.updateCaseMemos = null;
        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/case/" + caseId + "/memos", {
          method: "PATCH",
          data: {
            memos: memos.filter((item) => item),
          },
        });
        this.cases.map((item) => {
          if (item.caseId === caseId) {
            item.memos = memos;
          }
          return item;
        });
        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateCaseMemos ~ e:", e);
        this.errors.updateCaseMemos = e;
        return false;
      } finally {
        this.loadings.updateCaseMemos = false;
      }
    },

    updateCaseTagValue(caseId: string, tagId: string, value: string) {
      this.cases.map((item) => {
        if (item.caseId === caseId) {
          item.selectedTags?.map((tag) => {
            if (tag.tagId === tagId) {
              tag.value = value;
            }
            return tag;
          });
        }
        return item;
      });
    },

    async updateCaseTags(caseId: string, tags: any[]) {
      let selectedTags = cloneDeep(tags);
      try {
        this.loadings.updateCaseTags = true;
        this.errors.updateCaseTags = null;
        const {
          data,
        }: {
          data: Ref<Case>;
        } = await useAppService("/case/" + caseId + "/selected-tags", {
          method: "PATCH",
          data: {
            selectedTags: selectedTags.map((item) => {
              return {
                tagId: item.tagId,
                value: item.value,
                formType: item.formType,
                label: item.label,
                tagName: item.tagName,
                options: item.options,
              };
            }),
          },
        });
        this.cases.map((item) => {
          if (item.caseId === caseId) {
            item.selectedTags = data.value.selectedTags;
          }
          return item;
        });
        return data.value;
      } catch (e) {
        console.log("🚀 ~ updateCaseTags ~ e:", e);
        this.errors.updateCaseTags = e;
        return false;
      } finally {
        this.loadings.updateCaseTags = false;
      }
    },

    async fetchHandlingCases(
      customerId: string,
      setDefaultCase = false,
      loading = true,
    ) {
      const router = useRouter();
      try {
        this.loadings.fetchHandlingCases = loading;
        this.errors.fetchHandlingCases = null;
        const {
          data,
        }: {
          data: Ref<PaginationResponse<any>>;
        } = await useAppService("/case/cases/advanced", {
          method: "GET",
          params: {
            onlyCaseInCharge: true,
            caseStatus: "in_progress,waiting",
            customerId,
          },
        });
        this.handlingCases = data.value.items;
        this.handlingCasesTotalCount = data.value.totalCount;
        if (setDefaultCase && data.value.items.length) {
          const readCase = data.value.items.find(
            (obj) => obj.caseId === this.readCaseId,
          );
          router.push({
            query: {
              caseId: readCase?.caseId || data.value.items[0].caseId,
              customerId,
            },
          });
          this.caseDetail = readCase || data.value.items[0];
        }

        if (this.caseDetail.caseId) {
          await this.syncChat(this.caseDetail.caseId);
          // const newData = data.value.items.find(
          //   (obj) => obj.caseId === this.caseDetail.caseId,
          // );
          // if (newData) {
          //   this.caseDetail.chat = newData.chat;
          // }
        }
        return data.value.items[0];
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchHandlingCases ~ e:", e);
        this.errors.fetchHandlingCases = e;
        return false;
      } finally {
        this.loadings.fetchHandlingCases = false;
      }
    },

    updateHandlingCase(
      counseleeId: string,
      update: {
        key: "gender" | "ageDecade" | "conveyed" | "counseleeName";
        value: Gender | AgeDecade | string;
      },
    ) {
      this.handlingCases.map((item) => {
        if (item.counseleeId === counseleeId) {
          item[update.key] = update.value;
        }
        return item;
      });

      this.cases.map((item) => {
        if (item.counseleeId === counseleeId) {
          item[update.key] = update.value;
        }
        return item;
      });

      if (this.caseDetail.counseleeId === counseleeId) {
        this.caseDetail[update.key] = update.value;
      }
    },

    async fetchCaseChatHistory(caseId: string) {
      try {
        this.loadings.fetchCaseChatHistory = true;
        this.errors.fetchCaseChatHistory = null;
        const {
          data,
        }: {
          data: Ref<Case[]>;
        } = await useAppService("/case/" + caseId + "/chat-history", {
          method: "GET",
          params: {
            caseId,
          },
        });
        this.handlingCases.map((item) => {
          if (item.caseId === caseId) {
            item.histories = data.value;
          }
          return item;
        });
        this.caseDetail.histories = data.value;
        return true;
      } catch (e) {
        console.log("🚀 ~ file: cases.ts:27 ~ fetchCaseChatHistory ~ e:", e);
        this.errors.fetchCaseChatHistory = e;
        return false;
      } finally {
        this.loadings.fetchCaseChatHistory = false;
      }
    },

    updateUnReadMessages(message: CaseChatMessage) {
      this.handlingCases.map((item) => {
        if (item.caseId === message.caseId) {
          if (!item.unReadMessages) {
            item.unReadMessages = [];
          }

          item.unReadMessages.unshift(message);
        }
        return item;
      });
    },

    readMessage(caseId: string) {
      this.handlingCases.map((item) => {
        if (item.caseId === caseId) {
          item.unReadMessages = [];
        }
        return item;
      });
    },

    async fetchCasesSelection(customerId: string) {
      const toast = useToast();
      try {
        this.loadings.fetchCasesSelection = true;
        this.errors.fetchCasesSelection = null;
        this.casesSelection.cases = [];
        this.casesSelection.totalCasesCount = 0;
        const {
          data,
        }: {
          data: Ref<PaginationResponse<any>>;
        } = await useAppService("/case/cases/advanced", {
          method: "GET",
          params: {
            ...this.casesSelection.searchConditions,
            ...this.casesSelection.sortConditions,
            ...this.casesSelection.pagination,
            customerId,
            caseStatus: this.casesSelection.searchConditions.caseStatus.length
              ? this.casesSelection.searchConditions.caseStatus?.join(",")
              : undefined,
            counselorInChargeId:
              this.casesSelection.searchConditions.counselorInChargeId?.join(
                ",",
              ),
          },
        });
        this.casesSelection.cases = data.value.items;
        this.casesSelection.totalCasesCount = data.value.totalCount;
      } catch (e: any) {
        toast.add({
          id: "fetchCasesSelectionError",
          title: "エラー",
          description: e?.response?.data?.message || e.message,
          icon: "i-heroicons-exclamation-circle",
          color: "red",
        });
        this.errors.fetchCasesSelection = e;
      } finally {
        this.loadings.fetchCasesSelection = false;
      }
    },

    async resetSearchConditionsCasesSelection() {
      this.casesSelection.searchConditions = {
        counselorInChargeId: [],
        caseStatus: [],
      } as unknown as CaseSearchConditions;
    },
  },
});
