import type { CommonResponse } from "~/types";
import { useAppCustomersStore } from "~/stores/app/customers";
import { storeToRefs } from "pinia";
import { cloneDeep } from "lodash";
export const useMonitoringKeywordsStore = defineStore(
  "monitoringKeywordsStore",
  {
    state: () => ({
      loadings: {} as LoadingDynamic,
      errors: {} as ErrorDynamic,
    }),

    getters: {
      monitoringKeywords: () => {
        const { currentCustomer } = storeToRefs(useAppCustomersStore());
        const monitoringKeywords =
          currentCustomer.value?.setting?.watchWords || [];
        return monitoringKeywords;
      },
    },

    actions: {
      async updateMonitoringKeywords(watchWords: string[]) {
        try {
          this.loadings.updateMonitoringKeywords = true;
          this.errors.updateMonitoringKeywords = null;

          const store = useAppCustomersStore();
          const { currentCustomer } = storeToRefs(store);
          const customer = currentCustomer.value;

          const {
            data,
          }: {
            data: Ref<CommonResponse>;
          } = await useAppService(
            `/customer/watch-keyward/${customer.customerId}`,
            {
              method: "PATCH",
              data: {
                watchWords,
              },
            },
          );

          const cloneCustomer = cloneDeep(customer);
          cloneCustomer.setting!.watchWords = watchWords;

          store.setCustomer(cloneCustomer);

          return true;
        } catch (error) {
          this.errors.updateMonitoringKeywords = error;
          return false;
        } finally {
          this.loadings.updateMonitoringKeywords = false;
        }
      },
    },
  },
);
