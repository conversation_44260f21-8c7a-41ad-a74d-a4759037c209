import type { Customer } from "~/types";

export const useAppUIStore = defineStore("appUI", {
  persist: {
    paths: [
      "projectColors",
      "isSubNavigationMini",
      "showAppBottomBar",
      "isHideAppNavigation",
      "isHideAppSubNavigation",
    ],
  },
  state: () => ({
    isSubNavigationMini: false,
    showAppBottomBar: false,
    isHideAppNavigation: false,
    isHideAppSubNavigation: false,
    projectColors: {} as ProjectColors,
    appLoading: false,
  }),

  actions: {
    updateTheme(customer: Customer) {
      const appConfig = useAppConfig();
      const colorMode = useColorMode();

      appConfig.ui.primary = customer.theme || "primary";
      // colorMode.preference = "light";
    },

    toggleAppLoading(isLoading: boolean) {
      this.appLoading = isLoading;
    },
  },
});
