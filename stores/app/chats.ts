import _orderBy from 'lodash/orderBy'
import type { ChatMessage } from '@/types'
import { useAppUIStore } from '~/stores/app/ui'

export const useChatsStore = defineStore('chatsStore', {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    chattingUsers: [] as any[], // TODO: Define the type of chattingUsers
    messages: [] as ChatMessage[],
    currentChattingUser: null as any // TODO: Define the type of currentChattingUser
  }),

  getters: {
    chattingUsersForNavigation: (state) => {
      const appUIStore = useAppUIStore()
      const router = useRouter()
      return _orderBy(state.chattingUsers, ['online'], ['desc']).map((user) => {
        const obj = {
          ...user,
          avatar: {
            src: user.userAvatar,
            alt: user.userName,
            chipColor: 'green',
            chipPosition: 'bottom-right'
          },
          label: user.userName,
          badge: user.notSeenMessages,
          active: user.userId === state.currentChattingUser?.userId,
          click: () => {
            state.currentChattingUser = user
            router.replace({
              path: '/app/chats/' + user.userId
            })
          }
        }
        if (appUIStore.isSubNavigationMini) {
          // delete obj.label
          delete obj.badge
        }
        return obj
      })
    },

    currentMessages: (state) => {
      return (
        state.chattingUsers.find(
          obj => obj.userId === state.currentChattingUser?.userId
        )?.messages || []
      )
    },

    currentOldMessages: (state) => {
      return (
        state.chattingUsers.find(
          obj => obj.userId === state.currentChattingUser?.userId
        )?.oldMessages || []
      )
    },

    currentHistories: (state) => {
      return (
        state.chattingUsers.find(
          obj => obj.userId === state.currentChattingUser?.userId
        )?.histories || []
      )
    },

    chattingUser: (state) => {
      return state.chattingUsers.find(
        obj => obj.userId === state.currentChattingUser?.userId
      )
    },

    currentHistory: (state) => {
      return state.chattingUsers.find(
        obj => obj.userId === state.currentChattingUser?.userId
      )?.currentHistory
    }
  },
  actions: {
    sendMessage (message: ChatMessage) {
      const chattingUser = this.chattingUsers.find(
        item => item.userId === this.currentChattingUser?.userId
      )

      if (chattingUser) {
        chattingUser.messages.push(message)
      }
    },

    clearMessages () {
      this.messages = []
    },

    async addChattingUser (chattingUser: any) {
      try {
        this.loadings.addChattingUser = true
        this.errors.addChattingUser = null
        const check = this.chattingUsers.find(
          item => item.userId === chattingUser.userId
        )
        if (!check) {
          this.chattingUsers.unshift(chattingUser)
          // sleep 1s
          await this.fetchChattingMessages(chattingUser.userId)
        }

        this.currentChattingUser = chattingUser
      } catch (e) {
        console.log('🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:', e)
        this.errors.addChattingUser = e
      } finally {
        this.loadings.addChattingUser = false
      }
    },

    async fetchChattingMessages (userId: string) {
      try {
        this.loadings.fetchChattingMessages = true
        this.errors.fetchChattingMessages = null
        // sleep 1s
        await new Promise(resolve => setTimeout(resolve, 1000))
        const chattingUser = this.chattingUsers.find(
          item => item.userId === userId
        )
        if (chattingUser) {
          chattingUser.messages = [
            {
              isUserMessage: false,
              message: 'こんにちは！ お元気ですか？'
            },
            {
              isUserMessage: true,
              message: `こんにちは、${chattingUser.userName}さま！ 今日はどのようにお手伝いしましょうか？`
            }
          ]
          chattingUser.notSeenMessages = 0
        }
      } catch (e) {
        console.log('🚀 ~ file: cases.ts:27 ~ updateStatus ~ e:', e)
        this.errors.fetchChattingMessages = e
      } finally {
        this.loadings.fetchChattingMessages = false
      }
    },

    async fetchOldChatMessages (userId: string, history: any) {
      console.log(
        '🚀 ~ file: chats.ts:121 ~ fetchOldChatMessages ~ historyId:',
        history
      )
      try {
        this.loadings['fetchOldChatMessages_' + history.id] = true
        this.errors.fetchOldChatMessages = null
        const chattingUser = this.chattingUsers.find(
          item => item.userId === userId
        )
        chattingUser.currentHistory = history

        // sleep 1s
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (chattingUser) {
          const _messages = []
          for (let i = 0; i < 50; i++) {
            _messages.push({
              isUserMessage: i % 2 === 0,
              message: 'これは古いメッセージです。'
            })
          }
          chattingUser.oldMessages = _messages
        }
      } catch (e) {
        this.errors.fetchOldChatMessages = e
      } finally {
        this.loadings['fetchOldChatMessages_' + history.id] = false
      }
    },

    async fetchHistories (userId: string) {
      try {
        this.loadings.fetchHistories = true
        this.errors.fetchHistories = null
        // sleep 1s
        await new Promise(resolve => setTimeout(resolve, 1000))
        const chattingUser = this.chattingUsers.find(
          item => item.userId === userId
        )
        if (chattingUser) {
          chattingUser.histories = [
            {
              id: '1',
              label: '[1回目] 2023/07/09 20:02',
              operator: '相談員 瀬戸口'
            },
            {
              id: '2',
              label: '[2回目] 2023/07/10 10:02',
              operator: 'レホアンハオ'
            }
          ]
        }
      } catch (e) {
        this.errors.fetchHistories = e
      } finally {
        this.loadings.fetchHistories = false
      }
    },

    setExtendTabIndex (userId: string, index: number) {
      const chattingUser = this.chattingUsers.find(
        item => item.userId === userId
      )
      if (chattingUser) {
        chattingUser.extendTabIndex = index
      }
    },

    clearCurrentHistory (userId: string) {
      const chattingUser = this.chattingUsers.find(
        item => item.userId === userId
      )
      if (chattingUser) {
        chattingUser.currentHistory = null
      }
    }
  }
})
