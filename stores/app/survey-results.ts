import type { SurveyResult, SurveyResultsSearchConditions } from "@/types";
import { useAppService } from "~/composables/useAppService";

export const useSurveyResultsStore = defineStore("surveyResultsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    surveyResults: [] as SurveyResult[],
    searchConditions: {} as SurveyResultsSearchConditions,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalSurveyResultsCount: 0,
    sortConditions: {} as Sort,

    surveyResult: {} as SurveyResult,
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * state.pagination.pageRangeDisplayed,
        state.totalSurveyResultsCount,
      );
    },
  },
  actions: {
    async fetchSurveyResults(customerId: string) {
      const router = useRouter();
      try {
        this.loadings.fetchSurveyResults = true;
        this.errors.fetchSurveyResults = null;

        // router.push({
        //   query: {
        //     ...this.searchConditions,
        //     sortBy: this.sortConditions.sortBy,
        //     sortDesc: "" + this.sortConditions.sortDesc,
        //     ...this.pagination,
        //     counseleeId: Array.isArray(this.searchConditions.counseleeId)
        //       ? this.searchConditions.counseleeId.join(",")
        //       : this.searchConditions.counseleeId
        //       ? this.searchConditions.counseleeId
        //       : undefined,
        //   },
        // });
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey-result/survey-results/advanced", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.searchConditions,
            customerId,
            surveyId: Array.isArray(this.searchConditions.surveyId)
              ? this.searchConditions.surveyId.join(",")
              : this.searchConditions.surveyId,
            counseleeId: Array.isArray(this.searchConditions.counseleeId)
              ? undefined
              : this.searchConditions.counseleeId
              ? this.searchConditions.counseleeId
              : undefined,
            ...this.pagination,
          },
        });
        this.surveyResults = data.value?.items;
        this.totalSurveyResultsCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchSurveyResults ~ e:", e);
        this.errors.fetchSurveyResults = e;
      } finally {
        this.loadings.fetchSurveyResults = false;
      }
    },

    resetSearchConditions() {
      this.searchConditions = {
        surveyId: [],
        counseleeId: [],
        startDate: "",
        endDate: "",
      };
    },

    updateConditions(
      searchConditions?: SurveyResultsSearchConditions,
      pagination?: Pagination,
      sortConditions?: Sort,
    ) {
      if (searchConditions) {
        this.searchConditions = searchConditions;
      }
      if (pagination) {
        this.pagination = pagination;
      }
      if (sortConditions) {
        this.sortConditions = sortConditions;
      }
    },

    async fetchSurveyResult(surveyResultId: string) {
      try {
        this.loadings.fetchSurveyResult = true;
        this.errors.fetchSurveyResult = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("survey-result/" + surveyResultId, {
          method: "GET",
        });
        this.surveyResult = data.value;
        return this.surveyResult;
      } catch (e) {
        console.log("🚀 ~ fetchSurveyResult ~ e:", e);
        this.errors.fetchSurveyResult = e;
      } finally {
        this.loadings.fetchSurveyResult = false;
      }
    },
  },
});
