import { useAppService } from "~/composables/useAppService";
import type { ConsultingAccountListResponse } from "~/types";

export const useProjectsStore = defineStore("projects", {
  state: () => ({
    projects: [] as Project[],
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    currentProject: {} as Project,
  }),

  actions: {
    async fetchProjects() {
      try {
        this.loadings.fetchProjects = true;
        this.errors.fetchProjects = null;
        const {
          data,
        }: {
          data: Ref<ConsultingAccountListResponse>;
        } = await useAppService("/consulting-accounts", {
          method: "GET",
        });

        this.projects = data.value.consultingAccounts.map(
          (consultingAccount) => {
            return {
              id: consultingAccount.id as number,
              name: consultingAccount.name,
              avatar: consultingAccount.avatar,
              messagesCount: randomFromTo(0, 10),
            };
          }
        );
        // this.projects = [
        //   {
        //     id: 1,
        //     name: 'PNL相談',
        //     avatar: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAMAAAAJbSJIAAAApVBMVEX///8AoN8AAAAAnd68vLzFxcVzx+wAnt6rq6u/v78Amt2k2fIAo+Dy+/77//+z4PTi9PsPpuHu7u739/eq3PPb8fqGze684/VBsuVHR0fE5/f1/P5VuefM6/goq+Pb29stLS1oaGidnZ1XV1dOt+eEy+1nwOnPz8+UlJR2dnZgYGCIiIihoaGtra2Q0e/i4uIYGBgkJCQ4ODh7e3sREREWFhZEREQREW52AAAH7ElEQVR4nO2daXuiOhSAQ2JNb8CFVivVajtTZXY6ne3//7SLtrWQPZDg8pz30zwjMryTAOecLCIEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgi8Fok+fFbHHo63Dg8sLh4MWGMlLCkmIY7Io88wU7GE5TRqMdlK3GqqMG0/ko3tyUFJP5UnlYNzw9YAfDKX3120LSgeSQZZyvEsLYrqXJ9g9Rui5mskM74QfGDobDVUWwVMy4zweTvJQjtHZU2dyUMJrFB2nLD9jJsGD1S2eT6qfzPCrtIgWlZRZ33ZL3P7GT4TDlG+e9ERfxSqP3dutG/U4b8iPGboZLxl8ze73gYbx/AukdCc07c7x+xK6GBeGvmIx2H4zs/F6+QvrdvEo/Yexs2BcN4/Kvp2t7v92X0lFIs1c+Y2+GBRH+2gBld6EfOVe32JPhZLAWbk6bZkzmQQUvMPZkSPupawO+NWOh+5fur1sJfsPeDCP+5W4PyzVxbe+5heLVL16wjWELSKZ+pvbw81VTwU+C36EMI7JSPm96GP99amh4eTyGpaKqFXvby7o/fcNSUXEv9pyu64gNI3KnM8S90zeM2EZniC+P1rDMCN8wvE/qCRhviD8fpWGZCUarvIgnJcVmnRBdVkWpLNfYG+Lfx2dIWdqv1yyWRaaRpCutIf5wZIaUreeS5+O0HykdZfFbxRA/HpNh6TdVfHewUaYgkn5aNcS3bhFcSEOSzjTfHt8p0kjJK6NmiB+cIriAhiw3JO8TRU9lQirV467QJYILZkhZbDzBVJ5rUb4qKRi6RHChDCmxyWkHmfQUjO/dgiH+dGhDO0GEFlJFoRFFQ/sILpChpWCpuJLdi2xpNMTfD2pocQ++MU4kiiQ3G9pGcEEM+QvUMpOUriitJ8NSQ/ztYIY0darxyk7DdQK5If5yKEPhUahnwQ9/bP+T6m99hSH+ehhDVRqrZCLrp7VuqjLEv8wRXABD/kFoRvI8JbV+oDTED0ZF/4bUtQkRGomNSPp2hviPKUj1byhGlUaEgUg+TdQYGiM474ZUVS/TsRHHB6Lqjag1NERw3g3r3cuSpXgmUr2b9Yb6CM67YYNOWiJ2U1YdVDQY4h8dGtZ7lzW5OFBXrSuaDPHHDg2F3M6KWDSsRn5GQ00E59vQKSR9ZyY+atZOhuoanHdD7UinkqnYhtXOYGGorMH5NpTXrI0sxDasjrXZGOJ/HRk2G5AfejBUBKnnZIifZTW4szKURnBHch/6MpREcEfyLB1LDCvxrb2hePHeDZuEpbLAlLq+Ld7gI7hjiWmEDLGWZroY8hGcd0O3KpT6VLXO4GTIRXD+cwvVeJoWsY5Rq7bJDL+oFWs1OP/5YZNHjRi01V87MsMLfjpehWoNzn+O3+RGFG/D+jipzPA/6XyuVyqjqP7rNKRBN83EOk1a/VxuiO7Vir/2BaoAhu7vC3HCOJeFKQzR1bNScT/VL0A1kThn+WKGz8VGKkN0Lc6s3PMUyrBef7BBUoeKSG26gtIQoVvJR6/chzJ0vhPFu5B/XmkM0Ve14kUww7X5uxVkwxbcO0dniH6rFX8EMnQZIC2DbtlMNy5u0BrWFxrU+R7IkFKHfirpo8JLVW+IvqsVP78sVvNtqFioJyWXrV5g3BoTg6Eubv0dxlAzoZk/h0yQptzYh8lQWFBRQfbK9DGfhmRWrSgVFCfvGQ114U0gw7KjWixKk3bRiCZ8BzAboqc/XRtGNDIt2BrLZ0RJ5l9aGOoiuECGEWW5tqeqJu7Vg25rQ4T+dW24XbClfjEuM9UaPslUDjtD9Cg5LKzhdgl7LH2oLlWTS+VTOSwNkSbvD2S4dUz6S05yWijbTzEAaWsorlILb7ibqp/e3cyW48FiMZ7O43xFdGsw+Ze9m+H7YuYODaOXBfiEJklSNqlhhTeT5s/2hnZluUBrZqjF4kRh0wJnQ114E9rQApLIwwQXQ12B6uCGypzEydAivDmUIaWqGXFuhujq4TgNNXPDHQ21BarDGVJNecfV0BTetFnL3ViQpJq6gLvh6zYt3g1pZtrPRAXT5pMNDLXhTYs9FUazqEnXpfIXfStDXYGq1b4YY03MqYKYUslGhtIqmwdDhGLq1ozEkEc2NlRHcC0N0Th3aMYywTJPUGloqAxv2hrq0lvBL7WpHjc1RE+hDBGar7ULmt/9rOZMNzZEV9IIzofhdq1vos2UKKNr26UnzQ3RtSyC82O43VExT8Td2nZ2Zb64dtiwrYWhdPzNl2HJYnmTRbuNBrZ7DWwhpRxZ9UdOw6itDCXjbx4Ntwyno6J/l2VpkqxWd3kx4es2ZtoZigUqz4YeaGkoRHDnZ8iPv52hIXeGczSsF6jO0tDnroIB8GGInv6eu2GlQHWuhuj637kb7gtUZ2zouI/wKRq+hDdnbbgbfzsNw2Y78KHdjt4nYdi0CXdnOwHD28abfW65OH5Du11N2qPc7do7nKH9FkotUe9Y7pua4WO7TYVdGPLbA0l3BPRB1VCzMN0/N/wvBwS6DSuGPzvroTsW9f3WgjXhu+HX7nroC8tqyZdGwX4E4M1QtztEIJb74ULKdIO4LXkxfGi4V3I7BvluX1nCgv7Ewc7Qat+rEIzjPFv3w/7cyNaw0TbJJ0OvshbtPOkdrId2RdfvCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEDP/zTLi5kAW0PaAAAAAElFTkSuQmCC'
        //   },
        //   {
        //     id: 2,
        //     name: 'Workway相談',
        //     avatar: 'https://storage.googleapis.com/mixb-images/uploads/article/picture/image/501552/logo2.jpg'
        //   }
        // ]
        this.currentProject = this.projects[0];
      } catch (error) {
        this.errors.fetchProjects = error;
      } finally {
        this.loadings.fetchProjects = false;
      }
    },
  },
});
