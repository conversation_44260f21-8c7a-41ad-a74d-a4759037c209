import type { Wizard } from "@/types";
import { useAppService } from "~/composables/useAppService";
import { useAppUIStore } from "~/stores/app/ui";

export const useAppWizardsStore = defineStore("appWizardsStore", {
  state: () => ({
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    wizards: [] as Wizard[],
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalWizardsCount: 0,
    sortConditions: {} as Sort,
    allWizards: [] as Wizard[],
    selectedWizardsIds: [] as string[],
  }),

  getters: {
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) *
          (state.pagination.pageRangeDisplayed || 0) +
        1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * (state.pagination.pageRangeDisplayed || 0),
        state.totalWizardsCount || 0,
      );
    },

    getWizardById: (state) => (wizardId: string) => {
      console.log("🚀 ~ wizardId:", wizardId);
      const wizard = state.allWizards.find((wizard) => {
        return wizard.wizardId === wizardId;
      });
      console.log("🚀 ~ wizard:", wizard);

      return wizard;
    },
  },
  actions: {
    async fetchWizards(customerId: string) {
      try {
        this.loadings.fetchWizards = true;
        this.errors.fetchWizards = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard/surveys", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.pagination,
            customerId,
          },
        });
        this.wizards = data.value?.items;
        this.totalWizardsCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchWizards ~ e:", e);
        this.errors.fetchWizards = e;
      } finally {
        this.loadings.fetchWizards = false;
      }
    },

    async fetchAllWizards(customerId: string) {
      try {
        this.loadings.fetchAllWizards = true;
        this.errors.fetchAllWizards = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard/wizards", {
          method: "GET",
        });
        this.allWizards = data.value?.items;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchAllWizards ~ e:", e);
        this.errors.fetchAllWizards = e;
      } finally {
        this.loadings.fetchAllWizards = false;
      }
    },

    async deleteWizard(wizard: Wizard) {
      try {
        this.loadings.deleteSurvey = {
          [wizard.wizardId as string]: true,
        };
        this.errors.deleteSurvey = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard/" + wizard.wizardId, {
          method: "DELETE",
        });
        this.wizards = this.wizards.filter(
          (obj) => obj.wizardId !== wizard.wizardId,
        );
        this.totalWizardsCount = this.totalWizardsCount - 1;
        return true;
      } catch (error) {
        this.errors.deleteSurvey = error;
        return false;
      } finally {
        this.loadings.deleteSurvey = {
          [wizard.wizardId as string]: false,
        };
      }
    },

    async createWizard(wizard: Wizard) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.createWizard = true;
        this.errors.createWizard = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard/wizards", {
          method: "POST",
          data: wizard,
        });
        return true;
      } catch (error) {
        this.errors.createWizard = error;
        return false;
      } finally {
        this.loadings.createWizard = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async fetchSurvey(wizardId: string) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.fetchSurvey = true;
        this.errors.fetchSurvey = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("/wizard/" + wizardId, {
          method: "GET",
        });
        this.surveyDetail = data.value;
        return data.value;
      } catch (error) {
        this.errors.fetchSurvey = error;
        return false;
      } finally {
        this.loadings.fetchSurvey = false;
        appUIStore.toggleAppLoading(false);
      }
    },

    async updateWizard(wizardId: string, wizard: Wizard) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.updateSurvey = true;
        this.errors.updateSurvey = null;
        appUIStore.toggleAppLoading(true);
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useAppService("wizard/" + wizardId, {
          method: "PATCH",
          data: wizard,
        });
        return true;
      } catch (error) {
        this.errors.updateSurvey = error;
        return false;
      } finally {
        this.loadings.updateSurvey = false;
        appUIStore.toggleAppLoading(false);
      }
    },
  },
});
