import { useAppService } from "~/composables/useAppService";
import type { Customer, CaseChatMessage } from "~/types";
import { useSocketStore } from "~/stores/socket";
import { useCasesStore } from "./cases";
import { CustomerFeature } from "~/types/enums.d";
import _ from "lodash";
export const useAppCustomersStore = defineStore("appCustomersStore", {
  // persist: {
  //   paths: ["currentCustomer", "customerIndexes"],
  // },
  persist: [
    {
      paths: ["customerIndexes"],
      storage: window.localStorage,
    },
  ],
  state: () => ({
    customers: [] as Customer[],
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    currentCustomer: {} as Customer,
    customerIndexes: {} as Record<string, number>,
    customersSorted: [] as Customer[],
    currentCustomerId: "",
  }),
  getters: {
    customerIds: (state) => {
      return state.customers.map((item) => item.customerId);
    },
    // customersSorted: (state) => {
    //   // sort by customerIndexes
    //   return state.customers.sort((a: Customer, b: Customer) => {
    //     return (
    //       (state.customerIndexes[a.customerId as string] || 0) -
    //       (state.customerIndexes[b.customerId as string] || 0)
    //     );
    //   });
    // },
    avaiableFeatures: (state) => {
      return state.currentCustomer.featureList;
    },
  },
  actions: {
    setCustomer(customer: Customer) {
      this.customers = this.customers.map((item) => {
        if (item.customerId === customer.customerId) {
          return customer;
        }
        return item;
      });
    },
    async fetchCustomers(loading = true) {
      const authStore = useAuthStore();
      const socketStore = useSocketStore();
      try {
        this.loadings.fetchCustomers = loading;
        this.errors.fetchCustomers = null;
        const {
          data,
        }: {
          data: Ref<Customer[]>;
        } = await useAppService(
          "/customer/customers/" + authStore.user?.counselorId,
          {
            method: "GET",
          },
        );

        this.customers = data.value;
        // sort by customerIndexes
        this.customersSorted = this.customers.sort(
          (a: Customer, b: Customer) => {
            return (
              (this.customerIndexes[a.customerId as string] || 0) -
              (this.customerIndexes[b.customerId as string] || 0)
            );
          },
        );
        // this.currentCustomer = this.customers[0];
        socketStore.appConnectSocket(
          this.customerIds as string[],
          authStore.user?.counselorId as string,
        );
        socketStore.sendOnlineStatus(true);
      } catch (error: any) {
        this.errors.fetchCustomers = error;
        // handle case CUSTOMER_ALREADY_DELETED
        if (error?.response?.data?.code === "CUSTOMER_ALREADY_DELETED") {
          // remove current customer in Session
          this.currentCustomer = {} as Customer;
        }
      } finally {
        this.loadings.fetchCustomers = false;
      }
    },

    async updateCustomer(customer: Customer) {
      try {
        this.loadings.updateCustomer = true;
        this.errors.updateCustomer = null;
        const {
          data,
        }: {
          data: Ref<Customer[]>;
        } = await useAppService("/customer/profile/" + customer.customerId, {
          method: "PATCH",
          data: {
            theme: customer.theme || "primary",
            customerName: customer.basic.customerName,
            customerImage: customer.basic.customerImage || "",
          },
        });
        this.customers = this.customers.map((c) => {
          if (c.customerId === customer.customerId) {
            return customer;
          }
          return c;
        });
        this.currentCustomer = customer;

        return true;
      } catch (error: any) {
        console.log("🚀 ~ updateCustomer ~ error:", error);
        this.errors.updateCustomer =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.updateCustomer = false;
      }
    },

    updateOpenCaseCount(customerId: string, change: number) {
      this.customers = this.customers.map((c) => {
        if (c.customerId === customerId) {
          c.openCaseCount = !_.isNil(c.openCaseCount) ? c.openCaseCount + change : 0;
        }
        return c;
      });
    },

    updateUnReadMessages(message: CaseChatMessage) {
      this.customers = this.customers.map((c) => {
        if (c.customerId === message.customerId) {
          if (!c.unReadMessages) {
            c.unReadMessages = [];
          }

          // check if caseId is already in unReadMessages
          const index = c.unReadMessages.findIndex((item) => {
            return item.caseId === message.caseId;
          });
          if (index > -1) {
            c.unReadMessages[index] = message;
          } else {
            c.unReadMessages.unshift(message);
          }
        }
        return c;
      });
    },

    readMessage(customerId?: string, caseId?: string) {
      const casesStore = useCasesStore();
      this.customers = this.customers.map((c) => {
        if (c.customerId === customerId) {
          if (c.unReadMessages) {
            c.unReadMessages = c.unReadMessages.filter(
              (item) => item.caseId !== caseId,
            );
          }
        }
        return c;
      });

      casesStore.readMessage(caseId as string);
    },
  },
});
