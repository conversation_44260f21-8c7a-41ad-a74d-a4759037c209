// import type { User, AuthLoginPayload } from '~/types'
// import { useAuthService } from '~/composables/useAuthService'

export const useDialogsStore = defineStore('dialogs', {
  state: () => {
    return {
      confirmDialog: {
        open: false,
        title: '',
        message: '',
        confirmButtonText: '',
        cancelButtonText: '',
        confirmButtonColor: '',
        messageClass: '',
        onConfirm: () => {},
        onCancel: null,
        survey: null as any
      }
    }
  },
  getters: {},
  actions: {
    onCloseConfirmDialog: function () {
      this.confirmDialog.open = false
      this.confirmDialog.messageClass = ''
    },

    onOpenConfirmDialog: function (payload: {
      title: string
      message: string
      confirmButtonText: string
      cancelButtonText: string
      messageClass?: string
      survey?: any
      confirmButtonColor?: string
      onConfirm: () => void
    }) {
      this.confirmDialog.open = true
      this.confirmDialog.title = payload.title
      this.confirmDialog.message = payload.message
      this.confirmDialog.confirmButtonText = payload.confirmButtonText
      this.confirmDialog.cancelButtonText = payload.cancelButtonText
      this.confirmDialog.messageClass = payload.messageClass || ''
      this.confirmDialog.onConfirm = payload.onConfirm
      this.confirmDialog.survey = payload.survey
      this.confirmDialog.confirmButtonColor = payload.confirmButtonColor || ''
    }
  }
})
