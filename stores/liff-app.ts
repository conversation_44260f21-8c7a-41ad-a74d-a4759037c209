import type { CaseChatMessage } from "~/types";

export const useLiffAppStore = defineStore("liffAppStore", {
  persist: {
    paths: ["user", "authenticated"],
    storage: window.localStorage,
  },
  state: () => ({
    session: null as any | null,
    lineUser: null as any | null,
    customer: null as any | null,
    counselee: null as any | null,
    messages: [] as any[],
    loadings: {
      login: false,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,
    survey: null as any | null,
  }),
  getters: {
    isAuthenticated: (state) => {
      return !!state.session;
    },
  },
  actions: {
    async fetchCustomerInfo(
      customerId: string,
    ): Promise<any | false | undefined> {
      const router = useRouter();
      const route = useRoute();
      try {
        const appConfig = useAppConfig();
        const colorMode = useColorMode();

        this.errors.fetchCustomerInfo = null;
        this.loadings.fetchCustomerInfo = true;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/customer", {
          method: "GET",
          params: {
            customerId,
          },
        });

        this.customer = {
          ...data.value?.body?.customer,
          customerId,
        };

        appConfig.ui.primary = "cyan";
        colorMode.preference = "light";
        router.push({
          query: {
            ...route.query,
            c: customerId,
          },
        });
        return data.value;
      } catch (error: any) {
        this.errors.fetchCustomerInfo =
          error?.response?.data?.error || error?.message;
        return false;
      } finally {
        this.loadings.fetchCustomerInfo = false;
      }
    },

    async fetchCounseleeInfo(
      customerId: string,
      loading = true,
    ): Promise<any | false | undefined> {
      try {
        this.errors.fetchCounseleeInfo = null;
        this.loadings.fetchCounseleeInfo = loading;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/counselee", {
          method: "POST",
          data: {
            customerId,
            idToken: this.lineUser?.idToken,
          },
        });

        this.counselee = {
          ...data.value?.body,
        };
        this.messages = data.value?.body?.chats || [];

        return data.value?.body;
      } catch (error: any) {
        this.errors.fetchCounseleeInfo =
          error?.response?.data?.code || error?.message;
        return false;
      } finally {
        this.loadings.fetchCounseleeInfo = false;
      }
    },

    async sendMessage(message: string): Promise<any | false | undefined> {
      try {
        this.errors.sendMessage = null;
        this.loadings.sendMessage = true;
        this.messages.push({
          content: {
            text: message,
          },
          createdAt: new Date().toISOString(),
          sender: "counselee",
          isSending: true,
          senderName: this.lineUser?.displayName,
          senderAvatar: this.lineUser?.pictureUrl,
        });

        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/liff", {
          method: "POST",
          data: {
            idToken: this.lineUser?.idToken,
            content: {
              text: message,
            },
            customerId: this.customer?.customerId,
          },
        });
        // find the message and update it
        data.value?.body?.sendMessages?.forEach((msg: CaseChatMessage) => {
          const index = this.messages.findIndex(
            (message) =>
              message.isSending && message.content.text === msg.content.text,
          );
          if (index !== -1) {
            this.messages[index] = msg;
          } else {
            this.messages.push(msg);
          }
        });
      } catch (error: any) {
        this.errors.sendMessage = error?.response?.data?.code || error?.message;
        const index = this.messages.findIndex(
          (msg: CaseChatMessage) =>
            msg.isSending && msg.content?.text === message,
        );
        if (index !== -1) {
          this.messages[index].error = error?.response?.data?.message;
          this.messages[index].isSending = false;
        }
        return false;
      } finally {
        this.loadings.sendMessage = false;
      }
    },

    receiveMessage(message: CaseChatMessage) {
      console.log("🚀 ~ receiveMessage ~ message:", message);
      // check if message already exists
      const isExist = this.messages.find(
        (msg) => msg.chatId === message.chatId,
      );
      if (!isExist) {
        this.messages.push(message);
      }
    },

    async endChat(): Promise<any | false | undefined> {
      try {
        this.errors.endChat = null;
        this.loadings.endChat = true;
        // sleep 1s
        await new Promise((resolve) => setTimeout(resolve, 1000));
        this.session = null;
        this.customer = null;
        const accessToken = useCookie("webAppAccessToken");
        accessToken.value = null;
      } catch (error: any) {
        this.errors.endChat = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.endChat = false;
      }
    },

    async fetchMessages(): Promise<any | false | undefined> {
      try {
        this.errors.fetchMessages = null;
        this.loadings.fetchMessages = true;
        // sleep 1s
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error: any) {
        this.errors.fetchMessages = error?.response?.data?.error;
        return false;
      } finally {
        this.loadings.fetchMessages = false;
      }
    },

    initialSurveyData() {
      if (this.lineUser && this.survey?.formTemplate) {
        this.survey.formTemplate = this.survey?.formTemplate.map(
          (element: any) => {
            if (element.id === "name") {
              element.value = this.lineUser?.displayName;
            }
            return element;
          },
        );
      }
    },
  },
});
