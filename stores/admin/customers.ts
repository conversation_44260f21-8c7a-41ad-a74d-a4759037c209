import { cloneDeep } from "lodash";
import { useAdminService } from "~/composables/useAdminService";
import type { Customer, CommonResponse, Counselor } from "~/types";
import { useAppUIStore } from "../app/ui";

export const useCustomersStore = defineStore("customersStore", {
  state: () => ({
    loadings: {
      deleteCustomerAccount: {} as LoadingDynamic,
      switchCustomerAccountStatus: {} as LoadingDynamic,
    } as LoadingDynamic,
    errors: {
      deleteCustomerAccount: null,
    } as ErrorDynamic,
    customers: [] as Customer[],
    selectedCustomer: null as Customer | null,
    updatingCustomer: {} as LoadingDynamic,
    searchKeyword: "",
    statusFilter: ["active"],
  }),
  getters: {
    customerAccountsCount: (state: any): number => {
      return state.customers.length;
    },
    customersFiltered: (state: any) => {
      return state.customers
        .filter((customer: any) => {
          return customer.basic?.customerName
            .toLowerCase()
            .includes(state.searchKeyword.toLowerCase());
        })
        .filter((customer: any) => {
          let isMatch = false;
          state.statusFilter.forEach((status: string) => {
            if (status === "active" && !customer.deletedAt) {
              isMatch = true;
            }
            if (status === "inactive" && customer.deletedAt) {
              isMatch = true;
            }
          });
          return isMatch;
        })
        .map((customer: any) => {
          const activeChannels = [] as string[];
          ["line", "application", "facebook"].forEach((channel: string) => {
            if (customer[channel]?.isActive) {
              activeChannels.push(channel);
            }
          });
          return {
            ...customer,
            name: customer.basic?.customerName,
            snsChannels:
              customer.line?.isActive.toString() +
              customer.application?.isActive.toString() +
              customer.facebook?.isActive.toString(),
            counserlors: customer.roles?.length,
            activeChannels,
          };
        });
    },
  },
  actions: {
    setCustomerCounselors(customers: Customer[]) {
      this.customers = customers;
    },
    async fetchCustomerAccounts() {
      try {
        this.loadings.fetchCustomerAccounts = true;
        this.errors.fetchCustomerAccounts = null;
        const {
          data,
        }: {
          data: Ref<Customer[]>;
        } = await useAdminService("/customer/customers", {
          method: "GET",
        });

        this.customers = data.value.map((customer) => ({
          ...customer,
          startDate: new Date(customer.startDate),
          endDate: new Date(customer.endDate),
        }));
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: customers.ts:47 ~ fetchCustomerAccounts ~ error:",
          error,
        );
        this.errors.fetchCustomerAccounts = error;
        return false;
      } finally {
        this.loadings.fetchCustomerAccounts = false;
      }
    },

    async updateCustomerAccount(customerAccount: Customer) {
      const appUIStore = useAppUIStore();
      try {
        this.updatingCustomer[customerAccount.customerId || ""] = true;
        this.errors.updateCustomerAccount = null;
        appUIStore.appLoading = true;
        const {
          data,
        }: {
          data: Ref<Customer>;
        } = await useAdminService(`/customer/${customerAccount.customerId}`, {
          method: "PATCH",
          data: customerAccount,
        });

        this.customers = this.customers.map((item) => {
          if (item.customerId === customerAccount.customerId) {
            return {
              ...item,
              ...customerAccount,
              counseleeLimit: data.value.counseleeLimit,
            };
          }
          return item;
        });
        return true;
      } catch (error: any) {
        this.errors.updateCustomerAccount = error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.updatingCustomer[customerAccount.customerId || ""] = false;
        appUIStore.appLoading = false;
      }
    },

    async addCustomerAccount(customerAccount: Customer) {
      const appUIStore = useAppUIStore();
      try {
        this.loadings.addCustomerAccount = true;
        this.errors.addCustomerAccount = null;
        appUIStore.appLoading = true;
        const {
          data,
        }: {
          data: Ref<Customer>;
        } = await useAdminService("/customer/customers", {
          method: "POST",
          data: customerAccount,
        });

        const customers = cloneDeep(this.customers);
        customers.unshift({
          ...data.value,
          startDate: new Date(data.value.startDate),
          endDate: new Date(data.value.endDate),
        });

        this.customers = customers;
        return true;
      } catch (error: any) {
        this.errors.addCustomerAccount = error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.addCustomerAccount = false;
        appUIStore.appLoading = false;
      }
    },

    async deleteCustomerAccount(customerAccount: Customer) {
      try {
        this.loadings.deleteCustomerAccount[
          customerAccount.customerId as string
        ] = true;
        this.errors.deleteCustomerAccount = null;
        const {
          data,
        }: {
          data: Ref<CommonResponse>;
        } = await useAdminService(`/customer/${customerAccount.customerId}`, {
          method: "DELETE",
          data: customerAccount,
        });

        this.customers = this.customers.filter((item) => {
          return item.customerId !== customerAccount.customerId;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: customers.ts:188 ~ deleteCustomerAccount ~ error:",
          error,
        );
        this.errors.deleteCustomerAccount = error;
        return false;
      } finally {
        this.loadings.deleteCustomerAccount[
          customerAccount.customerId as string
        ] = false;
      }
    },

    async switchCustomerAccountStatus(customerAccount: Customer) {
      try {
        this.loadings.switchCustomerAccountStatus[
          customerAccount.customerId as string
        ] = true;
        this.errors.switchCustomerAccountStatus = null;
        const {
          data,
        }: {
          data: Ref<CommonResponse>;
        } = await useAdminService(`/customer/${customerAccount.customerId}`, {
          method: "DELETE",
          data: customerAccount,
        });

        this.customers = this.customers.map((item) => {
          if (item.customerId === customerAccount.customerId) {
            return {
              ...item,
              deletedAt: item.deletedAt ? "" : new Date().toISOString(),
            };
          }
          return item;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: customers.ts:188 ~ switchCustomerAccountStatus ~ error:",
          error,
        );
        this.errors.switchCustomerAccountStatus = error;
        return false;
      } finally {
        this.loadings.switchCustomerAccountStatus[
          customerAccount.customerId as string
        ] = false;
      }
    },

    async updateCustomerCounselors(customer: Customer, customerRoles: any[]) {
      try {
        this.loadings[customer.customerId || ""] = true;
        this.errors.updateCustomerCounselors = null;
        const {
          data,
        }: {
          data: Ref<Customer>;
        } = await useAdminService(`/customer/${customer.customerId}`, {
          method: "PUT",
          data: customerRoles,
        });

        this.customers = this.customers.map((item) => {
          if (item.customerId === data.value.customerId) {
            return { ...customer, roles: data.value.roles };
          }
          return item;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: customers.ts:188 ~ deleteCustomerAccount ~ error:",
          error,
        );
        this.errors.updateCustomerCounselors = error;
        return false;
      } finally {
        this.loadings[customer.customerId || ""] = false;
      }
    },

    async updateMonitaringKeywords(customer: Customer, watchWords: string[]) {
      try {
        this.loadings[customer.customerId || ""] = true;
        this.errors.updateCustomerCounserlors = null;

        const {
          data,
        }: {
          data: Ref<CommonResponse>;
        } = await useAdminService(
          `/customer/watch-keyward/${customer.customerId}`,
          {
            method: "PATCH",
            data: watchWords,
          },
        );

        this.customers = this.customers.map((item) => {
          if (item.customerId === customer.customerId) {
            item.setting!.watchWords = watchWords;
          }
          return item;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: customers.ts:188 ~ deleteCustomerAccount ~ error:",
          error,
        );
        this.errors.updateCustomerCounserlors = error;
        return false;
      } finally {
        this.loadings[customer.customerId || ""] = false;
      }
    },
  },
});
