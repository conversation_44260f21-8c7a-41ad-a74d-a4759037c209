import { cloneDeep } from "lodash";
import { useAdminService } from "~/composables/useAdminService";
import { useAppUIStore } from "../app/ui";
import type {
  Counselor,
  CommonResponse,
  CounselorWithCustomersResponse,
} from "~/types";
import { useCustomersStore } from "./customers";

export const useCounselorsStore = defineStore("counselor", {
  state: () => ({
    loadings: {
      deleteCounselor: {} as LoadingDynamic,
      switchCounselorStatus: {} as LoadingDynamic,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,
    counselors: [] as Counselor[],
    selectedCounselor: null as Counselor | null,
    updatingCounselor: {} as LoadingDynamic,
    searchKeyword: "",
    statusFilter: ["active"],
  }),

  getters: {
    counselorCount: (state): number => {
      return state.counselors?.length;
    },

    counserlorsForPalette: (state): any[] => {
      return (
        state.counselors.map((counselor) => {
          const obj = {
            ...counselor,
            id: counselor.counselorId,
            avatar: {
              src: counselor.profileImage,
              alt: counselor.fullName,
            },
            label: counselor.fullName,
          };
          return obj;
        }) || []
      );
    },

    counserlorsFiltered: (state) => {
      return state.counselors
        .filter((counselor) => {
          return (
            counselor.fullName
              .toLowerCase()
              .includes(state.searchKeyword.toLowerCase()) ||
            counselor.email
              .toLowerCase()
              .includes(state.searchKeyword.toLowerCase()) ||
            counselor.counselorId
              .toLowerCase()
              .includes(state.searchKeyword.toLowerCase()) ||
            counselor.organizationName
              .toLowerCase()
              .includes(state.searchKeyword.toLowerCase())
          );
        })
        .filter((counselor: any) => {
          let isMatch = false;
          state.statusFilter.forEach((status: string) => {
            if (status === "active" && !counselor.deletedAt) {
              isMatch = true;
            }
            if (status === "inactive" && counselor.deletedAt) {
              isMatch = true;
            }
          });
          return isMatch;
        });
    },
  },
  actions: {
    async fetchCounselors() {
      try {
        this.loadings.fetchCounselors = true;
        this.errors.fetchCounselors = null;
        const {
          data,
        }: {
          data: Ref<Counselor[]>;
        } = await useAdminService("/counselor/counselors", {
          method: "GET",
        });
        console.log(data.value);

        this.counselors = data.value;
        return true;
      } catch (error) {
        this.errors.fetchCounselors = error;
        return false;
      } finally {
        this.loadings.fetchCounselors = false;
      }
    },

    async updateCounselor(counselor: Counselor) {
      const customersStore = useCustomersStore();
      const appUIStore = useAppUIStore();
      try {
        this.updatingCounselor[counselor.counselorId || ""] = true;
        this.errors.updateCounselor = null;
        appUIStore.appLoading = true;
        // delete unnecessary fields
        delete counselor.needReissueTempPassword;
        const {
          data,
        }: {
          data: Ref<CounselorWithCustomersResponse>;
        } = await useAdminService(`/counselor/${counselor.counselorId}`, {
          method: "PATCH",
          data: counselor,
        });
        const resCounselor = data.value.counselor;
        const resCustomers = data.value.customers;
        this.counselors = this.counselors.map((item) => {
          if (item.counselorId === resCounselor.counselorId) {
            return resCounselor;
          }
          return item;
        });

        customersStore.setCustomerCounselors(data.value.customers);
        return true;
      } catch (error: any) {
        this.errors.updateCounselor =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.updatingCounselor[counselor.counselorId || ""] = false;
        appUIStore.appLoading = false;
      }
    },

    async addCounselor(counselor: Counselor) {
      const customersStore = useCustomersStore();
      const appUIStore = useAppUIStore();
      try {
        this.loadings.addCounselor = true;
        this.errors.addCounselor = null;
        appUIStore.appLoading = true;
        const {
          data,
        }: {
          data: Ref<CounselorWithCustomersResponse>;
        } = await useAdminService("/counselor/counselors", {
          method: "POST",
          data: counselor,
        });

        const counselors = cloneDeep(this.counselors);
        counselors.unshift(data.value.counselor);
        this.counselors = counselors;
        customersStore.setCustomerCounselors(data.value.customers);
        return true;
      } catch (error: any) {
        this.errors.addCounselor =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.addCounselor = false;
        appUIStore.appLoading = false;
      }
    },

    async deleteCounselor(counselor: Counselor) {
      try {
        this.loadings.deleteCounselor[counselor.counselorId || ""] = true;
        this.errors.deleteCounselor = null;
        const {
          data,
        }: {
          data: Ref<CommonResponse>;
        } = await useAdminService(`/counselor/${counselor.counselorId}`, {
          method: "DELETE",
          data: counselor,
        });

        this.counselors = this.counselors.filter((item) => {
          return item.counselorId !== counselor.counselorId;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: counselor.ts:188 ~ deleteCounselor ~ error:",
          error,
        );
        this.errors.deleteCounselor = error;
        return false;
      } finally {
        this.loadings.deleteCounselor[counselor.counselorId || ""] = false;
      }
    },

    async switchCounselorStatus(counselor: Counselor) {
      try {
        this.loadings.switchCounselorStatus[counselor.counselorId || ""] = true;
        this.errors.switchCounselorStatus = null;
        const {
          data,
        }: {
          data: Ref<CommonResponse>;
        } = await useAdminService(`/counselor/${counselor.counselorId}`, {
          method: "DELETE",
          data: counselor,
        });

        this.counselors = this.counselors.map((item) => {
          if (item.counselorId === counselor.counselorId) {
            return {
              ...item,
              deletedAt: item.deletedAt ? "" : new Date().toISOString(),
            };
          }
          return item;
        });
        return true;
      } catch (error) {
        console.log(
          "🚀 ~ file: counselor.ts:188 ~ switchCounselorStatus ~ error:",
          error,
        );
        this.errors.switchCounselorStatus = error;
        return false;
      } finally {
        this.loadings.switchCounselorStatus[counselor.counselorId || ""] =
          false;
      }
    },

    async reissueTemporaryPassword(counselor: Counselor) {
      const customersStore = useCustomersStore();
      const appUIStore = useAppUIStore();
      try {
        this.updatingCounselor[counselor.counselorId || ""] = true;
        this.errors.updateCounselor = null;
        appUIStore.appLoading = true;
        const {
          data,
        }: {
          data: Ref<CounselorWithCustomersResponse>;
        } = await useAdminService(`/counselor/reissue-temporary-password`, {
          method: "POST",
          data: {
            counselorId: counselor.counselorId,
          },
        });
        // const resCounselor = data.value.counselor;
        // this.counselors = this.counselors.map((item) => {
        //   if (item.counselorId === resCounselor.counselorId) {
        //     return resCounselor;
        //   }
        //   return item;
        // });
        return true;
      } catch (error: any) {
        this.errors.updateCounselor =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.updatingCounselor[counselor.counselorId || ""] = false;
        appUIStore.appLoading = false;
      }
    },
  },
});
