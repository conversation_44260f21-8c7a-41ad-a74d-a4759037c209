import type { WizardElement } from "@/types";
export const useWizardStore = defineStore("wizardStore", {
  persist: {
    paths: ["currentStep", "stepsHistory", "selectedLanguage"],
  },
  state: () => ({
    customer: null as any | null,
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    wizard: null as any | null,
    currentStep: 1,
    stepsHistory: [1],
    selectedLanguage: "ja",
  }),
  getters: {
    currentFormElements: (state) => {
      return state.wizard?.wizard?.filter(
        (formElement: any) => formElement.step === state.currentStep,
      );
    },
    steps: (state) => {
      const steps = state.wizard?.wizard?.map(
        (formElement: any) => formElement.step,
      );
      // Remove duplicate
      return [...new Set(steps)];
    },
    nextAction() {
      let next = null;
      this.currentFormElements?.forEach((formElement: any) => {
        formElement?.values
          ?.filter((obj: any) => formElement?.value?.includes(obj.value))
          .forEach((otpion: any) => {
            next = otpion.next;
          });
      });
      return next;
    },
    isFirstStep(): boolean {
      return this.currentStep === this.steps[0];
    },
    isCanNext() {
      let isCanNext = true;
      this.currentFormElements?.forEach((formElement: any) => {
        if (formElement.required && !formElement.value.length) {
          isCanNext = false;
        }
      });
      return isCanNext;
    },
    fullName: (state) => {
      const nameElement = state.wizard?.wizard?.find(
        (obj: any) => obj.id === "name",
      );

      // return undefined if nameElement is not found or value is empty array
      return nameElement?.value?.[0] ? nameElement?.value : undefined;
    },

    endStep: (state) => {
      return state.wizard?.wizard?.find((obj: any) => obj.id === "end");
    },

    informationStep: (state) => {
      return state.wizard?.wizard?.find(
        (obj: any) => obj.id === "introductionOfConsultation",
      );
    },

    wizardResult: (state) => {
      const { FormElementTypes } = useWizard();
      return state.wizard?.wizard?.filter((obj: WizardElement) => {
        return FormElementTypes.includes(obj.type);
      });
    },
  },
  actions: {
    async fetchCustomerInfo(slug: string): Promise<any | false | undefined> {
      try {
        this.errors.fetchCustomerInfo = null;
        this.loadings.fetchCustomerInfo = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useCounseleeService("/counselee-chat/anonymous/" + slug, {
          method: "GET",
        });

        this.customer = {
          ...(this.customer || {}),
          ...data.value?.body,
        };
        const appConfig = useAppConfig();
        appConfig.ui.primary = "cyan";
        await this.fetchWizard(data.value?.body?.wizardId);
        return true;
      } catch (error: any) {
        this.errors.fetchCustomerInfo = error?.response?.data;
        return false;
      } finally {
        this.loadings.fetchCustomerInfo = false;
      }
    },

    async fetchWizard(wizardId: string): Promise<any | false | undefined> {
      try {
        this.errors.fetchSurvey = null;
        this.loadings.fetchSurvey = true;
        const route = useRoute();
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useCounseleeService("/external-wizard/" + wizardId, {
          method: "GET",
        });
        this.wizard = data.value;
        const schoolId = Number(route.query.schoolId);
        if (schoolId) {
          this.setSpecialValue(schoolId, "schoolId");
        }
        this.currentStep = this.wizard?.wizard[0]?.step;
        return data.value;
      } catch (error: any) {
        this.errors.fetchSurvey =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.fetchSurvey = false;
      }
    },

    async nextStep(next?: any, schema: any = null) {
      const route = useRoute();
      const dialogsStore = useDialogsStore();
      const { $i18n } = useNuxtApp();
      const t = $i18n.t;
      if (next?.action) {
        switch (next.action) {
          case "login":
            // Do something
            navigateTo("/login");
            break;
          case "submit":
            if (isChatAvailableTime(route.params.slug as string)) {
              const res = await this.submitWizard(true, false);
              navigateTo("/web-app/" + route.params.slug);
            } else {
              console.log("時間対象外");
              dialogsStore.onOpenConfirmDialog({
                title: t("チャット可能時間外"),
                message: t(
                  "申し訳ございませんが、チャット可能時間外です。相談窓口の紹介を進みますか？",
                ),
                confirmButtonText: t("はい、相談窓口の紹介を表示"),
                cancelButtonText: t("いいえ、キャンセルする"),
                confirmButtonColor: "primary",
                onConfirm: () => {
                  this.currentStep = this.informationStep?.step;
                },
              });
            }

            break;
          case "send-mail":
            await this.sendMail(next?.mailTempalteId);
            await this.submitWizard(false, true);
            if (next.value) {
              this.currentStep = next?.value;
            }
            break;
          case "step":
            this.currentStep = next?.value || this.currentStep + 1;
            break;
          case "link":
            if (next?.value === "web-app") {
              if (isChatAvailableTime(route.params.slug as string)) {
                // await this.sendMail(next?.mailTempalteId);
                navigateTo("/web-app/" + route.params.slug);
              } else {
                dialogsStore.onOpenConfirmDialog({
                  title: t("チャット可能時間外"),
                  message: t(
                    "申し訳ございませんが、チャット可能時間外です。相談を申し込みますか？",
                  ),
                  confirmButtonText: t("はい、相談を申し込む"),
                  cancelButtonText: t("いいえ、キャンセルする"),
                  confirmButtonColor: "primary",
                  onConfirm: () => {
                    this.currentStep = this.currentStep + 1;
                    console.log(
                      "🚀 ~ nextStep ~ this.currentStep:",
                      this.currentStep,
                    );
                  },
                });
              }
            } else {
              navigateTo(next?.value);
            }
            break;
          default:
            this.currentStep = next;
            break;
        }
      } else {
        // the step after current step
        const index = this.steps.indexOf(this.currentStep);
        this.currentStep = this.steps[index + 1] as number;
      }
      // Add to stepsHistory
      this.stepsHistory.push(this.currentStep);
    },

    prevStep() {
      const lastStep = this.stepsHistory[this.stepsHistory.length - 2];
      if (lastStep) {
        this.currentStep = lastStep;
        this.stepsHistory.pop();
      } else {
        // the step before current step
        const index = this.steps.indexOf(this.currentStep);
        this.currentStep = this.steps[index - 1] as number;
      }
    },

    async submitWizard(
      needCreateCase = true,
      isStorage = false,
    ): Promise<any | false | undefined> {
      const route = useRoute();
      const webAppStore = useWebAppStore();
      try {
        this.errors.submitWizard = null;
        this.loadings.submitWizard = true;
        console.log("🚀 ~ this.fullName:", this.fullName);

        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useCounseleeService("external-wizard/wizard-results", {
          method: "POST",
          data: {
            slug: route.params.slug,
            fullName: this.fullName || "",
            wizardId: this.wizard?.wizardId,
            wizardName: this.wizard?.wizardName,
            wizardResult: this.wizardResult,
            needCreateCase,
            isStorage,
          },
        });
        if (data.value) {
          const { customer, counselee, case: caseData } = data.value?.body;
          webAppStore.joinChatByWizard(customer, counselee, caseData);
        }
        return data.value;
      } catch (error: any) {
        console.log("🚀 ~ error:", error);
        this.errors.submitWizard = error?.response?.data;
        return false;
      } finally {
        this.loadings.submitWizard = false;
      }
    },

    async sendMail(mailTempalteId: string) {
      const route = useRoute();
      try {
        this.errors.sendMail = null;
        this.loadings.sendMail = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await useCounseleeService("external-wizard/send-email", {
          method: "POST",
          data: {
            slug: route.params.slug,
            mailTempalteId: mailTempalteId,
            wizard: {
              wizardId: this.wizard?.wizardId,
              wizardName: this.wizard?.wizardName,
              wizard: this.wizard?.wizard,
            },
          },
        });
        return true;
      } catch (error: any) {
        this.errors.sendMail = error?.response?.data;
        return false;
      } finally {
        this.loadings.sendMail = false;
      }
    },

    setSpecialValue(value: any, id: string) {
      this.wizard.wizard = this.wizard?.wizard?.map((formElement: any) => {
        if (formElement.id === id) {
          formElement.value = value;
        }
        return formElement;
      });
    },
  },
});
