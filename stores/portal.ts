import type { WizardResult, WizardResultsSearchConditions } from "@/types";

export const usePortalStore = defineStore("portalStore", {
  persist: {
    paths: ["tempToken", "customer"],
    storage: window.sessionStorage,
  },
  state: () => ({
    loadings: {
      login: false,
    } as LoadingDynamic,
    errors: {
      login: null,
    } as ErrorDynamic,
    customer: null as any | null,
    tempToken: null as string | null,
    allCounselees: [] as any[],

    wizardResults: [] as WizardResult[],
    searchConditions: {} as WizardResultsSearchConditions,
    pagination: {
      pageRangeDisplayed: 10,
      page: 1,
    } as Pagination,
    totalWizardResultsCount: 0,
    sortConditions: {} as Sort,
  }),
  getters: {
    isAuthenticated: (state) => {
      return state.tempToken !== null;
    },
    allCounseleesForPalette(state) {
      return state.allCounselees.map((counselee) => {
        return {
          ...counselee,
          id: counselee.counseleeId,
          avatar: {
            src: counselee.pictureUrl,
            alt: counselee.fullname,
          },
          label: counselee.fullname,
        };
      });
    },
    pageFrom: (state) => {
      return (
        (state.pagination.page - 1) * state.pagination.pageRangeDisplayed + 1
      );
    },
    pageTo: (state) => {
      return Math.min(
        state.pagination.page * state.pagination.pageRangeDisplayed,
        state.totalWizardResultsCount,
      );
    },
  },
  actions: {
    async fetchCustomerInfo(slug: string): Promise<any | false | undefined> {
      try {
        this.errors.fetchCustomerInfo = null;
        this.loadings.fetchCustomerInfo = true;
        const {
          data,
        }: {
          data: Ref<any>;
          isLoading: Ref<boolean>;
        } = await usePortalService("/counselee-chat/anonymous/" + slug, {
          method: "GET",
        });

        this.customer = {
          ...(this.customer || {}),
          ...data.value?.body,
          slug: slug,
        };
        const appConfig = useAppConfig();
        appConfig.ui.primary = "cyan";
        return true;
      } catch (error: any) {
        this.errors.fetchCustomerInfo = error?.response?.data;
        return false;
      } finally {
        this.loadings.fetchCustomerInfo = false;
      }
    },

    async login(authPayload: {
      id: string;
      password: string;
    }): Promise<any | false | undefined> {
      try {
        this.errors.login = null;
        this.loadings.login = true;
        const { id, password } = authPayload;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await usePortalService(
          "/external-wizard/wizard-results/basic-auth",
          {
            method: "POST",
            data: { id, password },
          },
        );
        if (data.value) {
          this.tempToken = data.value?.response?.token;
          return data.value;
        }
      } catch (error: any) {
        this.errors.login = error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.login = false;
      }
    },

    logout() {
      const route = useRoute();
      this.tempToken = null;
      this.customer = null;
      this.allCounselees = [];
      this.$reset();
      this.fetchCustomerInfo(route.params.slug as string);
      navigateTo("/portal/" + route.params.slug + "/login");
    },

    async fetchAllCounselees() {
      try {
        this.loadings.fetchAllCounselees = true;
        this.errors.fetchAllCounselees = null;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await usePortalService(
          "external-wizard/wizard-results/counselees",
          {
            method: "GET",
          },
        );
        this.allCounselees = data.value;
        return data.value;
      } catch (e) {
        console.log("🚀 ~ fetchAllCounselees ~ e:", e);
        this.errors.fetchAllCounselees = e;
        return false;
      } finally {
        this.loadings.fetchAllCounselees = false;
      }
    },

    async fetchWizardResults() {
      try {
        this.loadings.fetchWizardResults = true;
        this.errors.fetchWizardResults = null;

        const {
          data,
        }: {
          data: Ref<any>;
        } = await usePortalService("/external-wizard/wizard-results/advanced", {
          method: "GET",
          params: {
            ...this.sortConditions,
            ...this.searchConditions,
            counseleeId: Array.isArray(this.searchConditions.counseleeId)
              ? undefined
              : this.searchConditions.counseleeId
              ? this.searchConditions.counseleeId
              : undefined,
            ...this.pagination,
          },
        });
        this.wizardResults = data.value?.items;
        this.totalWizardResultsCount = data.value?.totalCount;
        return true;
      } catch (e) {
        console.log("🚀 ~ fetchWizardResults ~ e:", e);
        this.errors.fetchWizardResults = e;
      } finally {
        this.loadings.fetchWizardResults = false;
      }
    },

    resetSearchConditions() {
      this.searchConditions = {
        wizardId: [],
        counseleeId: [],
        startDate: "",
        endDate: "",
      };
    },
  },
});
