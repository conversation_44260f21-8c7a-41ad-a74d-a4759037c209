export const useSurveyStore = defineStore("surveyStore", {
  persist: {
    paths: ["user", "authenticated"],
  },
  state: () => ({
    customer: null as any | null,
    loadings: {} as LoadingDynamic,
    errors: {} as ErrorDynamic,
    survey: null as any | null,
    counseleeId: null as string | null,
    submited: false,
  }),
  getters: {},
  actions: {
    async fetchCustomerInfo(
      customerId: string,
    ): Promise<any | false | undefined> {
      const router = useRouter();
      const route = useRoute();
      try {
        const appConfig = useAppConfig();
        const colorMode = useColorMode();

        this.errors.fetchCustomerInfo = null;
        this.loadings.fetchCustomerInfo = true;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/customer", {
          method: "GET",
          params: {
            customerId,
          },
        });

        this.customer = {
          ...data.value?.body?.customer,
          customerId,
        };

        appConfig.ui.primary = "cyan";
        colorMode.preference = "light";
        // router.push({
        //   query: {
        //     ...route.query,
        //     customerId,
        //   },
        // });
        return data.value;
      } catch (error: any) {
        this.errors.fetchCustomerInfo =
          error?.response?.data?.error || error?.message;
        return false;
      } finally {
        this.loadings.fetchCustomerInfo = false;
      }
    },

    async fetchSurvey(
      customerId: string,
      surveyId: string,
      counseleeId: string,
    ): Promise<any | false | undefined> {
      try {
        this.errors.fetchSurvey = null;
        this.loadings.fetchSurvey = true;
        this.counseleeId = counseleeId;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/survey", {
          method: "POST",
          data: {
            surveyId,
            customerId,
          },
        });

        this.survey = data.value?.body?.survey;
        return true;
      } catch (error: any) {
        this.errors.fetchSurvey =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.fetchSurvey = false;
      }
    },

    async submitSurvey(
      formTemplate: any,
      valuesObject: any,
    ): Promise<any | false | undefined> {
      try {
        this.errors.submitSurvey = null;
        this.loadings.submitSurvey = true;
        this.submited = false;
        const {
          data,
        }: {
          data: Ref<any>;
        } = await useCounseleeService("/counselee-chat/survey-result", {
          method: "POST",
          data: {
            surveyId: this.survey?.surveyId,
            customerId: this.customer?.customerId,
            counseleeId: this.counseleeId,
            surveyName: this.survey?.surveyName,
            formTemplate,
            segmentId: "",
          },
        });
        this.submited = true;
        return true;
      } catch (error: any) {
        this.errors.submitSurvey =
          error?.response?.data?.message || error?.message;
        return false;
      } finally {
        this.loadings.submitSurvey = false;
      }
    },

    initialSurveyData(user: any) {
      this.survey.formTemplate = this.survey?.formTemplate.map(
        (element: any) => {
          if (element.id === "name") {
            element.value = user?.displayName;
          }
          return element;
        },
      );
    },
  },
});
