<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });

  import { storeToRefs } from "pinia";
  import { useSurveyStore } from "~/stores/survey";
  const surveyStore = useSurveyStore();
  const { loadings, customer, errors, survey, submited } =
    storeToRefs(surveyStore);
  const route = useRoute();
  onMounted(async () => {
    // アンケートID: sid、カスタマーID: c、相談者ID: u
    const { sid, c, u } = route.query;
    await surveyStore.fetchSurvey(c as string, sid as string, u as string);
  });
</script>

<template>
  <NuxtLayout name="survey">
    <div
      class="relative flex flex-col items-center justify-start bg-gray-100 w-full lg:p-4 min-h-screen overflow-auto"
    >
      <div
        v-if="loadings['fetchSurvey']"
        class="flex flex-col h-full p-6 justify-center items-center space-y-4"
      >
        <Icon icon="eos-icons:loading" class="text-5xl text-gray-500" />
        <div class="text-sm text-gray-500">アンケートを読み込んでいます</div>
      </div>
      <FormSurveyRender
        class="mx-auto max-w-xl w-full"
        v-else-if="survey?.formTemplate"
        :formTemplate="survey?.formTemplate"
        @submit="surveyStore.submitSurvey"
        :submiting="loadings['submitSurvey']"
        :submited="submited"
        :submitError="errors['submitSurvey']"
      />
      <div
        class="flex flex-col h-full p-6 justify-center items-center space-y-4"
        v-else-if="errors['fetchSurvey']"
      >
        <Icon icon="material-symbols:error" class="text-6xl text-red-500" />
        <div class="text-sm text-red-500">
          {{ $t(errors["fetchSurvey"]) }}
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>
