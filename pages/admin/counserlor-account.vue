<template>
  <UCard>
    <template #header>
      <div class="flex justify-between">
        <div>
          <UButton
            icon="i-heroicons-plus"
            size="sm"
            color="primary"
            variant="solid"
            :label="t('Add new')"
            :trailing="false"
            :loading="loadings['addCounselor']"
            @click="onAddNewCounselor"
          />
        </div>
        <div class="flex flex-row space-x-2 flex-1 justify-end">
          <UInput
            class="w-1/3"
            placeholder="組織、名前、カウンセラーIDでフィルター"
            v-model="searchKeyword"
          />
          <AdminStatusSelect v-model="statusFilter" class="w-28" />
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="white"
            label="リロード"
            @click="counselorsStore.fetchCounselors()"
          />
        </div>
      </div>
    </template>
    <UTable
      :rows="counserlorsFiltered"
      :columns="columns"
      :loading="loadings['fetchCounselors']"
    >
      <template #fullName-data="{ row }">
        <div class="flex flex-inline space-x-2 items-center">
          <UAvatar
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-200 dark:bg-gray-400',
              placeholder: 'text-md font-semibold text-gray-700 dark:text-gray-800',
            }"
            :src="row.profileImage"
            :alt="row.fullName"
            size="sm"
          />
          <div>
            <div class="font-semibold">
              {{ row.fullName }}
            </div>
            <div class="text-xs">
              {{ row.email }}
            </div>
          </div>
        </div>
      </template>
      <template #role-data="{ row }">
        <UBadge :label="t(row.role)" :color="getCounselorRoleColor(row.role)" />
      </template>
      <template #loggedInAt-data="{ row }">
        <div class="text-xs w-32 flex-wrap whitespace-break-spaces">
          {{
            row.loggedInAt
              ? formatDate(new Date(row.loggedInAt), "YYYY年MM月DD日 HH:mm")
              : "未ログイン"
          }}
          {{ row.loggedInAt ? `(${fromNow(new Date(row.loggedInAt))})` : "" }}
        </div>
      </template>
      <template #deletedAt-data="{ row }">
        <div v-if="updatingCounselor[row.id]" class="w-full flex justify-center">
          <BaseLoader class="w-6" />
        </div>
        <div v-else class="flex items-center w-fit justify-end">
          <UButton
            icon="i-heroicons-pencil-square-20-solid"
            size="xs"
            color="primary"
            variant="ghost"
            :label="t('Edit')"
            :trailing="false"
            class="border-r dark:border-gray-700"
            @click="onEditCounselor(row)"
            :loading="updatingCounselor[row.counselorId]"
          />
          <!-- <UButton
            :key="row.counselorId"
            v-confirm="{
              title: 'アカウントの削除',
              message: `「${row.fullName}」のアカウントを削除してもよろしいですか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: () => onSwitchCounselorStatus(row),
            }"
            icon="i-heroicons-trash-20-solid"
            size="xs"
            color="red"
            variant="ghost"
            :label="t('Delete')"
            :trailing="false"
            :loading="loadings.deleteCounselor[row.counselorId]"
          /> -->
          <UToggle
            :on-icon="
              loadings.switchCounselorStatus[row.counselorId]
                ? 'i-eos-icons-loading'
                : 'i-heroicons-check-20-solid'
            "
            :off-icon="
              loadings.switchCounselorStatus[row.counselorId]
                ? 'i-eos-icons-loading'
                : 'i-heroicons-x-mark-20-solid'
            "
            :model-value="!row.deletedAt"
            class="ml-3"
            :key="row.counselorId"
            @update:modelValue="onSwitchCounselorStatus(row)"
            :disabled="loadings.switchCounselorStatus[row.counselorId]"
          />
        </div>
      </template>
    </UTable>
  </UCard>
  <CounselorAccountModal
    :show="isShowCounselorModal"
    :is-add-new="isAddNewCounselor"
    :is-view-only="isViewCounselorDetail"
    :counselor="selectedCounselor"
    :error="errors.updateCounselor || errors.addCounselor"
    @update="updateCounselor"
    @add-new="addCounselor"
    @delete="onSwitchCounselorStatus"
    @close="closeCounselorModal"
    @reissue-temporary-password="reissueTemporaryPassword"
  />
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import cloneDeep from "lodash/cloneDeep";
import type { TableColumn, Counselor } from "~/types";
import { useCounselorsStore } from "~/stores/admin/counselors";
import { useCustomersStore } from "~/stores/admin/customers";
const toast = useToast();
const counselorsStore = useCounselorsStore();
const customersStore = useCustomersStore();
const {
  loadings,
  statusFilter,
  updatingCounselor,
  selectedCounselor,
  searchKeyword,
  counserlorsFiltered,
  errors,
} = storeToRefs(counselorsStore);
const { customers } = storeToRefs(customersStore);
const { t } = useI18n();

const columns: TableColumn[] = [
  {
    key: "organizationName",
    label: t("Organization"),
    sortable: true,
  },
  {
    key: "fullName",
    label: t("Name"),
    sortable: true,
  },
  {
    key: "counselorId",
    label: t("Counserlor ID"),
    sortable: true,
  },
  {
    key: "role",
    label: t("Role class"),
    sortable: true,
  },
  {
    key: "loggedInAt",
    label: t("Last login"),
    sortable: true,
  },
  {
    key: "deletedAt",
    label: "状態",
    class: "text-right",
    sortable: true,
  },
];

const isShowCounselorModal = ref(false);
const closeCounselorModal = () => {
  isShowCounselorModal.value = false;
};
const isAddNewCounselor = ref(false);
const onAddNewCounselor = () => {
  errors.value.addCounselor = null;
  errors.value.updateCounselor = null;
  selectedCounselor.value = null;
  isAddNewCounselor.value = true;
  isViewCounselorDetail.value = false;
  isShowCounselorModal.value = true;
};

const isViewCounselorDetail = ref(false);
const onEditCounselor = (counselor: Counselor) => {
  selectedCounselor.value = cloneDeep(counselor);
  errors.value.addCounselor = null;
  errors.value.updateCounselor = null;
  isAddNewCounselor.value = false;
  isViewCounselorDetail.value = false;
  isShowCounselorModal.value = true;
};
const dialogsStore = useDialogsStore();
const onSwitchCounselorStatus = async (counselor: Counselor) => {
  const action = counselor.deletedAt ? "有効" : "無効";
  dialogsStore.onOpenConfirmDialog({
    title: `アカウント${action}の確認`,
    message: `「${counselor.fullName}」のアカウントを${action}にしてもよろしいですか？`,
    confirmButtonText: `はい、${action}にする`,
    cancelButtonText: "いいえ",
    confirmButtonColor: counselor.deletedAt ? "green" : "red",
    onConfirm: async () => {
      const result = await counselorsStore.switchCounselorStatus(counselor);

      if (result) {
        toast.add({
          title: "成功",
          description: `「${counselor.fullName}」のアカウントを${action}にしました。`,
          icon: "i-heroicons-check-circle",
        });
      }
    },
  });
};

const updateCounselor = async (counselor: Counselor) => {
  const result = await counselorsStore.updateCounselor(counselor);

  if (result) {
    toast.add({
      title: t("Update success"),
      description: t("Your changes have been successfully saved"),
      icon: "i-heroicons-check-circle",
    });

    // close modal
    closeCounselorModal();
  }
};

const reissueTemporaryPassword = async (counselor: Counselor) => {
  const result = await counselorsStore.reissueTemporaryPassword(counselor);

  if (result) {
    toast.add({
      title: "一時パスワード再発行が成功しました",
      description: `「${counselor.fullName}」の一時パスワードを再発行しました。`,
      icon: "i-heroicons-check-circle",
    });
  }
  // close modal
  closeCounselorModal();
};

const addCounselor = async (counselor: Counselor) => {
  const result = await counselorsStore.addCounselor(counselor);

  if (result) {
    toast.add({
      title: t("Add new success"),
      description: t("Add new consulting account successfully"),
      icon: "i-heroicons-check-circle",
    });
    // close modal
    closeCounselorModal();
  }
};
</script>
