<template>
  <UCard>
    <UTable :rows="permissionsTableRows" :columns="columns">
      <template #admin-data="{ row }">
        <UToggle size="sm" :model-value="row.admin" />
      </template>
      <template #supervisor-data="{ row }">
        <UToggle size="sm" :model-value="row.supervisor" />
      </template>
      <template #general-data="{ row }">
        <UToggle size="sm" :model-value="row.general" />
      </template>
      <template #viewer-data="{ row }">
        <UToggle size="sm" :model-value="row.viewer" />
      </template>
    </UTable>
  </UCard>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import cloneDeep from "lodash/cloneDeep";
  import type { TableColumn } from "~/types";
  import { usePermissionsStore } from "~/stores/permissions";
  const permissionsStore = usePermissionsStore();
  const {} = storeToRefs(permissionsStore);
  const { t } = useI18n();
  const { permissionsTableRows, permissionsList } =
    storeToRefs(permissionsStore);
  const columns: TableColumn[] = [
    {
      key: "permission",
      label: "権限",
      sortable: true,
    },
    {
      key: "admin",
      label: "管理者",
      sortable: true,
    },
    {
      key: "supervisor",
      label: "スーパーバイザー",
      sortable: true,
    },
    {
      key: "general",
      label: "一般",
      sortable: true,
    },
    {
      key: "viewer",
      label: "閲覧",
      sortable: true,
    },
  ];
</script>
