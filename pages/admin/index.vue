<template>
  <UCard>
    <template #header>
      <div class="flex justify-between">
        <div>
          <UButton
            icon="i-heroicons-plus"
            size="sm"
            color="primary"
            variant="solid"
            :label="$t('Add new')"
            :trailing="false"
            :loading="loadings['addCustomerAccount']"
            @click="onAddNewCustomerAccount"
          />
        </div>
        <div class="flex flex-row space-x-2 flex-1 justify-end">
          <UInput
            class="w-1/3"
            placeholder="カスタマー名でフィルター"
            v-model="searchKeyword"
          />
          <AdminStatusSelect v-model="statusFilter" class="w-28" />
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="white"
            label="リロード"
            @click="customersStore.fetchCustomerAccounts()"
          />
        </div>
      </div>
    </template>
    <UTable
      :rows="customersFiltered"
      :columns="columns"
      :loading="loadings['fetchCustomerAccounts']"
    >
      <template #name-data="{ row }">
        <div class="flex flex-inline space-x-2 items-center">
          <UAvatar
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-200 dark:bg-gray-400',
              placeholder:
                'text-md font-semibold text-gray-700 dark:text-gray-800',
            }"
            :src="row.basic.customerImage"
            :alt="row.basic.customerName"
            size="sm"
          />
          <div class="font-semibold">
            {{ row.basic.customerName }}
          </div>
        </div>
      </template>
      <template #snsChannels-data="{ row }">
        <div class="flex flex-inline space-x-2">
          <template v-for="channel in row.activeChannels" :key="channel">
            <UTooltip
              v-if="row[channel]"
              :text="t(channel)"
              :popper="{ placement: 'top' }"
            >
              <component :is="getSNSIconComponent(channel)" class="h-5 w-5" />
            </UTooltip>
          </template>
        </div>
      </template>
      <template #numOfUsers-data="{ row }">
        <div class="font-semibold">
          {{ row.counserlors?.length || 0 }}
        </div>
      </template>
      <template #counserlors-data="{ row }">
        <div class="font-semibold">
          {{ row.roles?.length || 0 }}
        </div>
      </template>
      <template #deletedAt-data="{ row }">
        <div v-if="updatingCustomer[row.id]" class="w-full flex justify-center">
          <BaseLoader class="w-6" />
        </div>
        <div v-else class="flex items-center w-fit justify-end">
          <UButton
            icon="i-heroicons-user-group"
            size="xs"
            color="gray"
            variant="ghost"
            label="カウンセラーの紐付け"
            :trailing="false"
            @click="onEditCustomerCounserlors(row)"
          />
          <UButton
            icon="i-heroicons-pencil-square-20-solid"
            size="xs"
            color="primary"
            variant="ghost"
            :label="t('Edit')"
            :trailing="false"
            @click="onEditCustomerAccount(row)"
            :loading="updatingCustomer[row.customerId]"
          />
          <!-- <UButton
            :key="row.customerId"
            v-confirm="{
              title: 'カスタマーの削除',
              message: `「${row.name}」のカスタマーを削除してもよろしいですか？`,
              confirmButtonText: 'はい、削除する',
              cancelButtonText: 'いいえ',
              onConfirm: () => onDeleteCustomerAccount(row),
            }"
            icon="i-heroicons-trash-20-solid"
            size="xs"
            color="red"
            variant="ghost"
            :label="t('Delete')"
            :trailing="false"
            :loading="loadings.deleteCustomerAccount[row.customerId]"
          /> -->
          <UToggle
            :on-icon="
              loadings.switchCustomerAccountStatus[row.customerId]
                ? 'i-eos-icons-loading'
                : 'i-heroicons-check-20-solid'
            "
            :off-icon="
              loadings.switchCustomerAccountStatus[row.customerId]
                ? 'i-eos-icons-loading'
                : 'i-heroicons-x-mark-20-solid'
            "
            :model-value="!row.deletedAt"
            class="ml-3"
            :key="row.customerId"
            @update:modelValue="onDeleteCustomerAccount(row)"
            :disabled="loadings.switchCustomerAccountStatus[row.customerId]"
          />
        </div>
      </template>
    </UTable>
  </UCard>
  <CustomerAccountModal
    :show="isShowCustomerAccountModal"
    :is-add-new="isAddNewCustomerAccount"
    :customer="selectedCustomer"
    :error="errors.addCustomerAccount || errors.updateCustomerAccount"
    @update="updateCustomerAccount"
    @add-new="addCustomerAccount"
    @close="closeCustomerAccountModal"
  />
  <CustomerCounserlorsModal
    :show="isShowCustomerCounserlorsModal"
    :customer="selectedCustomer"
    @update="updateCustomerCounselors"
    @close="closeCustomerCounserlorsModal"
  />
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import cloneDeep from "lodash/cloneDeep";
  import type { TableColumn, Customer, Counselor } from "~/types";
  import { useCustomersStore } from "~/stores/admin/customers";
  const toast = useToast();
  const customersStore = useCustomersStore();
  const {
    loadings,
    customers,
    searchKeyword,
    customersFiltered,
    selectedCustomer,
    updatingCustomer,
    statusFilter,
    errors,
  } = storeToRefs(customersStore);
  const { t } = useI18n();

  const columns: TableColumn[] = [
    {
      key: "name",
      label: t("Customer name"),
      sortable: true,
    },
    {
      key: "snsChannels",
      label: "チャンネル",
      sortable: true,
    },
    {
      key: "counseleeCount",
      label: t("Number of counselees"),
      sortable: true,
    },
    {
      key: "counserlors",
      label: "カウンセラーの紐付け",
      sortable: true,
    },
    {
      key: "deletedAt",
      label: "状態",
      class: "w-0 text-right",
      sortable: true,
    },
  ];

  const isShowCustomerAccountModal = ref(false);
  const closeCustomerAccountModal = () => {
    isShowCustomerAccountModal.value = false;
  };

  const isShowCustomerCounserlorsModal = ref(false);
  const closeCustomerCounserlorsModal = () => {
    isShowCustomerCounserlorsModal.value = false;
  };

  const isAddNewCustomerAccount = ref(false);
  const onAddNewCustomerAccount = () => {
    errors.value.addCustomerAccount = null;
    errors.value.updateCustomerAccount = null;
    selectedCustomer.value = null;
    isAddNewCustomerAccount.value = true;
    isShowCustomerAccountModal.value = true;
  };

  const onEditCustomerAccount = (customerAccount: Customer) => {
    const _customerAccount = customers.value.find(
      (c) => c.customerId === customerAccount.customerId,
    ) as Customer;
    errors.value.addCustomerAccount = null;
    errors.value.updateCustomerAccount = null;
    selectedCustomer.value = cloneDeep(_customerAccount);
    isAddNewCustomerAccount.value = false;
    isShowCustomerAccountModal.value = true;
  };

  const onEditCustomerCounserlors = (customerAccount: Customer) => {
    const _customerAccount = customers.value.find(
      (c) => c.customerId === customerAccount.customerId,
    ) as Customer;
    selectedCustomer.value = cloneDeep(_customerAccount);

    isAddNewCustomerAccount.value = false;
    isShowCustomerCounserlorsModal.value = true;
  };
  const dialogsStore = useDialogsStore();
  const onDeleteCustomerAccount = async (customerAccount: Customer) => {
    const action = customerAccount.deletedAt ? "有効" : "無効";
    dialogsStore.onOpenConfirmDialog({
      title: `カスタマーアカウント${action}の確認`,
      message: `「${customerAccount.basic.customerName}」のカスタマーアカウントを${action}にしてもよろしいですか？`,
      confirmButtonText: `はい、${action}にする`,
      cancelButtonText: "いいえ",
      confirmButtonColor: customerAccount.deletedAt ? "green" : "red",
      onConfirm: async () => {
        const result = await customersStore.switchCustomerAccountStatus(
          customerAccount,
        );

        if (result) {
          toast.add({
            title: "成功",
            description: `「${customerAccount.basic.customerName}」のカスタマーアカウントを${action}にしました。`,
            icon: "i-heroicons-check-circle",
          });
        }
      },
    });
  };

  const updateCustomerAccount = async (customerAccount: Customer) => {
    delete customerAccount.activeChannels;
    delete customerAccount.counseleeCount;
    const result = await customersStore.updateCustomerAccount(customerAccount);

    if (result) {
      toast.add({
        title: t("Update success"),
        description: t("Your changes have been successfully saved"),
        icon: "i-heroicons-check-circle",
      });
      closeCustomerAccountModal();
    }
  };

  const addCustomerAccount = async (customerAccount: Customer) => {
    const result = await customersStore.addCustomerAccount(customerAccount);

    if (result) {
      toast.add({
        title: t("Add new success"),
        description: "カスタマーを追加しました",
        icon: "i-heroicons-check-circle",
      });
      // close modal
      closeCustomerAccountModal();
    }
  };

  const updateCustomerCounselors = async (counserlors: Counselor[]) => {
    const result = await customersStore.updateCustomerCounselors(
      selectedCustomer.value as Customer,
      counserlors,
    );

    if (result) {
      toast.add({
        title: t("Update success"),
        description: "カウンセラーの紐付けを更新しました",
        icon: "i-heroicons-check-circle",
      });
    }
  };
</script>
