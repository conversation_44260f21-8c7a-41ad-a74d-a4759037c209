<script setup lang="ts">
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useWebAppStore } from "~/stores/web-app";
  const route = useRoute();
  const toast = useToast();
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });
  const webAppStore = useWebAppStore();
  const { loadings, errors, customer } = storeToRefs(webAppStore);
  const { object, string, boolean } = useYup();
  const { t } = useI18n();
  const schema = object({
    passCode: string().min(6),
  });

  type Schema = InferType<typeof schema>;

  const state = reactive({
    passCode: "",
  });
  async function onSubmit(event: FormSubmitEvent<Schema>) {
    // Do something with event.data
    const result = await webAppStore.joinChatByCode(
      route.params.slug as string,
      state.passCode,
    );
    if (result) {
      toast.add({
        title: "参加しました！",
        description: "SNS相談システムへようこそ！",
        icon: "i-heroicons-check-circle",
      });
      await navigateTo("/web-app/" + customer.value?.slug);
    }
  }
  onMounted(() => {
    const { code } = route.query;
    // check if code is valid with schema
    const result = schema.validateSync({ passCode: code });
    if (result) {
      state.passCode = code as string;
    }
  });
</script>

<template>
  <div>
    <NuxtLayout name="web-app-blank">
      <div class="flex flex-row space-x-2 mb-6">
        <SystemLogo class="mt-5" />
      </div>
      <div
        class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700"
      >
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1
            class="text-lg font-bold leading-tight tracking-tight text-gray-900 md:text-xl dark:text-white"
          >
            {{
              customer?.showBrowserAccessCode ? $t("相談に参加する") : $t("相談する")
            }}
          </h1>
          <div v-if="customer?.showBrowserAccessCode" class="text-sm">
            相談に参加するには、相談者から送られてきたコードを入力してください。
          </div>
          <div v-else class="text-sm">
            {{ $t("相談の開始には、下記のボタンをクリックしてください。") }}
          </div>
          <UForm
            class="space-y-10"
            :schema="schema"
            :state="state"
            @submit="onSubmit"
          >
            <UFormGroup v-if="customer?.showBrowserAccessCode" name="passCode">
              <BaseCodeInput v-model="state.passCode" />
            </UFormGroup>

            <UAlert
              v-if="errors.joinChatByCode"
              icon="i-heroicons-exclamation-triangle"
              :description="$t(errors.joinChatByCode)"
              :title="$t('Input code failed')"
              color="red"
              variant="subtle"
            />
            <UButton
              size="xl"
              :ui="{
                rounded: 'rounded-full',
              }"
              icon="i-heroicons-arrow-right"
              block
              :label="
                customer?.showBrowserAccessCode ? $t('次へ') : $t('相談を開始')
              "
              type="submit"
              :loading="loadings.joinChatByCode"
              :trailing="true"
              :disabled="
                customer?.showBrowserAccessCode &&
                (state?.passCode?.length < 6 || !state?.passCode)
              "
            />
          </UForm>
        </div>
      </div>
    </NuxtLayout>
  </div>
</template>
