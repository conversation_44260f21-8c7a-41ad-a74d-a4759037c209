<script setup lang="ts">
  definePageMeta({
    layout: false,
    noNeedAuth: true,
    middleware: "auth-web-app",
  });
  import { useSocketStore } from "~/stores/socket";
  import { storeToRefs } from "pinia";
  import { useWebAppStore } from "~/stores/web-app";
  const route = useRoute();
  const webAppStore = useWebAppStore();
  const { loadings, customer, messages, counselee, code } =
    storeToRefs(webAppStore);
  const messageText = ref<string>("");
  const onSend = (message: string) => {
    webAppStore.sendMessage(message);
  };
  const endChat = ref<boolean>(false);
  const onEndChat = () => {
    webAppStore.endChat();
    endChat.value = true;
    window.location.href = "/web-app/" + route.params.slug + "/code-input";
  };
  const socketStore = useSocketStore();
  const { webAppSocket } = storeToRefs(socketStore);
  onMounted(() => {
    socketStore.webAppConnectSocket(counselee.value?.counseleeId);
  });

  const reload = () => {
    window.location.reload();
  };

  onBeforeRouteLeave(() => {
    if (!endChat.value) {
      return confirm(
        "セキュリティ上の理由から、画面を更新したら、過去のチャットが見えなくなりますが、よろしいですか？",
      );
    }
  });
</script>

<template>
  <NuxtLayout name="web-app">
    <div
      class="h-full flex flex-col bg-slate-50/95 w-full max-w-xl rounded-t-2xl"
    >
      <WebAppChatHeader :user="customer" class="w-full">
        <template #right>
          <UButton
            :label="$t('相談終了')"
            class="px-3"
            size="xs"
            square
            color="red"
            variant="soft"
            :ui="{
              rounded: 'rounded-full',
            }"
            v-confirm="{
              title: $t('相談終了'),
              message: $t('相談を終了しますか？'),
              confirmButtonText: $t('はい、終了する'),
              cancelButtonText: $t('いいえ'),
              onConfirm: () => onEndChat(),
            }"
          />
        </template>
      </WebAppChatHeader>
      <div v-if="messages.length === 0" class="h-full w-full px-3">
        <div class="flex flex-col items-center h-full justify-center space-y-4">
          <UIcon name="i-entypo-chat" class="text-gray-400/95 text-6xl" />
          <div class="text-center text-gray-500 font-thin pb-1">
            {{ $t("まだメッセージはありません。") }}<br />
            {{ $t("ご相談をお待ちしております。") }}
          </div>
        </div>
      </div>
      <WebAppChatMessageList
        v-else
        class="h-full w-full px-4 pt-4 pb-32"
        :messages="messages"
        :loading="loadings.fetchMessages"
      />
      <div class="text-xs text-yellow-600 text-center py-1">
        <UIcon name="i-twemoji-warning" />
        {{ $t("セキュリティ上の理由から、画面を更新したら、過去のチャットが見えなくなります") }}
      </div>
      <WebAppChatMessageBox
        class="px-4 pb-2 w-full"
        v-model="messageText"
        @send="onSend"
      />
      <div class="text-center text-gray-500 text-xs font-light pb-1">
        Powered by {{ $t("system_name") }}
      </div>
    </div>
  </NuxtLayout>
</template>
