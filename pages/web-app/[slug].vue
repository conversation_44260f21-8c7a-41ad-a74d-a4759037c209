<script setup lang="ts"></script>

<template>
  <div>
    <NuxtPage />
  </div>
</template>
<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useWebAppStore } from "~/stores/web-app";

  const webAppStore = useWebAppStore();

  const { loadings, customer, errors } = storeToRefs(webAppStore);
  const route = useRoute();
  onMounted(() => {
    webAppStore.fetchCustomerInfo(route.params.slug);
  });

  watch(
    () => errors.value,
    (error) => {
      console.log("🚀 ~ error:", error);
      if (error.fetchCustomerInfo) {
        navigateTo("/web-app/not-found");
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
