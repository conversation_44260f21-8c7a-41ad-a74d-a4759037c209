<script setup lang="ts">
  import { useCustomersStore } from "~/stores/admin/customers";
  import { useCounselorsStore } from "~/stores/admin/counselors";
  import { useAuthStore } from "~/stores/auth";
  import { storeToRefs } from "pinia";
  import { Icon } from "@iconify/vue";
  const authStore = useAuthStore();
  const customersStore = useCustomersStore();
  const counselorsStore = useCounselorsStore();
  const { loadings } = storeToRefs(authStore);
  definePageMeta({
    layout: false,
    middleware: ["auth", "permissions"],
  });

  const appConfig = useAppConfig();
  if (process.client) {
    appConfig.ui.primary = "cyan";
  }

  onMounted(async () => {
    await authStore.fetchUserMe();
    counselorsStore.fetchCounselors();
    customersStore.fetchCustomerAccounts();
  });
</script>

<template>
  <div
    v-if="loadings['fetchUserMe']"
    class="bg-primary-800 dark:bg-gray-900 flex flex-col space-y-2 w-full h-screen justify-center items-center text-white"
  >
    <div class="flex flex-row space-x-2">
      <!-- <app-logo class="max-w-[222px] mb-2" /> -->

      <SystemLogo class="mt-6" />
    </div>

    <Icon icon="eos-icons:loading" class="text-4xl" />
    <div class="text-center">只今準備中です。もうしばらくお待ちください。</div>
  </div>
  <div v-else>
    <NuxtLayout name="admin">
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>
