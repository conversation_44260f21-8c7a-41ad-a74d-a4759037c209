<script setup lang="ts"></script>

<template>
  <div v-if="notFound">
    <div class="flex flex-row space-x-2 mb-6 justify-center">
      <SystemLogo class="mt-5" />
    </div>
    <div
      class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-lg xl:p-0 dark:bg-gray-800 dark:border-gray-700"
    >
      <div class="p-6 space-y-4 md:space-y-6 sm:p-8 text-center">
        <h1
          class="text-lg font-bold leading-tight tracking-tight text-red-500 md:text-xl dark:text-red-200"
        >
          404 Not Found
        </h1>

        <div class="text-sm">
          申し訳ございません。お探しのページが見つかりませんでした。<br />
          指定されたURLが正しいかご確認ください。
        </div>
      </div>
    </div>
  </div>
  <div
    v-else-if="loadings['fetchCustomerInfo']"
    class="flex flex-col h-screen p-6 justify-center items-center space-y-4"
  >
    <Icon icon="eos-icons:loading" class="text-5xl text-gray-500" />
    <div class="text-sm text-gray-500">データを読み込んでいます</div>
  </div>
  <NuxtPage v-else />
</template>
<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  const portalStore = usePortalStore();

  const { loadings, customer, errors } = storeToRefs(portalStore);
  const route = useRoute();
  onMounted(() => {
    portalStore.fetchCustomerInfo(route.params.slug);
  });
  const notFound = ref(false);
  watch(
    () => errors.value,
    (error) => {
      if (error.fetchCustomerInfo) {
        notFound.value = true;
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
