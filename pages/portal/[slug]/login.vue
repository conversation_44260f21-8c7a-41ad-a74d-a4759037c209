<script setup lang="ts">
  import { type InferType } from "yup";
  import type { FormSubmitEvent } from "#ui/types";
  const toast = useToast();
  definePageMeta({
    noNeedAuth: true,
  });

  const portalStore = usePortalStore();
  const { loadings, errors, customer } = storeToRefs(portalStore);
  const { object, string } = useYup();
  const { t } = useI18n();
  const schema = object({
    id: string().min(6).required(),
    password: string().min(8).required(),
  });

  type Schema = InferType<typeof schema>;

  const state = reactive({
    id: undefined as string | undefined,
    password: undefined,
  });
  async function onSubmit(event: FormSubmitEvent<Schema>) {
    // Do something with event.data
    const loginResult = await portalStore.login(event.data);
    if (loginResult) {
      navigateTo("/portal/" + customer.value?.slug);
    }
  }
  const showPassword = ref(false);
</script>

<template>
  <div class="w-full max-w-lg">
    <div class="flex flex-row space-x-2 mb-6 justify-center">
      <!-- <app-logo class="max-w-[222px] mb-2" /> -->

      <SystemLogo class="mt-5" />
    </div>
    <div
      class="w-full bg-white backdrop-blur rounded-lg shadow dark:border md:mt-0 xl:p-0 dark:bg-gray-800 dark:border-gray-700"
    >
      <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
        <div class="flex flex-row space-x-1 items-center justify-center">
          <UAvatar
            v-bind="{
              src: customer?.avatar,
              alt: customer?.name,
            }"
            size="lg"
            loading="lazy"
            :ui="{
              rounded: 'rounded-lg',
              background: 'bg-gray-300 dark:bg-gray-400',
              placeholder:
                'text-xs font-semibold text-gray-700 dark:text-gray-800',
            }"
          />
          <div class="text-xl">
            {{ customer?.name }}
          </div>
          <h1
            class="text-lg font-bold leading-tight tracking-tight text-gray-900 md:text-xl dark:text-white"
          >
            ポータル
          </h1>
        </div>

        <UForm
          class="space-y-6"
          :schema="schema"
          :state="state"
          @submit="onSubmit"
        >
          <UFormGroup :label="$t('Login ID')" required name="id">
            <UInput
              v-model="state.id"
              :placeholder="$t('Your login ID')"
              icon="i-heroicons-identification"
              size="lg"
            />
          </UFormGroup>

          <UFormGroup :label="$t('Password')" required name="password">
            <UInput
              v-model="state.password"
              :placeholder="$t('Your password')"
              icon="i-heroicons-lock-closed"
              size="lg"
              :type="showPassword ? 'text' : 'password'"
              autocomplete="off"
              :ui="{ icon: { trailing: { pointer: '' } } }"
            >
              <template #trailing>
                <UButton
                  :padded="false"
                  @click="showPassword = !showPassword"
                  color="gray"
                  variant="link"
                  :icon="
                    showPassword
                      ? 'i-heroicons-eye-slash-solid'
                      : 'i-heroicons-eye-solid'
                  "
                />
              </template>
            </UInput>
          </UFormGroup>
          <UAlert
            v-if="errors.login"
            icon="i-heroicons-exclamation-triangle"
            :description="$t(errors.login)"
            :title="$t('Login failed')"
            color="red"
            variant="subtle"
          />
          <UButton
            block
            :label="$t('Login')"
            type="submit"
            :loading="loadings.login === true"
          />
        </UForm>
      </div>
    </div>
  </div>
</template>
