<template>
  <UContainer class="w-full">
    <main
      class="w-full relative flex flex-col h-auto min-h-screen transition-all duration-200 space-y-6"
    >
      <div class="flex flex-col w-full h-full">
        <div
          class="w-full max-w-screen-xl px-5 mx-auto py-2 border-b flex flex-row items-center justify-between"
        >
          <div class="flex flex-row space-x-1 items-center">
            <UAvatar
              v-bind="{
                src: customer?.avatar,
                alt: customer?.name,
              }"
              size="lg"
              loading="lazy"
              :ui="{
                rounded: 'rounded-lg',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-xs font-semibold text-gray-700 dark:text-gray-800',
              }"
            />
            <div class="text-xl text-primary-500">
              {{ $t(customer?.name) }}
            </div>
          </div>
          <UButton
            color="gray"
            trailing-icon="i-material-symbols-logout"
            variant="solid"
            :ui="{
              rounded: 'rounded-full',
            }"
            @click="portalStore.logout"
          >
            <template #leading>
              <UIcon
                name="i-mingcute-user-4-fill"
                class="flex-shrink-0 h-5 w-5"
              />
            </template>
            <span> ログアウト </span>
          </UButton>
        </div>

        <div class="px-6">
          <PortalWizardResultsSearchForm />
        </div>
        <div class="px-6">
          <BaseTable
            title="相談内容一覧"
            :pagination="pagination"
            :page-from="pageFrom"
            :page-to="pageTo"
            @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
            @update:page="(value: number) => (pagination.page = value)"
            :total="totalWizardResultsCount"
          >
            <template #action>
              <UButton
                size="xs"
                variant="solid"
                icon="i-heroicons-arrow-path-solid"
                color="gray"
                label="リロード"
                @click="portalStore.fetchWizardResults()"
              />
            </template>
            <UTable
              :columns="columns"
              :rows="wizardResults"
              v-model:sort="sort"
              sort-mode="manual"
              sort-asc-icon="i-heroicons-arrow-up"
              sort-desc-icon="i-heroicons-arrow-down"
              :loading="loadings['fetchWizardResults'] === true"
            >
              <template #wizardName-data="{ row }">
                <div>
                  <ULink
                    @click="
                      detailWizardResult = row;
                      isOpenDetailModal = true;
                    "
                    inactive-class="text-primary-500 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-200"
                  >
                    {{ $t(row.wizardName) }}
                  </ULink>
                </div>
              </template>
              <template #counseleeName-data="{ row }">
                <div class="flex flex-row items-center space-x-2">
                  <UAvatar
                    size="xs"
                    :src="row.counseleeAvatar"
                    :alt="row.counseleeName"
                  />
                  <div>
                    <ULink
                      inactive-class="dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                    >
                      {{ row.counseleeName }}
                    </ULink>
                  </div>
                </div>
              </template>
              <template #createdAt-data="{ row }">
                <div>
                  <span>
                    {{
                      formatDate(
                        new Date(row.createdAt),
                        "YYYY年MM月DD日 HH時mm分",
                      )
                    }}
                  </span>
                </div>
              </template>
            </UTable>
          </BaseTable>
        </div>
      </div>

      <BaseCopyright
        class="mt-4 flex flex-row items-center justify-center"
        textClass="!text-gray-800 dark:!text-gray-300"
        wrapClass="bg-transparent"
      />
    </main>
    <WizardResultDetailModal
      :isOpen="isOpenDetailModal"
      :wizardResult="detailWizardResult"
      @close="isOpenDetailModal = false"
    />
  </UContainer>
</template>

<script setup lang="ts">
  import type { WizardResult } from "@/types";
  const portalStore = usePortalStore();
  const {
    customer,
    loadings,
    pagination,
    wizardResults,
    pageTo,
    pageFrom,
    sortConditions,
    totalWizardResultsCount,
    tempToken
  } = storeToRefs(portalStore);

  const columns = [
    {
      key: "wizardName",
      label: "ウィザード名",
      sortable: true,
    },
    {
      key: "counseleeName",
      label: "相談者",
      sortable: true,
    },
    {
      key: "createdAt",
      label: "作成日時",
      sortable: true,
    },
  ];
  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const isOpenDetailModal = ref(false);
  const detailWizardResult = ref(null) as Ref<WizardResult | null>;
</script>
