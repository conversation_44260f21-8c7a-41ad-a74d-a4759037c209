<template>
  <div class="flex flex-col h-screen">
    <AppPageHeader title="集計" />
    <div class="p-4">
      <ReportsSearchForm v-model="period" />
    </div>
    <!-- <UDivider color="gray" /> -->
    <div class="h-full px-4 pt-0 py-2">
      <ReportsSelectColumns
        class="h-full"
        :columns="avaiables"
        v-model="selected"
        @export="onExport"
        :loading="loadings.exportConsultationLogs"
      >
        <!-- <template #action-options>
          <UButton
            icon="i-heroicons-plus"
            size="xs"
            color="primary"
            variant="outline"
            label="項目追加"
            :trailing="false"
            @click="isOpenCaseTagsPalette = true"
          ></UButton>
        </template> -->
        <template #action>
          <!-- <UButton
            :trailing="true"
            @click="onExport"
            :loading="loadings['exportConsultationLogs']"
            :disabled="selected.length === 0"
          >
            エクスポート(時間)
          </UButton>
          <UButton
            :trailing="true"
            @click="onExport"
            :loading="loadings['exportConsultationLogs']"
            :disabled="selected.length === 0"
          >
            エクスポート(日)
          </UButton> -->
          <UButton
            :trailing="true"
            @click="onExport"
            :loading="loadings['exportConsultationLogs']"
            :disabled="selected.length === 0"
            icon="i-heroicons-arrow-right"
          >
            エクスポート
          </UButton>
        </template>
      </ReportsSelectColumns>
    </div>
  </div>

  <AppCaseTagsPalette
    :is-open="isOpenCaseTagsPalette"
    @close="isOpenCaseTagsPalette = false"
    @select="onSelectCaseTag"
  />
</template>

<script setup lang="ts">
  import { useReportsStore } from "~/stores/app/reports";
  import { storeToRefs } from "pinia";
  import type { CaseTag } from "~/types";
  import { ReportExportType } from "~/types/enums.d";

  const toast = useToast();
  const reportsStore = useReportsStore();
  const { loadings } = storeToRefs(reportsStore);

  const avaiables = ref([
    {
      value: "allCaseCount",
      label: "相談件数",
      selected: false,
    },
    {
      value: "beforeStartCaseCount",
      label: "対応件数",
      selected: false,
    },
    {
      value: "inProgressOrCompletionCaseCount",
      label: "未対応件数",
      selected: false,
    },
    {
      value: "customerId",
      label: "カスタマーID",
      selected: false,
    },
    {
      value: "customerName",
      label: "カスタマー名",
      selected: false,
    },
  ]);

  const period = ref({
    type: "this_month",
    value: {
      from: null,
      to: null,
    },
  });
  const selected = ref([]);

  const onExport = async () => {
    console.log("export");
    const result = await reportsStore.exportConsultationLogs(
      period.value,
      selected.value,
      ReportExportType.TOTALLING,
    );

    if (result) {
      toast.add({
        icon: "i-heroicons-check-circle",
        color: "green",
        title: "エクスポート成功",
        description:
          "エクスポートは処理中です。処理ステータスはエクスポート履歴で確認できます。",
        actions: [
          {
            label: "エクスポート履歴",
            click: () => {
              navigateTo("/app/reports");
            },
            color: "green",
          },
        ],
      });
    }
  };

  const isOpenCaseTagsPalette = ref(false);
  const onSelectCaseTag = (caseTag: CaseTag) => {
    // check if already selected
    const column = avaiables.value.find((item) => item.value === caseTag.tagId);
    if (column) {
      column.selected = true;
      isOpenCaseTagsPalette.value = false;
      return;
    }
    avaiables.value.push({
      value: caseTag.tagId as string,
      label: caseTag.tagName,
      selected: true,
    });
    isOpenCaseTagsPalette.value = false;
  };
</script>
