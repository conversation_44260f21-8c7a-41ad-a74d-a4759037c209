<template>
  <div>
    <div
      class="sticky top-0 z-50 pt-3 bg-gray-100 dark:bg-gray-800 pb-2 px-4 border-b dark:border-gray-700 font-semibold text-md flex flex-inline justify-between"
    >
      <div class="truncate">エクスポート履歴</div>
    </div>
    <div class="p-4">
      <!-- <ReportsSearchForm v-model="period">
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-magnifying-glass"
            color="primary"
            label="検索"
            class="px-8"
            @click="reportsStore.fetchReports(period)"
          />
        </template>
      </ReportsSearchForm> -->
    </div>
    <div class="px-6">
      <BaseTable
        title="エクスポート履歴一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalReportsCount"
      >
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="reportsStore.fetchReports()"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="reports"
          v-model:sort="sort"
          :loading="loadings['fetchReports']"
        >
          <template #exportType-data="{ row }">
            <div>
              <span>
                {{ $t(row.exportType) }}
              </span>
            </div>
          </template>
          <template #outputColumnNames-data="{ row }">
            <div>
              <span>
                {{ getColsExported(row) }}
                カラム
              </span>
            </div>
            <div class="text-xs">
              {{ row.surveyName }}
              <span v-if="row.surveyOutputColumnNames?.length"
                >({{ row.surveyOutputColumnNames?.length }} カラム)</span
              >
            </div>
          </template>
          <template #startDate-data="{ row }">
            <div class="flex flex-inline space-x-1 items-center">
              <span> {{ formatDate(new Date(row.startDate)) }} </span>
              <UIcon name="i-heroicons-arrow-small-right" />
              <span> {{ formatDate(new Date(row.endDate)) }} </span>
            </div>
          </template>

          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{
                  formatDate(new Date(row.createdAt), "YYYY年MM月DD日 HH時mm分")
                }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                v-if="row.status === ReportExportStatus.DOWNLOADABLE"
                icon="i-heroicons-arrow-down-tray"
                size="xs"
                color="green"
                variant="solid"
                block
                label="ダウンロード"
                :trailing="false"
                @click="() => onDownload(row)"
              />
              <UButton
                v-else-if="row.status === ReportExportStatus.NOW_CREATING"
                icon="i-ep-loading"
                size="xs"
                color="gray"
                variant="solid"
                label="作成中"
                block
                :trailing="false"
                disabled
              />
              <UButton
                v-else-if="row.status === ReportExportStatus.ERROR"
                icon="i-heroicons-exclamation-circle"
                size="xs"
                color="red"
                variant="soft"
                label="エラー"
                block
                :trailing="false"
                disabled
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>
    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import type { ReportExport } from "~/types";
  import { ReportExportStatus } from "~/types/enums.d";
  import { useReportsStore } from "~/stores/app/reports";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);

  const toast = useToast();
  const reportsStore = useReportsStore();
  const route = useRoute();
  const {
    loadings,
    pagination,
    reports,
    pageTo,
    pageFrom,
    totalReportsCount,
    sortConditions,
  } = storeToRefs(reportsStore);
  const permissions = usePermissions();
  const columns = computed(() => {
    return [
      {
        key: "exportType",
        label: "タイプ",
        sortable: false,
      },
      {
        key: "outputColumnNames",
        label: "出力したカラム",
        sortable: false,
      },
      {
        key: "startDate",
        label: "出力範囲の開始日付 → 終了日付",
        sortable: false,
      },
      {
        key: "createdAt",
        label: "作成日時",
        sortable: true,
      },
      {
        label: "#",
        key: "action",
        class: "text-center w-0",
        hide: !permissions.value.includes("download:app-reports"),
      },
    ].filter((column) => !column.hide);
  });

  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        reportsStore.fetchReports();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        reportsStore.fetchReports();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        reportsStore.fetchReports();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  onMounted(() => {
    const query = route.query;
    let _pagination, _sortConditions;
    if (query) {
      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      reportsStore.updateConditions(_pagination, _sortConditions);
    }
  });
  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const onDownload = (report: ReportExport) => {
    // create fake file
    // const blob = new Blob([""], { type: "text/csv" });
    // const url = window.URL.createObjectURL(blob);
    const url = report.exportUrl;
    const link = document.createElement("a");
    document.body.appendChild(link);
    link.href = url;
    link.download = "export.csv";
    link.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
  };

  const getColsExported = (row: ReportExport) => {
    if(!row.surveys) return row.outputColumnNames?.length;
    if(!row.outputColumnNames) return "不明"
    const allSurveyCols = row.surveys?.reduce((acc, survey) => {
      return acc + survey.outputColumns.length;
    }, 0);
    return row.outputColumnNames?.length + (allSurveyCols || 0);
  };
</script>
