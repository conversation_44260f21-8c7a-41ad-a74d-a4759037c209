<template>
  <div class="flex flex-col h-screen">
    <AppPageHeader title="相談者" />
    <div class="p-4">
      <ReportsSearchForm v-model="period" />
    </div>
    <!-- <UDivider color="gray" /> -->
    <div class="h-full px-4 pt-0 py-2">
      <ReportsSelectColumns
        class="h-full"
        :key="surveySelected"
        :columns="avaiablesExportCols"
        v-model="selected"
        @export="onExport"
        :loading="loadings.exportConsultationLogs"
      >
        <template #title-right>
          <div class="flex flex-row items-center gap-3">
            <div class="flex flex-row items-center gap-3">
              <URadio
                v-for="option in targetOptions"
                :value="option.value"
                v-model="targetSelected"
                :label="option.label"
              />
            </div>
            <AppSurveysMultiSelect
              v-if="targetSelected === 'survey'"
              v-model="selectedSurveysIds"
              size="xs"
              class="w-48"
              multiple
              :loading="surveysLoadings.fetchAllSurveys"
            />
            <AppWizardsMultiSelect
              v-if="targetSelected === 'wizard'"
              v-model="selectedWizardsIds"
              size="xs"
              class="w-48"
              :loading="wizardsLoadings.fetchAllWizards"
              multiple
            />
          </div>
        </template>
      </ReportsSelectColumns>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useReportsStore } from "~/stores/app/reports";
import { storeToRefs } from "pinia";
import { ReportExportType, CustomerFeature } from "~/types/enums.d";
import { useSurveysStore } from "~/stores/app/surveys";
import { useAppCustomersStore } from "~/stores/app/customers";
import { useAppWizardsStore } from "~/stores/app/wizards";
import { cloneDeep } from "lodash";
const wizardsStore = useAppWizardsStore();
const surveysStore = useSurveysStore();
const toast = useToast();
const reportsStore = useReportsStore();
const { loadings } = storeToRefs(reportsStore);
const customersStore = useAppCustomersStore();
const { currentCustomer, avaiableFeatures } = storeToRefs(customersStore);
const { loadings: surveysLoadings, allSurveys, selectedSurveysIds } = storeToRefs(
  surveysStore
);

const { loadings: wizardsLoadings, allWizards, selectedWizardsIds } = storeToRefs(
  wizardsStore
);
const avaiables = ref([
  { value: "counseleeId", label: "相談者ID", selected: false },
  { value: "customerId", label: "カスタマーID", selected: false },
  { value: "pictureUrl", label: "画像URL", selected: false },
  { value: "lineId", label: "相談者のLINEID", selected: false },
  { value: "email", label: "メールアドレス", selected: false },
  { value: "fullName", label: "相談者名", selected: false },
  { value: "isBlock", label: "ブロックの状態", selected: false },
  { value: "gender", label: "性別", selected: false },
  { value: "ageDecade", label: "年齢", selected: false },
  { value: "conveyed", label: "申し送り事項", selected: false },
  {
    value: "counseleeCreatedAt",
    label: "相談者作成日",
    selected: false,
  },
  {
    value: "counseleeUpdatedAt",
    label: "相談者更新日",
    selected: false,
  },
  {
    value: "counseleeCreatedBy",
    label: "相談者作成者",
    selected: false,
  },
  {
    value: "counseleeUpdatedBy",
    label: "相談者更新者",
    selected: false,
  },
]);

const targetOptions = computed(() => {
  return [
    {
      value: "wizard",
      label: "ウィザード対象",
      show: avaiableFeatures.value?.includes(CustomerFeature.Wizard),
    },
    { value: "survey", label: "アンケート対象", show: true },
  ].filter((item) => item.show);
});

const targetSelected = ref("survey");
watch(targetSelected, (newValue) => {
  if (newValue === "wizard") {
    selectedSurveysIds.value = [];
  } else {
    selectedWizardsIds.value = [];
  }
});
const { FormElementTypes } = useWizard();
const selectedSurveys = ref([]) as Ref<any[]>;
watch(
  () => selectedSurveysIds.value,
  (newValue, oldValue) => {
    if (newValue.length !== oldValue.length) {
      updateSelected(newValue);
    }
  }
);

const selectedWizards = ref([]) as Ref<any[]>;
watch(
  () => selectedWizardsIds.value,
  (newValue, oldValue) => {
    if (newValue.length !== oldValue.length) {
      updateSelectedWizards(newValue);
    }
  }
);

const updateSelected = (newValue: any) => {
  selectedSurveys.value = newValue.map((id) => {
    return allSurveys.value.find((survey) => survey.surveyId === id);
  });

  // fiff oldValues and newValues
  selected.value = selected.value.filter((item: any) => {
    return !item.survey || (item.survey && newValue.includes(item.survey.surveyId));
  });
};

const updateSelectedWizards = (newValue: any) => {
  console.log("🚀 ~ updateSelectedWizards ~ newValue:", newValue);
  selectedWizards.value = newValue.map((id: string) => {
    return allWizards.value.find((wizard) => wizard.wizardId === id);
  });

  // fiff oldValues and newValues
  selected.value = selected.value.filter((item: any) => {
    return !item.survey || (item.survey && newValue.includes(item.survey.surveyId));
  });
};

const avaiablesExportCols = computed(() => {
  return avaiables.value
    .concat(
      ...cloneDeep(
        selectedSurveys.value.map((survey) => {
          return survey.formTemplate
            .filter((obj: any) => {
              return !["titleAndDescription"].includes(obj.type);
            })
            .map((obj: any) => {
              return {
                value: obj._id,
                label: obj.title,
                selected: false,
                group: "surveyOutputColumnNames",
                survey: survey,
              };
            });
        })
      )
    )
    .concat(
      ...cloneDeep(
        selectedWizards.value.map((wizard) => {
          return wizard.wizard
            .filter((obj: any) => {
              return FormElementTypes.includes(obj.type);
            })
            .map((obj: any) => {
              return {
                value: obj._id,
                label: obj.title,
                selected: false,
                group: "wizardOutputColumnNames",
                wizard: wizard,
              };
            });
        })
      )
    );
});

const period = ref({
  type: "this_month",
  value: {
    from: null,
    to: null,
  },
});
const selected = ref([]);

const onExport = async () => {
  console.log("export");
  console.log("period: ", period);
  const result = await reportsStore.exportConsultationLogs(
    period.value,
    selected.value,
    ReportExportType.COUNSELEE
  );

  if (result) {
    toast.add({
      icon: "i-heroicons-check-circle",
      color: "green",
      title: "エクスポート成功",
      description:
        "エクスポートは処理中です。処理ステータスはエクスポート履歴で確認できます。",
      actions: [
        {
          label: "エクスポート履歴",
          click: () => {
            navigateTo("/app/reports");
          },
          color: "green",
        },
      ],
    });
  }
};

onMounted(async () => {
  await surveysStore.fetchAllSurveys(currentCustomer.value.customerId as string);
  await wizardsStore.fetchAllWizards(currentCustomer.value.customerId as string);
  updateSelected(selectedSurveysIds.value);
  // wizardsStore.createWizard()
});
</script>
