<script setup lang="ts">
import { ReportExportType, CustomerFeature } from "~/types/enums.d";
import type { CaseTag } from "~/types";
import { cloneDeep } from "lodash";
const wizardsStore = useAppWizardsStore();
const surveysStore = useSurveysStore();
const toast = useToast();
const reportsStore = useReportsStore();
const { loadings } = storeToRefs(reportsStore);
const { loadings: surveysLoadings, allSurveys, selectedSurveysIds } = storeToRefs(
  surveysStore
);
const { loadings: wizardsLoadings, allWizards, selectedWizardsIds } = storeToRefs(
  wizardsStore
);

const selectedSurveys = ref([]) as Ref<any[]>;
watch(
  () => selectedSurveysIds.value,
  (newValue, oldValue) => {
    if (newValue.length !== oldValue.length) {
      updateSelected(newValue);
    }
  }
);

const selectedWizards = ref([]) as Ref<any[]>;
watch(
  () => selectedWizardsIds.value,
  (newValue, oldValue) => {
    if (newValue.length !== oldValue.length) {
      updateSelectedWizards(newValue);
    }
  }
);

const updateSelected = (newValue: any) => {
  selectedSurveys.value = newValue.map((id: string) => {
    return allSurveys.value.find((survey) => survey.surveyId === id);
  });

  // fiff oldValues and newValues
  selected.value = selected.value.filter((item: any) => {
    return !item.survey || (item.survey && newValue.includes(item.survey.surveyId));
  });
};

const updateSelectedWizards = (newValue: any) => {
  console.log("🚀 ~ updateSelectedWizards ~ newValue:", newValue);
  selectedWizards.value = newValue.map((id: string) => {
    return allWizards.value.find((wizard) => wizard.wizardId === id);
  });

  // fiff oldValues and newValues
  selected.value = selected.value.filter((item: any) => {
    return !item.survey || (item.survey && newValue.includes(item.survey.surveyId));
  });
};

const customersStore = useAppCustomersStore();
const { currentCustomer, avaiableFeatures } = storeToRefs(customersStore);
const avaiables = ref([
  {
    value: "caseId",
    label: "ケースID",
    selected: false,
  },
  {
    value: "counseleeId",
    label: "相談者ID",
    selected: false,
  },
  {
    value: "counseleeName",
    label: "相談者名",
    selected: false,
  },
  {
    value: "counselorInChargeId",
    label: "カウンセラーID",
    selected: false,
  },
  {
    value: "counselorInChargeName",
    label: "カウンセラー名",
    selected: false,
  },
  {
    value: "order",
    label: "ケースのインデックス",
    selected: false,
  },
  {
    value: "risk",
    label: "リスク",
    selected: false,
  },
  {
    value: "status",
    label: "ケースの状態",
    selected: false,
  },
  {
    value: "startTime",
    label: "対応開始時間",
    selected: false,
  },
  {
    value: "endTime",
    label: "対応終了時間",
    selected: false,
  },
  {
    value: "channel",
    label: "チャネル",
    selected: false,
  },
  {
    value: "count",
    label: "相談回数",
    selected: false,
  },
  {
    value: "memos",
    label: "メモ一覧",
    selected: false,
  },
  {
    value: "counseleeChatCount",
    label: "相談者のメッセージ数",
    selected: false,
  },
  {
    value: "counselorChatCount",
    label: "カウンセラーのメッセージ数",
    selected: false,
  },
  {
    value: "createdAt",
    label: "ケース作成日",
    selected: false,
  },
  {
    value: "updatedAt",
    label: "ケース更新日",
    selected: false,
  },
  {
    value: "createdBy",
    label: "ケース作成者",
    selected: false,
  },
  {
    value: "updatedBy",
    label: "ケース更新者",
    selected: false,
  },
]) as Ref<
  {
    value: string;
    label: string;
    selected: boolean;
    group?: string;
    survey?: any;
  }[]
>;

const targetOptions = computed(() => {
  return [
    {
      value: "wizard",
      label: "ウィザード対象",
      show: avaiableFeatures.value?.includes(CustomerFeature.Wizard),
    },
    { value: "survey", label: "アンケート対象", show: true },
  ].filter((item) => item.show);
});
const targetSelected = ref("survey");
watch(targetSelected, (newValue) => {
  if (newValue === "wizard") {
    selectedSurveysIds.value = [];
  } else {
    selectedWizardsIds.value = [];
  }
});

const { FormElementTypes } = useWizard();
const avaiablesExportCols = computed(() => {
  return avaiables.value
    .concat(
      ...cloneDeep(
        selectedSurveys.value.map((survey) => {
          return survey.formTemplate
            .filter((obj: any) => {
              return !["titleAndDescription"].includes(obj.type);
            })
            .map((obj: any) => {
              return {
                value: obj._id,
                label: obj.title,
                selected: false,
                group: "surveyOutputColumnNames",
                survey: survey,
              };
            });
        })
      )
    )
    .concat(
      ...cloneDeep(
        selectedWizards.value.map((wizard) => {
          return wizard.wizard
            .filter((obj: any) => {
              return FormElementTypes.includes(obj.type);
            })
            .map((obj: any) => {
              return {
                value: obj._id,
                label: obj.title,
                selected: false,
                group: "wizardOutputColumnNames",
                wizard: wizard,
              };
            });
        })
      )
    );
});
const period = ref({
  type: "this_month",
  value: {
    from: null,
    to: null,
  },
});
const selected = ref([]) as Ref<
  {
    value: string;
    label: string;
    selected: boolean;
    group?: string;
    survey?: any;
  }[]
>;
const isAddedEmergency = ref(false);
const onAddEmergency = () => {
  if (isAddedEmergency.value) {
    return;
  }
  // if already added emergency columns, just toggle the selected value
  const emergency = avaiables.value.filter((item) => item.group === "emergency");
  if (!emergency.length) {
    avaiables.value.push(
      {
        value: "ip",
        label: "IPアドレス",
        selected: true,
        group: "emergency",
      },
      {
        value: "lineId",
        label: "接続元LINEID",
        selected: true,
        group: "emergency",
      },
      {
        value: "port",
        label: "ポート",
        selected: true,
        group: "emergency",
      },
      {
        value: "apiUrl",
        label: "接続先URL",
        selected: true,
        group: "emergency",
      },
      {
        value: "startTime",
        label: "相談開始日時",
        selected: true,
        group: "emergency",
      }
    );
  } else {
    emergency.map((item) => {
      item.selected = !item.selected;
    });
  }
  isAddedEmergency.value = !isAddedEmergency.value;
};

const onExport = async () => {
  console.log("export");
  console.log("period: ", period);
  const result = await reportsStore.exportConsultationLogs(
    period.value,
    selected.value,
    ReportExportType.CASE
  );

  if (result) {
    toast.add({
      icon: "i-heroicons-check-circle",
      color: "green",
      title: "エクスポート成功",
      description:
        "エクスポートは処理中です。処理ステータスはエクスポート履歴で確認できます。",
      actions: [
        {
          label: "エクスポート履歴",
          click: () => {
            navigateTo("/app/reports");
          },
          color: "green",
        },
      ],
    });
  }
};

onMounted(async () => {
  await surveysStore.fetchAllSurveys(currentCustomer.value.customerId as string);
  await wizardsStore.fetchAllWizards(currentCustomer.value.customerId as string);
  updateSelected(selectedSurveysIds.value);
  // wizardsStore.createWizard()
});

const isOpenCaseTagsPalette = ref(false);
const onSelectCaseTag = (caseTag: CaseTag) => {
  // check if already selected
  const column = avaiables.value.find((item) => item.value === caseTag.tagId);
  if (column) {
    column.selected = true;
    isOpenCaseTagsPalette.value = false;
    return;
  }
  avaiables.value.push({
    value: caseTag.tagId as string,
    label: caseTag.tagName,
    selected: true,
    group: "tagOutputColumnNames",
  });
  isOpenCaseTagsPalette.value = false;
};
</script>

<template>
  <div class="flex flex-col h-screen">
    <AppPageHeader title="ケース一覧" />
    <div class="p-4">
      <ReportsSearchForm v-model="period" />
    </div>
    <!-- <UDivider color="gray" /> -->
    <div class="h-full px-4 pt-0 py-2">
      <ReportsSelectColumns
        class="h-full"
        :columns="avaiablesExportCols"
        v-model="selected"
        @export="onExport"
        :loading="loadings.exportConsultationLogs"
      >
        <template #action-options>
          <UButton
            icon="i-heroicons-plus"
            size="xs"
            color="primary"
            variant="outline"
            label="相談分類"
            :trailing="false"
            @click="isOpenCaseTagsPalette = true"
          ></UButton>
          <UButton
            icon="i-material-symbols-emergency"
            size="xs"
            color="red"
            :variant="isAddedEmergency ? 'solid' : 'outline'"
            label="緊急対応"
            :trailing="false"
            @click="onAddEmergency"
          ></UButton>
        </template>
        <template #title-right>
          <div class="flex flex-row items-center gap-3">
            <div class="flex flex-row items-center gap-3">
              <URadio
                v-for="option in targetOptions"
                :value="option.value"
                v-model="targetSelected"
                :label="option.label"
              />
            </div>
            <AppSurveysMultiSelect
              v-if="targetSelected === 'survey'"
              v-model="selectedSurveysIds"
              size="xs"
              class="w-48"
              multiple
              :loading="surveysLoadings.fetchAllSurveys"
            />
            <AppWizardsMultiSelect
              v-if="targetSelected === 'wizard'"
              v-model="selectedWizardsIds"
              size="xs"
              class="w-48"
              multiple
              :loading="wizardsLoadings.fetchAllWizards"
            />
          </div> </template
        >Ï
      </ReportsSelectColumns>
    </div>
  </div>

  <AppCaseTagsPalette
    :is-open="isOpenCaseTagsPalette"
    @close="isOpenCaseTagsPalette = false"
    @select="onSelectCaseTag"
  />
</template>
