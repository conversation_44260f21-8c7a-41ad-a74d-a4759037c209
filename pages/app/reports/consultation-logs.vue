<template>
  <div class="flex flex-col h-screen">
    <AppPageHeader title="相談ログ" />
    <div class="p-4">
      <ReportsSearchForm v-model="period" />
    </div>
    <!-- <UDivider color="gray" /> -->
    <div class="h-full px-4 pt-0 py-2">
      <ReportsSelectColumns
        class="h-full"
        :columns="avaiables"
        v-model="selected"
        @export="onExport"
        :loading="loadings.exportConsultationLogs"
      >
        <template #title-right>
          <div class="flex flex-row items-center gap-3">
            <div class="flex flex-row items-center gap-3">
              ケースのエクスポート対象
            </div>
            <UButton
              @click="isShowCaseSelection = true"
              color="gray"
              size="xs"
              class="min-w-20 justify-center"
            >
              <template #leading>
                <div v-if="!casesSelected.length">すべて</div>
                <div v-else class="text-primary-500 font-semibold">
                  {{ casesSelected.length }}件
                </div>
              </template>
            </UButton>
          </div>
        </template>
      </ReportsSelectColumns>
    </div>
    <CasesSelectModal
      :show="isShowCaseSelection"
      @close="isShowCaseSelection = false"
      @select="casesSelected = $event"
    />
  </div>
</template>

<script setup lang="ts">
  import { useReportsStore } from "~/stores/app/reports";
  import { storeToRefs } from "pinia";
  import { ReportExportType } from "~/types/enums.d";
  const toast = useToast();
  const reportsStore = useReportsStore();
  const { loadings } = storeToRefs(reportsStore);

  const avaiables = ref([
    {
      value: "caseId",
      label: "ケースID",
      selected: false,
    },
    {
      value: "counseleeId",
      label: "相談者ID",
      selected: false,
    },
    {
      value: "counseleeName",
      label: "相談者名",
      selected: false,
    },
    {
      value: "counselorInChargeId",
      label: "カウンセラーID",
      selected: false,
    },
    {
      value: "counselorInChargeName",
      label: "カウンセラー名",
      selected: false,
    },
    {
      value: "order",
      label: "ケースのインデックス",
      selected: false,
    },
    {
      value: "risk",
      label: "リスク",
      selected: false,
    },
    {
      value: "status",
      label: "ケースの状態",
      selected: false,
    },
    {
      value: "startTime",
      label: "対応開始時間",
      selected: false,
    },
    {
      value: "endTime",
      label: "対応終了時間",
      selected: false,
    },
    {
      value: "channel",
      label: "チャネル",
      selected: false,
    },
    {
      value: "count",
      label: "相談回数",
      selected: false,
    },
    {
      value: "memos",
      label: "メモ一覧",
      selected: false,
    },
    {
      value: "counseleeChatCount",
      label: "相談者のメッセージ数",
      selected: false,
    },
    {
      value: "counselorChatCount",
      label: "カウンセラーのメッセージ数",
      selected: false,
    },
    {
      value: "createdAt",
      label: "ケース作成日",
      selected: false,
    },
    {
      value: "updatedAt",
      label: "ケース更新日",
      selected: false,
    },
    {
      value: "createdBy",
      label: "ケース作成者",
      selected: false,
    },
    {
      value: "updatedBy",
      label: "ケース更新者",
      selected: false,
    },
    { value: "chatId", label: "チャットID", selected: false },
    { value: "content", label: "チャットの内容", selected: false },
    { value: "surveyId", label: "アンケートID", selected: false },
    { value: "surveyName", label: "アンケート名", selected: false },
    { value: "sender", label: "送信者情報", selected: false },
    { value: "senderId", label: "送信者ID", selected: false },
    { value: "senderName", label: "送信者名", selected: false },
    { value: "senderAvatar", label: "送信者の画像URL", selected: false },
    {
      value: "chatCreatedAt",
      label: "チャット作成日",
      selected: false,
    },
    {
      value: "chatUpdatedAt",
      label: "チャット更新日",
      selected: false,
    },
    {
      value: "chatCreatedBy",
      label: "チャット作成者",
      selected: false,
    },
    {
      value: "chatUpdatedBy",
      label: "チャット更新者",
      selected: false,
    },
  ]);

  const period = ref({
    type: "this_month",
    value: {
      from: null,
      to: null,
    },
  });
  const selected = ref([]);
  const isShowCaseSelection = ref(false);
  const casesSelected = ref([]);
  const onExport = async () => {
    console.log("export");
    const result = await reportsStore.exportConsultationLogs(
      period.value,
      selected.value,
      ReportExportType.CHAT,
      casesSelected.value,
    );

    if (result) {
      toast.add({
        icon: "i-heroicons-check-circle",
        color: "green",
        title: "エクスポート成功",
        description:
          "エクスポートは処理中です。処理ステータスはエクスポート履歴で確認できます。",
        actions: [
          {
            label: "エクスポート履歴",
            click: () => {
              navigateTo("/app/reports");
            },
            color: "green",
          },
        ],
      });
    }
  };
</script>
