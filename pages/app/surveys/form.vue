<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="" class="sticky top-0 z-10 bg-gray-100 dark:bg-gray-900">
      <template #left>
        <div class="flex flex-1 flex-row items-center space-x-2">
          <UButton
            icon="i-heroicons-arrow-left"
            size="sm"
            color="gray"
            variant="ghost"
            label="戻る"
            :trailing="false"
            :padded="false"
            @click="navigateTo('/app/surveys/list')"
          />
          <div class="border-l pl-3 flex-1">
            <UInput
              :padded="false"
              placeholder="アンケート名を入力してください"
              variant="none"
              size="xl"
              v-model="surveyDetail.surveyName"
            />
          </div>
        </div>
      </template>
      <template #right>
        <div class="mr-4 space-x-3">
          <UButton
            v-if="isAddNew"
            icon="i-material-symbols-light-select-window"
            class="px-6"
            color="gray"
            @click="isOpenSurveyTemplatePalette = true"
          >
            テンプレートを使用
          </UButton>
          <UButton
            :disabled="!surveyDetail.surveyName?.trim()"
            icon="i-ic-outline-save"
            class="px-6"
            @click="onSubmit"
          >
            {{ $t("Save") }}
          </UButton>
        </div>
      </template>
    </AppPageHeader>
    <div class="flex flex-row px-6 space-x-6 dark:bg-gray-900">
      <div class="py-6 flex-1 flex flex-row space-x-4">
        <div class="relative flex-1 flex flex-col pb-40">
          <draggable
            class="dragArea list-group w-full relative flex-1 flex flex-col space-y-4"
            item-key="_id"
            ref="parent"
            :component-data="{
              tag: 'div',
              type: 'transition-group',
              name: !drag ? 'flip-list' : null,
            }"
            handle=".handle"
            :list="formTemplate"
            v-bind="dragOptions"
            @start="onStartDrag"
            @end="onEndDrag"
            :key="formTemplate.length"
            direction="vertical"
          >
            <template #item="{ element, index }">
              <div :key="element._id">
                <component
                  :id="element._id"
                  :is="FormElements[element.type]"
                  :schema="element"
                  :active="activeElement._id === element._id"
                  :borderTop="index === 0"
                  :isAddNew="isAddNew"
                />
              </div>
            </template>
          </draggable>
        </div>
        <div class="relative w-12 flex justify-center">
          <FormSurveyMenu
            ref="formSurveyMenuRef"
            class="absolute transition-all duration-300"
          />
        </div>
      </div>
      <div class="pt-2">
        <SurveyPreview
          :formTemplate="formTemplate"
          :activeElement="activeElement"
        />
      </div>
    </div>
  </div>
  <SurveysTemplatePalette
    :is-open="isOpenSurveyTemplatePalette"
    @close="isOpenSurveyTemplatePalette = false"
    @select="onSelectSurveyTemplate"
  />
</template>

<script setup lang="ts">
  definePageMeta({
    layout: false,
  });
  import type { Survey } from "@/types";
  import draggable from "vuedraggable";
  import { useSurveyFormStore } from "~/stores/app/survey-form";
  import { useSurveysStore } from "~/stores/app/surveys";
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { cloneDeep } from "lodash";
  import { useAppCustomersStore } from "~/stores/app/customers";

  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const surveysStore = useSurveysStore();
  const surveyFormStore = useSurveyFormStore();
  const { formTemplate, activeElement } = storeToRefs(surveyFormStore);
  const { surveyDetail, loadings } = storeToRefs(surveysStore);

  const isOpenSurveyTemplatePalette = ref(false);
  const isAddNew = ref(false);
  const toast = useToast();
  const route = useRoute();

  const drag = ref(false);
  const dragOptions = computed(() => ({
    animation: 200,
    group: "description",
    disabled: false,
    ghostClass: "ghost",
    dragClass: "no-move",
  }));
  // onBeforeRouteLeave(() => {
  //   return confirm(
  //     t("You have unsaved changes. Are you sure you want to leave?"),
  //   );
  // });
  const { FormElements: FormElements } = useFormSurveyElements();
  onMounted(async () => {
    const { addNew, surveyId } = route.query;
    if (addNew) {
      surveyFormStore.initialFormTemplate();
      isAddNew.value = true;
      surveyDetail.value = {
        surveyName: "",
        formTemplate: [],
      };
    } else if (surveyId) {
      const survey = await surveysStore.fetchSurvey(surveyId as string);
      if (survey) {
        formTemplate.value = cloneDeep(survey.formTemplate);
      }
    }
  });

  const formSurveyMenuRef = ref(null as any);
  const onStartDrag = () => {
    drag.value = true;
  };
  const onEndDrag = () => {
    drag.value = false;
    nextTick(() => {
      const element = document.getElementById(activeElement.value._id);
      if (element) {
        const menuElement = formSurveyMenuRef.value?.$el;
        if (menuElement) {
          menuElement.style.top = `${element.offsetTop}px`;
        }
      }
    });
  };
  watch(
    () => activeElement.value,
    (activeElement) => {
      nextTick(() => {
        const element = document.getElementById(activeElement._id);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "center",
          });

          const menuElement = formSurveyMenuRef.value?.$el;
          if (menuElement) {
            menuElement.style.top = `${element.offsetTop}px`;
          }
        }
      });
    },
  );

  // listen scroll event
  const scrollHandler = () => {
    nextTick(() => {
      const menuElement = formSurveyMenuRef?.value?.$el;

      if (menuElement) {
        // check if menu is visible
        const menuTop = menuElement.offsetTop;
        const menuBottom = menuTop + menuElement.offsetHeight;
        const windowTop = window.pageYOffset;
        const windowBottom = windowTop + window.innerHeight;

        // check if active element is visible
        const activeElement = document.getElementById(
          surveyFormStore.activeElement._id,
        );
        const activeElementTop = activeElement?.offsetTop || 0;
        const activeElementBottom =
          activeElementTop + (activeElement?.offsetHeight || 0);
        const isActiveElementVisible =
          activeElementTop > windowTop && activeElementBottom < windowBottom;
        if (isActiveElementVisible) {
          menuElement.style.top = `${activeElementTop}px`;
        } else if (menuTop < windowTop) {
          menuElement.style.top = `${windowTop}px`;
        } else if (menuBottom > windowBottom) {
          menuElement.style.top = `${
            windowBottom - menuElement.offsetHeight * 2
          }px`;
        }
      }
    });
  };

  onMounted(() => {
    window.addEventListener("scroll", scrollHandler);
  });

  const onSelectSurveyTemplate = (template: Survey) => {
    surveyDetail.value = template;
    formTemplate.value = cloneDeep(template.formTemplate);
    isOpenSurveyTemplatePalette.value = false;
  };

  const onSubmit = async () => {
    const { surveyName } = surveyDetail.value;
    if (!surveyName) {
      return;
    }
    const payload = {
      surveyName,
      formTemplate: formTemplate.value,
      customerId: currentCustomer.value.customerId,
    };
    let result;
    if (isAddNew.value) {
      result = await surveysStore.createSurvey(payload);
    } else {
      result = await surveysStore.updateSurvey(surveyDetail.value.surveyId as string, payload);
    }
    if (result) {
      toast.add({
        title: "保存しました",
        description: "アンケートを保存しました",
        icon: "i-heroicons-check-circle",
      });
      navigateTo(`/app/surveys/list`);
    }
  };
</script>
