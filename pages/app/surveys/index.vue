<template>
  <AppSubNavigation title="アンケート">
    <div class="p-2">
      <SurveysMenu />
    </div>
  </AppSubNavigation>
  <main
    class="relative flex flex-col h-auto min-h-screen transition-all duration-200 space-y-6"
    :class="{
      'md:ml-60': !isSubNavigationMini,
      'md:ml-16': isSubNavigationMini,
    }"
  >
    <div class="flex flex-col w-full h-full">
      <AppPageHeader title="アンケート結果" />

      <div class="px-6">
        <SurveyResultsSearchForm />
      </div>
      <div class="px-6">
        <BaseTable
          title="アンケート結果一覧"
          :pagination="pagination"
          :page-from="pageFrom"
          :page-to="pageTo"
          @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
          @update:page="(value: number) => (pagination.page = value)"
          :total="totalSurveyResultsCount"
        >
          <template #action>
            <UButton
              size="xs"
              variant="solid"
              icon="i-heroicons-arrow-path-solid"
              color="gray"
              label="リロード"
              @click="
                surveyResultsStore.fetchSurveyResults(
                  currentCustomer.customerId as string,
                )
              "
            />
          </template>

          <UTable
            :columns="columns"
            :rows="surveyResults"
            v-model:sort="sort"
            sort-mode="manual"
            sort-asc-icon="i-heroicons-arrow-up"
            sort-desc-icon="i-heroicons-arrow-down"
            :loading="loadings['fetchSurveyResults'] === true"
          >
            <template #surveyName-data="{ row }">
              <div>
                <ULink
                  @click="
                    detailSurveyResult = row;
                    isOpenDetailModal = true;
                  "
                  inactive-class="text-primary-500 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-200"
                >
                  {{ $t(row.surveyName) }}
                </ULink>
              </div>
            </template>
            <template #counseleeName-data="{ row }">
              <div class="flex flex-row items-center space-x-2">
                <UAvatar
                  size="xs"
                  :src="row.counseleeAvatar"
                  :alt="row.counseleeName"
                />
                <div>
                  <ULink
                    inactive-class="text-primary-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    {{ row.counseleeName }}
                  </ULink>
                </div>
              </div>
            </template>
            <template #createdAt-data="{ row }">
              <div>
                <span>
                  {{
                    formatDate(
                      new Date(row.createdAt),
                      "YYYY年MM月DD日 HH時mm分",
                    )
                  }}
                </span>
              </div>
            </template>
            <template #segmentId-data="{ row }">
              <div
                v-if="row.segmentId"
                class="flex flex-row items-center space-x-2 justify-end"
              >
                <div>
                  <ULink
                    inactive-class="text-primary-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
                  >
                    {{ row.segmentName }}
                  </ULink>
                </div>
                <div>
                  <UBadge
                    size="xs"
                    :ui="{ rounded: 'rounded-full' }"
                    class="px-3"
                    :color="getSegmentDeliveryStatusColor(row.segmentStatus)"
                  >
                    {{ $t(row.segmentStatus) }}
                  </UBadge>
                </div>
              </div>
              <div v-else>
                <span class="text-gray-500 dark:text-gray-400"> なし </span>
              </div>
            </template>
          </UTable>
        </BaseTable>
      </div>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </main>
  <SurveyResultDetailModal
    :isOpen="isOpenDetailModal"
    :surveyResult="detailSurveyResult"
    @close="isOpenDetailModal = false"
  />
</template>

<script setup lang="ts">
  import type { SurveyResult } from "@/types";
  import { storeToRefs } from "pinia";
  import { useSurveyResultsStore } from "~/stores/app/survey-results";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const surveyResultsStore = useSurveyResultsStore();

  const {
    loadings,
    pagination,
    surveyResults,
    pageTo,
    pageFrom,
    sortConditions,
    totalSurveyResultsCount,
  } = storeToRefs(surveyResultsStore);

  const columns = [
    {
      key: "surveyName",
      label: "アンケート名",
      sortable: true,
    },
    {
      key: "counseleeName",
      label: "相談者",
      sortable: true,
    },
    {
      key: "createdAt",
      label: "作成日時",
      sortable: true,
    },
    {
      key: "segmentId",
      label: "セグメント配信",
      sortable: true,
      class: "w-[147px] text-right",
    },
  ];
  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const isOpenDetailModal = ref(false);
  const detailSurveyResult = ref(null) as Ref<SurveyResult | null>;
</script>
