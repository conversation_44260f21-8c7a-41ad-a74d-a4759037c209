<template>
  <AppSubNavigation title="アンケート">
    <div class="p-2">
      <SurveysMenu />
    </div>
  </AppSubNavigation>
  <main
    class="relative flex flex-col h-auto min-h-screen transition-all duration-200 space-y-6"
    :class="{
      'md:ml-60': !isSubNavigationMini,
      'md:ml-16': isSubNavigationMini,
    }"
  >
    <div>
      <AppPageHeader title="アンケート一覧" />
      <div class="px-6 pt-6">
        <BaseTable
          title="アンケート一覧"
          :pagination="pagination"
          :page-from="pageFrom"
          :page-to="pageTo"
          @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
          @update:page="(value: number) => (pagination.page = value)"
          :total="totalSurveysCount"
        >
          <template #header-right>
            <UButton
              size="md"
              label="新規作成"
              variant="soft"
              icon="i-heroicons-plus-circle-solid"
              color="blue"
              @click="navigateTo('/app/surveys/form?addNew=true')"
            />
          </template>
          <template #action>
            <UButton
              size="xs"
              variant="solid"
              icon="i-heroicons-arrow-path-solid"
              color="gray"
              label="リロード"
              @click="
                surveysStore.fetchSurveys(currentCustomer.customerId as string)
              "
            />
          </template>

          <UTable
            :columns="columns"
            :rows="surveys"
            v-model:sort="sort"
            sort-mode="manual"
            sort-asc-icon="i-heroicons-arrow-up"
            sort-desc-icon="i-heroicons-arrow-down"
            :loading="loadings['fetchSurveys'] === true"
          >
            <template #empty-state>
              <div class="flex flex-col items-center justify-center py-6 gap-3">
                <Icon icon="wpf:survey" class="text-gray-400 text-3xl" />
                <span class="text-sm text-gray-400">
                  アンケートがありません
                </span>
              </div>
            </template>
            <template #createdAt-data="{ row }">
              <div>
                <span>
                  {{
                    formatDate(
                      new Date(row.createdAt),
                      "YYYY年MM月DD日 HH時mm分",
                    )
                  }}
                </span>
              </div>
            </template>
            <template #updatedAt-data="{ row }">
              <div>
                <span>
                  {{
                    formatDate(
                      new Date(row.updatedAt),
                      "YYYY年MM月DD日 HH時mm分",
                    )
                  }}
                </span>
              </div>
            </template>
            <template #action-data="{ row }">
              <div class="flex space-x-2">
                <UButton
                  icon="i-heroicons-trash"
                  size="xs"
                  color="red"
                  variant="soft"
                  label="削除"
                  :key="row.surveyId"
                  :trailing="false"
                  v-confirm="{
                    title: 'アンケートの削除',
                    message: `「${row.surveyName}」のアンケートを削除してもよろしいですか？`,
                    confirmButtonText: 'はい、削除する',
                    cancelButtonText: 'いいえ',
                    onConfirm: () => deleteSurvey(row),
                  }"
                  :loading="
                    loadings.deleteSurvey
                      ? loadings['deleteSurvey'][row.surveyId] === true
                      : false
                  "
                />
                <UButton
                  icon="i-heroicons-pencil-square"
                  size="xs"
                  color="primary"
                  variant="soft"
                  label="編集"
                  :trailing="false"
                  @click="
                    navigateTo('/app/surveys/form?surveyId=' + row.surveyId)
                  "
                />
              </div>
            </template>
          </UTable>
        </BaseTable>
      </div>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </main>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import type { Survey } from "~/types";
  import { useSurveysStore } from "~/stores/app/surveys";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { Icon } from "@iconify/vue";
  const customersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(customersStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const toast = useToast();
  const { t } = useI18n();
  const surveysStore = useSurveysStore();
  const {
    loadings,
    pagination,
    surveys,
    pageTo,
    pageFrom,
    totalSurveysCount,
    sortConditions,
  } = storeToRefs(surveysStore);

  const columns = [
    {
      key: "surveyName",
      label: "アンケート名",
      sortable: true,
    },
    {
      key: "createdBy",
      label: "作成者",
      sortable: true,
    },
    {
      key: "createdAt",
      label: "作成日時",
      sortable: true,
    },
    // {
    //   key: "updatedBy",
    //   label: "更新者",
    //   sortable: true,
    // },
    // {
    //   key: "updatedAt",
    //   label: "更新日時",
    //   sortable: true,
    // },
    {
      label: "#",
      key: "action",
      class: "text-center w-0",
    },
  ];
  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });
  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        surveysStore.fetchSurveys(currentCustomer.value.customerId as string);
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        surveysStore.fetchSurveys(currentCustomer.value.customerId as string);
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        surveysStore.fetchSurveys(currentCustomer.value.customerId as string);
      }
    },
    { deep: true, immediate: true },
  );

  const deleteSurvey = async (survey: Survey) => {
    const result = await surveysStore.deleteSurvey(survey);

    if (result) {
      toast.add({
        title: t("Delete success"),
        description: "アンケートを削除しました",
        icon: "i-heroicons-check-circle",
      });
    }
  };
</script>
