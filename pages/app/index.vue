<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useAppCustomersStore } from "~/stores/app/customers";
  const appUIStore = useAppUIStore();
  const appCustomersStore = useAppCustomersStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  const { currentCustomer } = storeToRefs(appCustomersStore);
  definePageMeta({
    middleware: ["permissions"],
  });
</script>

<template>
  <div>
    <AppSubNavigation>
      <div class="p-2">
        <AppCounselorsList />
      </div>
    </AppSubNavigation>
    <div
      class="text-center bg-white dark:bg-gray-800 text-xl py-2 px-3 border-b dark:border-gray-700 font-semibold sticky top-0 z-40 shadow-sm"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <div class="text-center text-primary-600">
        {{ currentCustomer.basic.customerName }}
      </div>
    </div>
    <main
      class="relative h-auto min-h-screen transition-all duration-200 p-6 space-y-6"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <DashboardStatistics />
      <UDivider />
      <DashboardSearchForm />
      <DashboardTable />
      <BaseCopyright
        class="bottom-0 flex flex-row items-center justify-center"
        textClass="!text-gray-800 dark:!text-gray-300"
        wrapClass="bg-transparent"
      />
    </main>
    <!-- <AppUsersPalette /> -->
  </div>
</template>
