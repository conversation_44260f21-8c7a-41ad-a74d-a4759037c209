<template>
  <div>
    <AppScenarioList
      :scenarios="scenarios"
      :loading="loading"
      :total-count="totalCount"
      :current-page="currentPage"
      :page-size="pageSize"
      @create-scenario="navigateToCreate"
      @edit-scenario="navigateToEdit"
      @view-scenario="navigateToView"
      @delete-scenario="handleDelete"
      @toggle-scenario="handleToggle"
      @page-change="handlePageChange"
      @refresh-scenarios="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario",
  middleware: ["permissions"],
})

const router = useRouter()
const toast = useToast()

// Composables
const { 
  scenarios, 
  loading, 
  totalCount, 
  currentPage, 
  pageSize,
  fetchScenarios,
  deleteScenario,
  toggleScenario
} = useScenarioService()

// Load scenarios on mount
await fetchScenarios()

const navigateToCreate = () => {
  router.push('/app/chatbot/scenario/create')
}

const navigateToEdit = (scenario: Scenario) => {
  router.push(`/app/chatbot/scenario/${scenario.id}/edit`)
}

const navigateToView = (scenario: Scenario) => {
  router.push(`/app/chatbot/scenario/${scenario.id}`)
}

const handleDelete = async (scenario: Scenario) => {
  try {
    await deleteScenario(scenario.id)
    toast.add({
      title: 'シナリオを削除しました',
      description: `「${scenario.name}」を削除しました`,
      color: 'green'
    })
    await fetchScenarios()
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの削除に失敗しました',
      color: 'red'
    })
  }
}

const handleToggle = async (scenario: Scenario) => {
  try {
    await toggleScenario(scenario.id, !scenario.isActive)
    toast.add({
      title: 'シナリオを更新しました',
      description: `「${scenario.name}」を${!scenario.isActive ? '有効' : '無効'}にしました`,
      color: 'green'
    })
    await fetchScenarios()
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの更新に失敗しました',
      color: 'red'
    })
  }
}

const handlePageChange = async (page: number) => {
  currentPage.value = page
  await fetchScenarios()
}

const handleRefresh = async () => {
  await fetchScenarios()
}
</script>