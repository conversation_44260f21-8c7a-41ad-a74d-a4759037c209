<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="シナリオ編集">
      <template #actions>
        <div class="flex items-center space-x-2">
          <!-- Preview Toggle (Mobile/Tablet) -->
          <UButton
            v-if="!isDesktop"
            :variant="showPreview ? 'solid' : 'outline'"
            color="primary"
            icon="i-heroicons-eye"
            @click="togglePreview"
          >
            プレビュー
          </UButton>

          <UButton
            color="gray"
            variant="soft"
            icon="i-heroicons-arrow-left"
            @click="handleCancel"
          >
            戻る
          </UButton>
        </div>
      </template>
    </AppPageHeader>

    <!-- Main Content with Responsive Layout -->
    <div class="flex-1 flex min-h-0 relative">
      <!-- Left Panel - Form -->
      <div
        class="flex flex-col min-w-0 transition-all duration-300"
        :class="{
          'flex-1': isDesktop || !showPreview,
          'w-full': !isDesktop && showPreview,
          'hidden': !isDesktop && showPreview
        }"
      >
        <div class="flex-1 overflow-y-auto p-6">
          <div class="max-w-4xl mx-auto space-y-8">
            <AppScenarioForm
              ref="scenarioFormRef"
              :scenario="scenario"
              :saving="saving"
              :end-templates="endTemplates"
              :loading-end-templates="loadingEndTemplates"
              @save="handleSave"
              @cancel="handleCancel"
              @form-change="handleFormChange"
            />

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3">
              <UButton
                color="gray"
                variant="soft"
                @click="handleCancel"
                :disabled="saving"
              >
                キャンセル
              </UButton>
              <UButton
                color="primary"
                @click="handleSubmit"
                :loading="saving"
                :disabled="!canSave"
              >
                シナリオを更新
              </UButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Panel - Preview -->
      <div
        class="border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 transition-all duration-300"
        :class="{
          'w-1/2': isDesktop,
          'w-full': !isDesktop && showPreview,
          'hidden': !isDesktop && !showPreview
        }"
      >
        <ScenarioPreview
          :scenario="previewScenario || scenario"
          :end-templates="endTemplates"
        />

        <!-- Mobile Close Button -->
        <div v-if="!isDesktop && showPreview" class="absolute top-4 right-4 z-10">
          <UButton
            variant="solid"
            color="gray"
            icon="i-heroicons-x-mark"
            @click="togglePreview"
            size="sm"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario-id-edit",
  middleware: ["permissions"],
})

const router = useRouter()
const route = useRoute()
const toast = useToast()

const {
  saving,
  endTemplates,
  loadingEndTemplates,
  getScenario,
  updateScenario,
  fetchEndTemplates
} = useScenarioService()

const scenarioId = route.params.id as string
const scenario = ref<Scenario | null>(null)

// Form ref to access canSave
const scenarioFormRef = ref()

// Preview scenario data
const previewScenario = ref<Scenario | null>(null)

// Responsive design
const showPreview = ref(false)
const isDesktop = ref(true)

// Computed property for canSave
const canSave = computed(() => {
  return scenarioFormRef.value?.canSave || false
})

// Check screen size
const checkScreenSize = () => {
  if (process.client) {
    isDesktop.value = window.innerWidth >= 1024 // lg breakpoint
    if (isDesktop.value) {
      showPreview.value = true // Always show on desktop
    }
  }
}

// Toggle preview on mobile/tablet
const togglePreview = () => {
  showPreview.value = !showPreview.value
}

// Load scenario and end templates
const loadData = async () => {
  await Promise.all([
    loadScenario(),
    fetchEndTemplates()
  ])
}

const loadScenario = async () => {
  if (scenarioId) {
    scenario.value = await getScenario(scenarioId)
  }
}

await loadData()

const handleSubmit = () => {
  // Trigger form submission
  if (scenarioFormRef.value) {
    const formElement = scenarioFormRef.value.$el.querySelector('form')
    if (formElement) {
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }
}

const handleSave = async (scenarioData: Partial<Scenario>) => {
  try {
    const updatedScenario = await updateScenario(scenarioId, scenarioData)
    toast.add({
      title: 'シナリオを更新しました',
      description: `「${updatedScenario?.name}」を更新しました`,
      color: 'green'
    })
    router.push('/app/chatbot/scenario')
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの更新に失敗しました',
      color: 'red'
    })
  }
}

const handleCancel = () => {
  router.push(`/app/chatbot/scenario/${scenarioId}`)
}

// Handle form changes for preview
const handleFormChange = (formData: Partial<Scenario>) => {
  previewScenario.value = formData as Scenario
}

// Initialize responsive behavior
onMounted(() => {
  checkScreenSize()
  if (process.client) {
    window.addEventListener('resize', checkScreenSize)
  }
})

onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', checkScreenSize)
  }
})
</script>