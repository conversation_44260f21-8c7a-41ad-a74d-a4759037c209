<template>
  <div>
    <AppScenarioViewer
      :scenario="scenario"
      @back="handleBack"
      @edit="handleEdit"
    />
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario-id",
  middleware: ["permissions"],
})

const router = useRouter()
const route = useRoute()

const { getScenario } = useScenarioService()

const scenarioId = route.params.id as string
const scenario = ref<Scenario | null>(null)

// Load scenario data
const loadScenario = async () => {
  if (scenarioId) {
    scenario.value = await getScenario(scenarioId)
  }
}

await loadScenario()

const handleBack = () => {
  router.push('/app/chatbot/scenario')
}

const handleEdit = (scenarioToEdit: Scenario) => {
  router.push(`/app/chatbot/scenario/${scenarioToEdit.id}/edit`)
}
</script>