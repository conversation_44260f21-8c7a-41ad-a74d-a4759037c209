<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="新規シナリオ作成">
      <template #actions>
        <UButton
          color="gray"
          variant="soft"
          icon="i-heroicons-arrow-left"
          @click="handleCancel"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>

    <div class="p-6">
      <div class="max-w-4xl mx-auto space-y-8">
        <AppScenarioForm
          ref="scenarioFormRef"
          :saving="saving"
          :end-templates="endTemplates"
          :loading-end-templates="loadingEndTemplates"
          @save="handleSave"
          @cancel="handleCancel"
        />

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
          <UButton
            color="gray"
            variant="soft"
            @click="handleCancel"
            :disabled="saving"
          >
            キャンセル
          </UButton>
          <UButton
            color="primary"
            @click="handleSubmit"
            :loading="saving"
            :disabled="!canSave"
          >
            シナリオを作成
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Scenario } from '~/types'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot-scenario-create",
  middleware: ["permissions"],
})

const router = useRouter()
const toast = useToast()

const {
  saving,
  endTemplates,
  loadingEndTemplates,
  createScenario,
  fetchEndTemplates
} = useScenarioService()

// Form ref to access canSave
const scenarioFormRef = ref()

// Computed property for canSave
const canSave = computed(() => {
  return scenarioFormRef.value?.canSave || false
})

// Load end templates for the form
await fetchEndTemplates()

const handleSubmit = () => {
  // Trigger form submission
  if (scenarioFormRef.value) {
    const formElement = scenarioFormRef.value.$el.querySelector('form')
    if (formElement) {
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }
}

const handleSave = async (scenarioData: Partial<Scenario>) => {
  try {
    const newScenario = await createScenario(scenarioData)
    toast.add({
      title: 'シナリオを作成しました',
      description: `「${newScenario?.name}」を作成しました`,
      color: 'green'
    })
    router.push('/app/chatbot/scenario')
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'シナリオの作成に失敗しました',
      color: 'red'
    })
  }
}

const handleCancel = () => {
  router.push('/app/chatbot/scenario')
}
</script>