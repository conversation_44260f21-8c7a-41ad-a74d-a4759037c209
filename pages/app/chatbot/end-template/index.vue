<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="終了テンプレート" />
    <div class="px-6 pt-6">
      <BaseTable
        title="終了テンプレート管理"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalTemplatesCount"
      >
        <template #header-right>
          <UButton
            size="md"
            label="新規作成"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="navigateToCreate"
          />
        </template>
        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="refreshTemplates"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="paginatedTemplates"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loading"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-document-check" class="text-gray-400 text-3xl" />
              <span class="text-sm text-gray-400">
                終了テンプレートがありません
              </span>
            </div>
          </template>
          <template #name-data="{ row }">
            <div class="flex items-center space-x-3">
              <UIcon name="i-heroicons-document-check" class="text-blue-500" />
              <div>
                <div class="font-semibold">{{ row.name }}</div>
                <p class="text-xs text-gray-500">{{ getCategoryLabel(row.category) }}</p>
              </div>
            </div>
          </template>
          <template #finalQuestion-data="{ row }">
            <div class="max-w-md">
              <div class="bg-gray-50 dark:bg-gray-800 rounded p-2 text-sm">
                <p class="line-clamp-2">{{ row.finalQuestion }}</p>
              </div>
            </div>
          </template>
          <template #choices-data="{ row }">
            <div class="max-w-xs">
              <UBadge variant="soft" size="sm">
                {{ row.choices.length }}個
              </UBadge>
              <div class="mt-1 text-xs text-gray-500">
                <div v-for="(choice, index) in row.choices.slice(0, 2)" :key="choice.id" class="truncate">
                  {{ index + 1 }}. {{ choice.text }}
                </div>
                <div v-if="row.choices.length > 2" class="text-gray-400">
                  他{{ row.choices.length - 2 }}個...
                </div>
              </div>
            </div>
          </template>
          <template #status-data="{ row }">
            <UBadge
              :color="row.isActive ? 'green' : 'gray'"
              variant="soft"
              size="sm"
            >
              {{ row.isActive ? '有効' : '無効' }}
            </UBadge>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.createdAt) }}
              </span>
            </div>
          </template>
          <template #updatedAt-data="{ row }">
            <div>
              <span>
                {{ formatDateTime(row.updatedAt) }}
              </span>
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                @click="navigateToEdit(row.id)"
              />
              <UButton
                :icon="row.isActive ? 'i-heroicons-pause' : 'i-heroicons-play'"
                size="xs"
                :color="row.isActive ? 'red' : 'green'"
                variant="soft"
                :label="row.isActive ? '無効化' : '有効化'"
                @click="toggleTemplate(row)"
              />
              <UButton
                icon="i-heroicons-document-duplicate"
                size="xs"
                color="gray"
                variant="soft"
                label="複製"
                @click="duplicateTemplate(row)"
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>



  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { EndTemplate, EndTemplateChoice } from '@/types'

definePageMeta({
  navigationPage: "app-chatbot-end-template",
  middleware: ["permissions"],
});

const loading = ref(false)

// Pagination
const pagination = ref({
  page: 1,
  pageRangeDisplayed: 10
})



const categoryOptions = [
  { label: '問題解決完了', value: 'resolved' },
  { label: '情報提供完了', value: 'information' },
  { label: '案内完了', value: 'guidance' },
  { label: '営業時間外', value: 'off-hours' },
  { label: 'エラー対応', value: 'error' },
  { label: 'その他', value: 'other' }
]

const templates = ref<EndTemplate[]>([
  {
    id: '1',
    templateName: '問題解決完了テンプレート',
    finalQuestion: 'ご質問は解決されましたでしょうか？',
    choices: [
      {
        id: 'choice_1_1',
        text: '解決しました。ありがとうございました。',
        responseMessage: 'お役に立てて良かったです。また何かございましたらお気軽にお声かけください。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_1_2',
        text: 'まだ解決していません。',
        responseMessage: '申し訳ございません。担当者がご対応いたします。',
        openOneOnOneTalk: true
      },
      {
        id: 'choice_1_3',
        text: '別の質問があります。',
        responseMessage: '',
        openOneOnOneTalk: true
      }
    ],
    isActive: true,
    createdAt: '2024-01-15',
    updatedAt: '2024-01-20'
  },
  {
    id: '2',
    templateName: '情報提供完了テンプレート',
    finalQuestion: '必要な情報をお伝えできましたでしょうか？',
    choices: [
      {
        id: 'choice_2_1',
        text: '十分な情報をいただけました。',
        responseMessage: 'ありがとうございます。今後ともよろしくお願いいたします。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_2_2',
        text: '追加で質問があります。',
        responseMessage: '',
        openOneOnOneTalk: true
      }
    ],
    isActive: true,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-18'
  },
  {
    id: '3',
    templateName: '営業時間外対応テンプレート',
    finalQuestion: '営業時間外のため、お急ぎの場合はどうされますか？',
    choices: [
      {
        id: 'choice_3_1',
        text: '営業時間内に改めて連絡します。',
        responseMessage: '承知いたしました。営業時間は平日9:00-18:00です。',
        openOneOnOneTalk: false
      },
      {
        id: 'choice_3_2',
        text: '緊急事態です。',
        responseMessage: '緊急事態の場合は、担当者にお繋ぎいたします。',
        openOneOnOneTalk: true
      }
    ],
    isActive: false,
    createdAt: '2024-01-05',
    updatedAt: '2024-01-05'
  }
])

// Table columns
const columns = [
  {
    key: "templateName",
    label: "テンプレート名",
    sortable: true,
  },
  {
    key: "finalQuestion",
    label: "最終設問",
    sortable: false,
  },
  {
    key: "choices",
    label: "選択肢数",
    sortable: false,
  },
  {
    key: "status",
    label: "ステータス",
    sortable: true,
  },
  {
    key: "createdAt",
    label: "作成日時",
    sortable: true,
  },
  {
    key: "updatedAt",
    label: "更新日時",
    sortable: true,
  },
  {
    label: "#",
    key: "action",
    class: "text-center w-0",
  },
]

// Sorting
const sort = ref({
  column: 'createdAt',
  direction: 'desc' as 'asc' | 'desc'
})

// Computed properties for pagination
const totalTemplatesCount = computed(() => templates.value.length)

const paginatedTemplates = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageRangeDisplayed
  const end = start + pagination.value.pageRangeDisplayed

  // Apply sorting
  const sorted = [...templates.value].sort((a, b) => {
    const aVal = a[sort.value.column as keyof typeof a]
    const bVal = b[sort.value.column as keyof typeof b]

    if (!aVal && !bVal) return 0
    if (!aVal) return 1
    if (!bVal) return -1

    if (aVal < bVal) return sort.value.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sort.value.direction === 'asc' ? 1 : -1
    return 0
  })

  return sorted.slice(start, end)
})

const pageFrom = computed(() => {
  return (pagination.value.page - 1) * pagination.value.pageRangeDisplayed + 1
})

const pageTo = computed(() => {
  const end = pagination.value.page * pagination.value.pageRangeDisplayed
  return Math.min(end, totalTemplatesCount.value)
})



const formatDateTime = (date: string | Date) => {
  console.log("🚀 ~ formatDateTime ~ date:", date)
  // return empty string if date is not set
  if (!date) return ''

  // Convert string to Date object if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    console.warn('Invalid date:', date)
    return ''
  }

  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

const getCategoryLabel = (category: string) => {
  const option = categoryOptions.find(opt => opt.value === category)
  return option ? option.label : category
}

const refreshTemplates = () => {
  loading.value = true
  // Simulate API call
  setTimeout(() => {
    loading.value = false
    const toast = useToast()
    toast.add({
      title: 'テンプレート一覧を更新しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
  }, 1000)
}

const navigateToCreate = () => {
  navigateTo('/app/chatbot/end-template/create')
}

const navigateToEdit = (templateId: string) => {
  navigateTo(`/app/chatbot/end-template/edit/${templateId}`)
}



const toggleTemplate = async (template: EndTemplate) => {
  template.isActive = !template.isActive
  template.updatedAt = new Date().toISOString()

  const toast = useToast()
  toast.add({
    title: template.isActive ? 'テンプレートを有効化しました' : 'テンプレートを無効化しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const duplicateTemplate = (template: EndTemplate) => {
  const newTemplate: EndTemplate = {
    ...template,
    id: Date.now().toString(),
    templateName: `${template.templateName} (コピー)`,
    choices: template.choices.map(choice => ({
      ...choice,
      id: generateChoiceId()
    })),
    isActive: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  templates.value.push(newTemplate)

  const toast = useToast()
  toast.add({
    title: 'テンプレートを複製しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

// const deleteTemplate = (template: EndTemplate) => {
//   const index = templates.value.findIndex(t => t.id === template.id)
//   if (index > -1) {
//     templates.value.splice(index, 1)
//
//     const toast = useToast()
//     toast.add({
//       title: 'テンプレートを削除しました',
//       icon: 'i-heroicons-check-circle',
//       color: 'green'
//     })
//   }
// }
</script>
