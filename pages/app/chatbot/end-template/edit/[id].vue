<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="終了テンプレート編集">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="handleCancel"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div class="p-6">
      <div class="max-w-4xl mx-auto space-y-8">
        <AppEndTemplateForm
          ref="templateFormRef"
          :template="template"
          :saving="saving"
          @save="handleSave"
          @cancel="handleCancel"
        />
        
        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
          <UButton 
            color="gray" 
            variant="soft" 
            @click="handleCancel"
            :disabled="saving"
          >
            キャンセル
          </UButton>
          <UButton 
            color="primary" 
            @click="handleSubmit"
            :loading="saving"
            :disabled="!canSave"
          >
            テンプレートを更新
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EndTemplate } from '~/types'

definePageMeta({
  navigationPage: "app-chatbot-end-template-edit",
  middleware: ["permissions"],
})

const router = useRouter()
const route = useRoute()
const toast = useToast()

const saving = ref(false)
const templateId = route.params.id as string
const template = ref<EndTemplate | null>(null)

// Form ref to access canSave
const templateFormRef = ref()

// Computed property for canSave
const canSave = computed(() => {
  return templateFormRef.value?.canSave || false
})

// Load template data
const loadTemplate = async () => {
  try {
    // TODO: Implement API call to get template
    // For now, use mock data from the original file
    const mockTemplates = [
      {
        id: '1',
        templateName: '問題解決完了テンプレート',
        finalQuestion: 'ご質問は解決されましたでしょうか？',
        choices: [
          {
            id: 'choice_1_1',
            text: '解決しました。ありがとうございました。',
            responseMessage: 'お役に立てて良かったです。また何かございましたらお気軽にお声かけください。',
            openOneOnOneTalk: false
          },
          {
            id: 'choice_1_2',
            text: 'まだ解決していません。',
            responseMessage: '申し訳ございません。担当者がご対応いたします。',
            openOneOnOneTalk: true
          }
        ],
        isActive: true,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-20'
      }
    ]
    
    template.value = mockTemplates.find(t => t.id === templateId) || null
    
    if (!template.value) {
      throw new Error('Template not found')
    }
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'テンプレートの読み込みに失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
    router.push('/app/chatbot/end-template')
  }
}

// Load template on mount
await loadTemplate()

const handleSubmit = () => {
  // Trigger form submission
  if (templateFormRef.value) {
    const formElement = templateFormRef.value.$el.querySelector('form')
    if (formElement) {
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }
}

const handleSave = async (templateData: Partial<EndTemplate>) => {
  saving.value = true
  
  try {
    // TODO: Implement API call to update template
    console.log('Updating template:', templateData)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast.add({
      title: 'テンプレートを更新しました',
      description: `「${templateData.templateName}」を更新しました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
    // Navigate back to template list
    router.push('/app/chatbot/end-template')
    
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'テンプレートの更新に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  router.push('/app/chatbot/end-template')
}
</script>
