<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="新規終了テンプレート作成">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="handleCancel"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div class="p-6">
      <div class="max-w-4xl mx-auto space-y-8">
        <AppEndTemplateForm
          ref="templateFormRef"
          :saving="saving"
          @save="handleSave"
          @cancel="handleCancel"
        />
        
        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3">
          <UButton 
            color="gray" 
            variant="soft" 
            @click="handleCancel"
            :disabled="saving"
          >
            キャンセル
          </UButton>
          <UButton 
            color="primary" 
            @click="handleSubmit"
            :loading="saving"
            :disabled="!canSave"
          >
            テンプレートを作成
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EndTemplate } from '~/types'

definePageMeta({
  navigationPage: "app-chatbot-end-template-create",
  middleware: ["permissions"],
})

const router = useRouter()
const toast = useToast()

const saving = ref(false)

// Form ref to access canSave
const templateFormRef = ref()

// Computed property for canSave
const canSave = computed(() => {
  return templateFormRef.value?.canSave || false
})

const handleSubmit = () => {
  // Trigger form submission
  if (templateFormRef.value) {
    const formElement = templateFormRef.value.$el.querySelector('form')
    if (formElement) {
      formElement.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }))
    }
  }
}

const handleSave = async (templateData: Partial<EndTemplate>) => {
  saving.value = true
  
  try {
    // TODO: Implement API call to create template
    console.log('Creating template:', templateData)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast.add({
      title: 'テンプレートを作成しました',
      description: `「${templateData.templateName}」を作成しました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
    // Navigate back to template list
    router.push('/app/chatbot/end-template')
    
  } catch (error) {
    toast.add({
      title: 'エラー',
      description: 'テンプレートの作成に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  router.push('/app/chatbot/end-template')
}
</script>
