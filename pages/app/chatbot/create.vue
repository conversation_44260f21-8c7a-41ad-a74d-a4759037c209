<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="新規チャットボット作成">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div class="p-6">
      <div class="max-w-4xl mx-auto">
        <form @submit.prevent="createChatbot" class="space-y-8">
          <!-- Basic Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-blue-500" />
                <h3 class="text-lg font-semibold">基本情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボット名" required>
                <UInput
                  v-model="form.name"
                  placeholder="例: サポートボット"
                  :error="errors.name"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットの名称を入力します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="対応シナリオ" required>
                <USelectMenu
                  v-model="form.selectedScenario"
                  :options="availableScenarios"
                  value-attribute="id"
                  option-attribute="name"
                  placeholder="シナリオを選択してください"
                  :error="errors.selectedScenario"
                  searchable
                  searchable-placeholder="シナリオ名でフィルター..."
                >
                  <template #option="{ option }">
                    <div class="flex flex-col">
                      <span class="font-medium">{{ option.name }}</span>
                      <span v-if="option.description" class="text-xs text-gray-500">
                        {{ option.description }}
                      </span>
                    </div>
                  </template>
                </USelectMenu>
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットに紐付けるシナリオを選択します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="開始キーワード" required>
                <UInput
                  v-model="form.startKeyword"
                  placeholder="例: チャットボット開始"
                  :error="errors.startKeyword"
                  maxlength="100"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    ユーザーがLINEトークルーム上でKANAMETOチャットボットを起動させるためのキーワードを登録します。全角100文字以内（半角200文字以内）で登録します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="前の設問の選択肢が押された時の挙動" required>
                <URadioGroup
                  v-model="form.previousQuestionBehavior"
                  :options="previousQuestionBehaviorOptions"
                  :error="errors.previousQuestionBehavior"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    利用者が現在表示中の設問以外の設問の選択肢をタップした際の挙動を設定します
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Additional Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-information-circle" class="text-green-500" />
                <h3 class="text-lg font-semibold">追加情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="説明">
                <UTextarea
                  v-model="form.description"
                  placeholder="このチャットボットの用途や機能について説明してください"
                  :rows="3"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットの用途や機能について説明します（任意）
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>



          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3">
            <UButton 
              color="gray" 
              variant="soft" 
              @click="$router.back()"
              :disabled="loading"
            >
              キャンセル
            </UButton>
            <UButton 
              color="primary" 
              type="submit"
              :loading="loading"
            >
              チャットボットを作成
            </UButton>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const router = useRouter()
const loading = ref(false)

// Scenario service
const { scenarios, fetchScenarios } = useScenarioService()

const form = reactive({
  name: '',
  selectedScenario: null,
  startKeyword: '',
  previousQuestionBehavior: 'mismatch_message',
  description: ''
})

const errors = reactive({
  name: '',
  selectedScenario: '',
  startKeyword: '',
  previousQuestionBehavior: ''
})

// 前の設問の選択肢が押された時の挙動オプション
const previousQuestionBehaviorOptions = [
  {
    value: 'mismatch_message',
    label: '不一致メッセージを表示（デフォルト）',
    help: '不一致メッセージを表示し、現在の設問を再表示します'
  },
  {
    value: 'execute_action',
    label: '選択肢のアクションを実行',
    help: '利用者が選択した選択肢に紐づいたアクションを実行します'
  }
]

// 利用可能なシナリオ（API から取得）
const availableScenarios = computed(() => {
  return scenarios.value.filter(scenario => scenario.isActive)
})



const validateForm = () => {
  // Reset errors
  errors.name = ''
  errors.selectedScenario = ''
  errors.startKeyword = ''
  errors.previousQuestionBehavior = ''

  let isValid = true

  if (!form.name.trim()) {
    errors.name = 'チャットボット名は必須です'
    isValid = false
  }

  if (!form.selectedScenario) {
    errors.selectedScenario = '対応シナリオを選択してください'
    isValid = false
  }

  if (!form.startKeyword.trim()) {
    errors.startKeyword = '開始キーワードは必須です'
    isValid = false
  } else if (form.startKeyword.length > 100) {
    errors.startKeyword = '開始キーワードは100文字以内で入力してください'
    isValid = false
  }

  if (!form.previousQuestionBehavior) {
    errors.previousQuestionBehavior = '前の設問の選択肢が押された時の挙動を選択してください'
    isValid = false
  }

  return isValid
}



const createChatbot = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    // TODO: Implement API call to create chatbot
    console.log('Creating chatbot:', form)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))

    const toast = useToast()
    toast.add({
      title: 'チャットボットを作成しました',
      description: `${form.name}が正常に作成されました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })

    // Navigate back to chatbot list
    router.push('/app/chatbot')

  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの作成に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// Load scenarios on component mount
onMounted(async () => {
  await fetchScenarios()
})
</script>
