<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader :title="`チャットボット編集: ${chatbot?.name || 'Loading...'}`">
      <template #actions>
        <UButton 
          color="gray" 
          variant="soft" 
          icon="i-heroicons-arrow-left"
          @click="$router.back()"
        >
          戻る
        </UButton>
      </template>
    </AppPageHeader>
    
    <div v-if="loading" class="p-6">
      <div class="animate-pulse space-y-4">
        <div class="h-4 bg-gray-200 rounded w-3/4"></div>
        <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        <div class="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>

    <div v-else-if="chatbot" class="p-6">
      <div class="max-w-4xl mx-auto">
        <form @submit.prevent="updateChatbot" class="space-y-8">
          <!-- Basic Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-fluent-emoji-high-contrast-robot" class="text-blue-500" />
                <h3 class="text-lg font-semibold">基本情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="チャットボット名" required>
                <UInput
                  v-model="form.name"
                  placeholder="例: サポートボット"
                  :error="errors.name"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットの名称を入力します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="対応シナリオ" required>
                <USelectMenu
                  v-model="form.selectedScenario"
                  :options="availableScenarios"
                  value-attribute="id"
                  option-attribute="name"
                  placeholder="シナリオを選択してください"
                  :error="errors.selectedScenario"
                  searchable
                  searchable-placeholder="シナリオ名でフィルター..."
                >
                  <template #option="{ option }">
                    <div class="flex flex-col">
                      <span class="font-medium">{{ option.name }}</span>
                      <span v-if="option.description" class="text-xs text-gray-500">
                        {{ option.description }}
                      </span>
                    </div>
                  </template>
                </USelectMenu>
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットに紐付けるシナリオを選択します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="開始キーワード" required>
                <UInput
                  v-model="form.startKeyword"
                  placeholder="例: チャットボット開始"
                  :error="errors.startKeyword"
                  maxlength="100"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    ユーザーがLINEトークルーム上でKANAMETOチャットボットを起動させるためのキーワードを登録します。全角100文字以内（半角200文字以内）で登録します
                  </span>
                </template>
              </UFormGroup>

              <UFormGroup label="前の設問の選択肢が押された時の挙動" required>
                <URadioGroup
                  v-model="form.previousQuestionBehavior"
                  :options="previousQuestionBehaviorOptions"
                  :error="errors.previousQuestionBehavior"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    利用者が現在表示中の設問以外の設問の選択肢をタップした際の挙動を設定します
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Additional Information -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-information-circle" class="text-green-500" />
                <h3 class="text-lg font-semibold">追加情報</h3>
              </div>
            </template>
            <div class="space-y-4">
              <UFormGroup label="説明">
                <UTextarea
                  v-model="form.description"
                  placeholder="このチャットボットの用途や機能について説明してください"
                  :rows="3"
                />
                <template #help>
                  <span class="text-xs text-gray-500">
                    チャットボットの用途や機能について説明します（任意）
                  </span>
                </template>
              </UFormGroup>
            </div>
          </UCard>

          <!-- Metadata -->
          <UCard>
            <template #header>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-information-circle" class="text-gray-500" />
                <h3 class="text-lg font-semibold">メタデータ</h3>
              </div>
            </template>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">チャットボットID</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 font-mono">{{ chatbot.id }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">作成日時</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ formatDateTime(chatbot.createdAt) }}</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">シナリオ数</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.scenarioCount }}個</p>
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">テンプレート数</label>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ chatbot.templateCount }}個</p>
              </div>
            </div>
          </UCard>

          <!-- Action Buttons -->
          <div class="flex justify-between">
            <UButton 
              color="red" 
              variant="soft"
              icon="i-heroicons-trash"
              @click="deleteChatbot"
              :disabled="saving"
            >
              削除
            </UButton>
            <div class="flex space-x-3">
              <UButton 
                color="gray" 
                variant="soft" 
                @click="$router.back()"
                :disabled="saving"
              >
                キャンセル
              </UButton>
              <UButton 
                color="primary" 
                type="submit"
                :loading="saving"
              >
                更新
              </UButton>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div v-else class="p-6">
      <div class="text-center py-12">
        <UIcon name="i-heroicons-exclamation-triangle" class="text-6xl text-gray-300 mx-auto mb-4" />
        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
          チャットボットが見つかりません
        </h3>
        <p class="text-gray-500 mb-4">
          指定されたIDのチャットボットは存在しません
        </p>
        <UButton color="primary" @click="$router.push('/app/chatbot')">
          一覧に戻る
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useScenarioService } from '~/composables/useScenarioService'

definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const saving = ref(false)

// Scenario service
const { scenarios, fetchScenarios } = useScenarioService()

// 前の設問の選択肢が押された時の挙動オプション
const previousQuestionBehaviorOptions = [
  {
    value: 'mismatch_message',
    label: '不一致メッセージを表示（デフォルト）',
    help: '不一致メッセージを表示し、現在の設問を再表示します'
  },
  {
    value: 'execute_action',
    label: '選択肢のアクションを実行',
    help: '利用者が選択した選択肢に紐づいたアクションを実行します'
  }
]

// 利用可能なシナリオ（API から取得）
const availableScenarios = computed(() => {
  return scenarios.value.filter(scenario => scenario.isActive)
})

// Mock chatbot data
const chatbots = [
  {
    id: 'bot_001',
    name: 'サポートボット',
    description: '一般的なサポート業務を担当するチャットボット',
    selectedScenario: 'scenario_001',
    startKeyword: 'サポート開始',
    previousQuestionBehavior: 'mismatch_message',
    scenarioCount: 5,
    templateCount: 8,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'bot_002',
    name: 'FAQ自動応答ボット',
    description: 'よくある質問に自動で回答するチャットボット',
    selectedScenario: 'scenario_002',
    startKeyword: 'FAQ開始',
    previousQuestionBehavior: 'execute_action',
    scenarioCount: 12,
    templateCount: 15,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-18')
  }
]

const chatbot = computed(() => {
  return chatbots.find(bot => bot.id === route.params.id)
})

const form = reactive({
  name: '',
  selectedScenario: null,
  startKeyword: '',
  previousQuestionBehavior: 'mismatch_message',
  description: ''
})

const errors = reactive({
  name: '',
  selectedScenario: '',
  startKeyword: '',
  previousQuestionBehavior: ''
})

const formatDateTime = (date: Date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const validateForm = () => {
  // Reset errors
  errors.name = ''
  errors.selectedScenario = ''
  errors.startKeyword = ''
  errors.previousQuestionBehavior = ''

  let isValid = true

  if (!form.name.trim()) {
    errors.name = 'チャットボット名は必須です'
    isValid = false
  }

  if (!form.selectedScenario) {
    errors.selectedScenario = '対応シナリオを選択してください'
    isValid = false
  }

  if (!form.startKeyword.trim()) {
    errors.startKeyword = '開始キーワードは必須です'
    isValid = false
  } else if (form.startKeyword.length > 100) {
    errors.startKeyword = '開始キーワードは100文字以内で入力してください'
    isValid = false
  }

  if (!form.previousQuestionBehavior) {
    errors.previousQuestionBehavior = '前の設問の選択肢が押された時の挙動を選択してください'
    isValid = false
  }

  return isValid
}

// Load chatbot data into form
const loadChatbotData = () => {
  if (chatbot.value) {
    form.name = chatbot.value.name
    form.selectedScenario = chatbot.value.selectedScenario
    form.startKeyword = chatbot.value.startKeyword
    form.previousQuestionBehavior = chatbot.value.previousQuestionBehavior
    form.description = chatbot.value.description || ''
  }
}

const updateChatbot = async () => {
  if (!validateForm()) {
    return
  }
  
  saving.value = true
  
  try {
    // TODO: Implement API call to update chatbot
    console.log('Updating chatbot:', form)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Update local data
    if (chatbot.value) {
      Object.assign(chatbot.value, form)
      chatbot.value.updatedAt = new Date()
    }
    
    const toast = useToast()
    toast.add({
      title: 'チャットボットを更新しました',
      description: `${form.name}の設定が保存されました`,
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの更新に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  } finally {
    saving.value = false
  }
}

const deleteChatbot = async () => {
  const confirmed = confirm('このチャットボットを削除してもよろしいですか？この操作は取り消せません。')
  
  if (!confirmed) return
  
  try {
    // TODO: Implement API call to delete chatbot
    console.log('Deleting chatbot:', route.params.id)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const toast = useToast()
    toast.add({
      title: 'チャットボットを削除しました',
      icon: 'i-heroicons-check-circle',
      color: 'green'
    })
    
    router.push('/app/chatbot')
    
  } catch (error) {
    const toast = useToast()
    toast.add({
      title: 'エラー',
      description: 'チャットボットの削除に失敗しました',
      icon: 'i-heroicons-exclamation-triangle',
      color: 'red'
    })
  }
}

// Load chatbot data and populate form
onMounted(async () => {
  // Load scenarios first
  await fetchScenarios()

  setTimeout(() => {
    loadChatbotData()
    loading.value = false
  }, 500)
})
</script>
