<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="チャットボットログ" />
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-xl font-semibold">ログ管理</h2>
          <p class="text-gray-600 dark:text-gray-400 mt-1">
            チャットボットの動作ログを確認できます
          </p>
        </div>
        <div class="flex space-x-3">
          <UButton color="gray" variant="soft" icon="i-heroicons-arrow-path" @click="refreshLogs">
            更新
          </UButton>
          <UButton color="gray" variant="soft" icon="i-heroicons-arrow-down-tray" @click="exportLogs">
            エクスポート
          </UButton>
        </div>
      </div>

      <!-- Filters -->
      <UCard class="mb-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <UFormGroup label="期間">
            <USelect v-model="filters.period" :options="periodOptions" />
          </UFormGroup>
          <UFormGroup label="公開ステータス">
            <USelect v-model="filters.publishStatus" :options="publishStatusOptions" />
          </UFormGroup>
          <UFormGroup label="ステータス">
            <USelect v-model="filters.status" :options="statusOptions" />
          </UFormGroup>
          <UFormGroup label="チャットボット名">
            <UInput v-model="filters.chatbotName" placeholder="チャットボット名で検索" />
          </UFormGroup>
          <UFormGroup label="友だちID">
            <UInput v-model="filters.friendId" placeholder="友だちIDで検索" />
          </UFormGroup>
        </div>
        <div class="mt-4 flex justify-end space-x-2">
          <UButton color="gray" variant="soft" @click="clearFilters">
            クリア
          </UButton>
          <UButton color="primary" @click="applyFilters">
            検索
          </UButton>
        </div>
      </UCard>

      <!-- Chatbot Logs Table -->
      <UCard>
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr class="border-b border-gray-200 dark:border-gray-700">
                <th class="text-left py-3 px-4 font-semibold text-sm">公開ステータス</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">開始日時</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">チャットボットID</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">チャットボット名</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">友だちID</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">プロフィール名</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">ステータス</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">トーク開設</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">スタッフ対応</th>
                <th class="text-left py-3 px-4 font-semibold text-sm">閲覧</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="log in filteredLogs"
                :key="log.id"
                class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <td class="py-3 px-4">
                  <UBadge
                    :color="getPublishStatusColor(log.publishStatus)"
                    variant="soft"
                    size="sm"
                  >
                    {{ log.publishStatus }}
                  </UBadge>
                </td>
                <td class="py-3 px-4 text-sm">
                  {{ formatDateTime(log.startTime) }}
                </td>
                <td class="py-3 px-4 text-sm font-mono">
                  {{ log.chatbotId }}
                </td>
                <td class="py-3 px-4 text-sm">
                  {{ log.chatbotName }}
                </td>
                <td class="py-3 px-4 text-sm font-mono">
                  {{ log.friendId }}
                </td>
                <td class="py-3 px-4 text-sm">
                  {{ log.profileName }}
                </td>
                <td class="py-3 px-4">
                  <UBadge
                    :color="getStatusColor(log.status)"
                    variant="soft"
                    size="sm"
                  >
                    {{ log.status }}
                  </UBadge>
                </td>
                <td class="py-3 px-4">
                  <UBadge
                    v-if="log.talkOpening !== null"
                    :color="getTalkOpeningColor(log.talkOpening)"
                    variant="soft"
                    size="sm"
                  >
                    {{ log.talkOpening }}
                  </UBadge>
                  <span v-else class="text-gray-400 text-sm">-</span>
                </td>
                <td class="py-3 px-4">
                  <UBadge
                    v-if="log.staffResponse !== null"
                    :color="getStaffResponseColor(log.staffResponse)"
                    variant="soft"
                    size="sm"
                  >
                    {{ log.staffResponse }}
                  </UBadge>
                  <span v-else class="text-gray-400 text-sm">-</span>
                </td>
                <td class="py-3 px-4">
                  <UButton
                    color="blue"
                    variant="ghost"
                    size="sm"
                    icon="i-heroicons-eye"
                    @click="viewChatbotSession(log)"
                  >
                    閲覧
                  </UButton>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Empty State -->
          <div v-if="filteredLogs.length === 0" class="text-center py-12">
            <UIcon name="i-noto-v1-robot" class="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              チャットボットログが見つかりません
            </h3>
            <p class="text-gray-500">
              検索条件を変更してください
            </p>
          </div>
        </div>

        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
          <div class="text-sm text-gray-500">
            {{ totalLogsCount }} 件中 {{ filteredLogs.length }} 件のログを表示
          </div>
          <div class="flex space-x-2">
            <UButton
              color="gray"
              variant="soft"
              size="sm"
              :disabled="currentPage === 1"
              @click="currentPage--"
            >
              前へ
            </UButton>
            <span class="px-3 py-1 text-sm">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <UButton
              color="gray"
              variant="soft"
              size="sm"
              :disabled="currentPage === totalPages"
              @click="currentPage++"
            >
              次へ
            </UButton>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Chatbot Session Detail Modal -->
    <UModal v-model="showDetailModal" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard v-if="selectedLog">
        <template #header>
          <h3 class="text-lg font-semibold">チャットボットセッション詳細</h3>
        </template>

        <AppChatbotChatbotSessionDetail :session="selectedLog" />

        <template #footer>
          <div class="flex justify-end">
            <UButton color="gray" variant="soft" @click="showDetailModal = false">
              閉じる
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'

definePageMeta({
  navigationPage: "app-chatbot-logs",
  middleware: ["permissions"],
});

const showDetailModal = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const itemsPerPage = 20

const periodOptions = [
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '過去7日', value: 'week' },
  { label: '過去30日', value: 'month' },
  { label: 'すべて', value: 'all' }
]

const publishStatusOptions = [
  { label: 'すべて', value: 'all' },
  { label: 'テスト', value: 'test' },
  { label: '公開', value: 'published' }
]

const statusOptions = [
  { label: 'すべて', value: 'all' },
  { label: '対応中', value: 'in_progress' },
  { label: '終了', value: 'completed' },
  { label: '中断', value: 'interrupted' },
  { label: '中断（トーク開設）', value: 'interrupted_talk' },
  { label: '中断（タイムアウト）', value: 'interrupted_timeout' }
]

const filters = reactive({
  period: 'today',
  publishStatus: 'all',
  status: 'all',
  chatbotName: '',
  friendId: ''
})

// Mock chatbot session data
const logs = ref([
  {
    id: 1,
    publishStatus: 'テスト',
    startTime: new Date('2024-01-20T10:30:00'),
    chatbotId: 'CB001',
    chatbotName: 'サポートボット',
    friendId: 'U1234567890abcdef',
    profileName: '田中太郎',
    status: '終了',
    talkOpening: 'なし',
    staffResponse: null,
    chatHistory: [
      {
        timestamp: new Date('2024-01-20T10:30:00'),
        isBot: true,
        content: 'こんにちは！何かお困りのことはありませんか？',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T10:30:15'),
        isBot: false,
        content: '製品について質問があります',
        selectedChoice: '製品に関する質問'
      },
      {
        timestamp: new Date('2024-01-20T10:30:20'),
        isBot: true,
        content: 'どのような製品についてお聞きになりたいですか？',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T10:30:35'),
        isBot: false,
        content: 'ソフトウェアライセンスについて',
        selectedChoice: 'ソフトウェア'
      }
    ],
    userResponses: [
      { question: '設問1: お困りの内容', answer: '製品に関する質問' },
      { question: '設問2: 製品カテゴリ', answer: 'ソフトウェア' }
    ]
  },
  {
    id: 2,
    publishStatus: '公開',
    startTime: new Date('2024-01-20T11:15:00'),
    chatbotId: 'CB002',
    chatbotName: 'FAQ自動応答ボット',
    friendId: 'U2345678901bcdefg',
    profileName: '佐藤花子',
    status: '中断（トーク開設）',
    talkOpening: '開設',
    staffResponse: '未',
    chatHistory: [
      {
        timestamp: new Date('2024-01-20T11:15:00'),
        isBot: true,
        content: 'よくある質問にお答えします。どのようなことでお困りですか？',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T11:15:20'),
        isBot: false,
        content: '複雑な問題があります',
        selectedChoice: 'その他・複雑な問題'
      },
      {
        timestamp: new Date('2024-01-20T11:15:25'),
        isBot: true,
        content: 'スタッフがお手伝いします。1:1トークを開設いたします。',
        selectedChoice: null
      }
    ],
    userResponses: [
      { question: '設問1: お困りの内容', answer: 'その他・複雑な問題' }
    ]
  },
  {
    id: 3,
    publishStatus: 'テスト',
    startTime: new Date('2024-01-20T09:45:00'),
    chatbotId: 'CB001',
    chatbotName: 'サポートボット',
    friendId: 'U3456789012cdefgh',
    profileName: '鈴木一郎',
    status: '中断（タイムアウト）',
    talkOpening: null,
    staffResponse: null,
    chatHistory: [
      {
        timestamp: new Date('2024-01-20T09:45:00'),
        isBot: true,
        content: 'こんにちは！何かお困りのことはありませんか？',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T09:45:30'),
        isBot: false,
        content: '料金について',
        selectedChoice: '料金・請求'
      }
    ],
    userResponses: [
      { question: '設問1: お困りの内容', answer: '料金・請求' }
    ]
  },
  {
    id: 4,
    publishStatus: '公開',
    startTime: new Date('2024-01-20T14:20:00'),
    chatbotId: 'CB003',
    chatbotName: '営業時間外対応ボット',
    friendId: 'U4567890123defghi',
    profileName: '山田次郎',
    status: '中断（トーク開設）',
    talkOpening: '時間外',
    staffResponse: '済',
    chatHistory: [
      {
        timestamp: new Date('2024-01-20T14:20:00'),
        isBot: true,
        content: '申し訳ございませんが、現在営業時間外です。',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T14:20:15'),
        isBot: false,
        content: '緊急の問題があります',
        selectedChoice: '緊急対応希望'
      }
    ],
    userResponses: [
      { question: '設問1: 対応希望', answer: '緊急対応希望' }
    ]
  },
  {
    id: 5,
    publishStatus: '公開',
    startTime: new Date('2024-01-20T16:30:00'),
    chatbotId: 'CB002',
    chatbotName: 'FAQ自動応答ボット',
    friendId: 'U5678901234efghij',
    profileName: '高橋美咲',
    status: '対応中',
    talkOpening: null,
    staffResponse: null,
    chatHistory: [
      {
        timestamp: new Date('2024-01-20T16:30:00'),
        isBot: true,
        content: 'よくある質問にお答えします。どのようなことでお困りですか？',
        selectedChoice: null
      },
      {
        timestamp: new Date('2024-01-20T16:30:25'),
        isBot: false,
        content: 'パスワードを忘れました',
        selectedChoice: 'パスワード関連'
      },
      {
        timestamp: new Date('2024-01-20T16:30:30'),
        isBot: true,
        content: 'パスワードリセットの手順をご案内します。',
        selectedChoice: null
      }
    ],
    userResponses: [
      { question: '設問1: お困りの内容', answer: 'パスワード関連' }
    ]
  }
])

const filteredLogs = computed(() => {
  let result = logs.value

  // Apply filters
  if (filters.publishStatus !== 'all') {
    result = result.filter(log => log.publishStatus === filters.publishStatus)
  }

  if (filters.status !== 'all') {
    result = result.filter(log => log.status === filters.status)
  }

  if (filters.chatbotName) {
    result = result.filter(log =>
      log.chatbotName.toLowerCase().includes(filters.chatbotName.toLowerCase())
    )
  }

  if (filters.friendId) {
    result = result.filter(log =>
      log.friendId.toLowerCase().includes(filters.friendId.toLowerCase())
    )
  }

  // Apply pagination
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return result.slice(start, end)
})

const totalLogsCount = computed(() => {
  let result = logs.value

  // Apply same filters for total count
  if (filters.publishStatus !== 'all') {
    result = result.filter(log => log.publishStatus === filters.publishStatus)
  }

  if (filters.status !== 'all') {
    result = result.filter(log => log.status === filters.status)
  }

  if (filters.chatbotName) {
    result = result.filter(log =>
      log.chatbotName.toLowerCase().includes(filters.chatbotName.toLowerCase())
    )
  }

  if (filters.friendId) {
    result = result.filter(log =>
      log.friendId.toLowerCase().includes(filters.friendId.toLowerCase())
    )
  }

  return result.length
})

const totalPages = computed(() => {
  return Math.ceil(totalLogsCount.value / itemsPerPage)
})

const formatDateTime = (date) => {
  return new Intl.DateTimeFormat('ja-JP', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

const getPublishStatusColor = (status) => {
  switch (status) {
    case 'テスト': return 'yellow'
    case '公開': return 'green'
    default: return 'gray'
  }
}

const getStatusColor = (status) => {
  switch (status) {
    case '対応中': return 'blue'
    case '終了': return 'green'
    case '中断': return 'yellow'
    case '中断（トーク開設）': return 'orange'
    case '中断（タイムアウト）': return 'red'
    default: return 'gray'
  }
}

const getTalkOpeningColor = (talkOpening) => {
  switch (talkOpening) {
    case 'なし': return 'gray'
    case '開設': return 'green'
    case '時間外': return 'yellow'
    case '流入制限': return 'red'
    default: return 'gray'
  }
}

const getStaffResponseColor = (staffResponse) => {
  switch (staffResponse) {
    case '未': return 'yellow'
    case '済': return 'green'
    default: return 'gray'
  }
}

const viewChatbotSession = (log) => {
  selectedLog.value = log
  showDetailModal.value = true
}

const refreshLogs = () => {
  // TODO: Implement API call to refresh chatbot logs
  const toast = useToast()
  toast.add({
    title: 'チャットボットログを更新しました',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const exportLogs = () => {
  // TODO: Implement chatbot log export functionality
  const toast = useToast()
  toast.add({
    title: 'チャットボットログのエクスポートを開始しました',
    description: 'ダウンロードが完了するまでお待ちください',
    icon: 'i-heroicons-check-circle',
    color: 'green'
  })
}

const clearFilters = () => {
  Object.assign(filters, {
    period: 'today',
    publishStatus: 'all',
    status: 'all',
    chatbotName: '',
    friendId: ''
  })
  currentPage.value = 1
}

const applyFilters = () => {
  currentPage.value = 1
  // Filters are applied automatically through computed property
}
</script>
