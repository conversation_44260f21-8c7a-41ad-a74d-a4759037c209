<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { CounselorRole } from "~/types/enums.d";
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);
  definePageMeta({
    middleware: ["permissions"],
    navigationPage: "app-settings",
    roles: [
      CounselorRole.ADMIN,
      CounselorRole.GENERAL,
      CounselorRole.SUPERVISOR,
    ],
  });
</script>

<template>
  <div>
    <AppSubNavigation title="設定">
      <div class="p-2">
        <SettingsMenu />
      </div>
    </AppSubNavigation>
    <main
      class="relative h-auto min-h-screen transition-all duration-200 space-y-6"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <NuxtPage />
    </main>
  </div>
</template>
