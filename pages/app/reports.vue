<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppUIStore } from '~/stores/app/ui'
const appUIStore = useAppUIStore()
const { isSubNavigationMini } = storeToRefs(appUIStore)
definePageMeta({
  navigationPage: 'app-reports',
  middleware: ["permissions"],
})
</script>

<template>
  <div>
    <AppSubNavigation title="設定">
      <div class="p-2">
        <ReportsMenu />
      </div>
    </AppSubNavigation>
    <main
      class="relative flex flex-col h-auto min-h-screen transition-all duration-200 space-y-6"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <NuxtPage />
    </main>
  </div>
</template>
