<template>
  <div>
    <div
      class="sticky top-0 bg-gray-100 dark:bg-gray-800 bg-opacity-95 z-50 pt-3 pb-2 px-4 border-b dark:border-gray-700 font-semibold text-md flex flex-inline justify-between"
    >
      <div class="truncate">相談分類管理</div>
    </div>
    <div class="p-6">
      <BaseTable
        title="相談分類一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalTagsCount"
      >
        <template #header-right>
          <UButton
            v-if="userPermissions.includes('create:app-settings-tags')"
            size="md"
            label="追加"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="onAddTag"
          />
        </template>

        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="tagsStore.fetchTags()"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="caseTags"
          v-model:sort="sort"
          :loading="loadings['fetchTags']"
        >
          <template #formTemplateId-data="{ row }">
            <div>
              {{ $t(row.formType) }}
            </div>
          </template>
          <template #createdAt-data="{ row }">
            <div>
              {{
                formatDate(new Date(row.createdAt), "YYYY年MM月DD日 HH時mm分")
              }}
            </div>
          </template>
          <template #action-data="{ row }">
            <div class="flex space-x-2">
              <UButton
                icon="i-heroicons-trash"
                size="xs"
                color="red"
                variant="soft"
                label="削除"
                :trailing="false"
                :key="row.tagId"
                v-confirm="{
                  title: '相談分類の削除',
                  message: `「${row.label}」の相談分類を削除してもよろしいですか？`,
                  confirmButtonText: 'はい、削除する',
                  cancelButtonText: 'いいえ',
                  onConfirm: () => deleteTag(row),
                }"
                :loading="
                  loadings.deleteTag ? loadings['deleteTag'][row.tagId] : false
                "
              />
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                :trailing="false"
                @click="onUpdateTag(row)"
                :loading="
                  loadings.updateTag ? loadings.updateTag[row.tagId] : false
                "
              />
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
    <TagModal
      :show="isTagModal"
      :is-add-new="isAddNewTag"
      :data="selectedTag"
      @add-new="createTag"
      @update="updateTag"
      @close="closeTagModal"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import type { CaseTag } from "~/types";
  import { useTagsStore } from "~/stores/app/tags";
  import { useAppCustomersStore } from "~/stores/app/customers";
  definePageMeta({
    middleware: ["permissions"],
  });
  const userPermissions = usePermissions();

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);

  const route = useRoute();
  const toast = useToast();
  const { t } = useI18n();
  const tagsStore = useTagsStore();

  const {
    loadings,
    pagination,
    caseTags,
    pageTo,
    pageFrom,
    sortConditions,
    totalTagsCount,
  } = storeToRefs(tagsStore);

  const columns = computed(() => {
    return [
      {
        key: "tagId",
        label: "相談分類ID",
        sortable: true,
      },
      {
        key: "tagName",
        label: "相談分類名",
        sortable: true,
      },
      {
        key: "formTemplateId",
        label: "フォームテンプレートタイプ",
        sortable: true,
      },
      {
        key: "createdAt",
        label: "作成日時",
        sortable: true,
      },
      {
        label: "#",
        key: "action",
        class: "text-center w-0",
        canNotUse: !userPermissions.value.includes("update:app-settings-tags"),
      },
    ].filter((column) => !column.canNotUse);
  });

  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        tagsStore.fetchTags();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        tagsStore.fetchTags();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        tagsStore.fetchTags();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  onMounted(() => {
    const query = route.query;
    let _pagination, _sortConditions;
    if (query) {
      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      tagsStore.updateConditions(_pagination, _sortConditions);
    }
  });

  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const isTagModal = ref(false);
  const isAddNewTag = ref(false);
  const selectedTag = ref(null) as Ref<CaseTag | null>;
  const closeTagModal = () => {
    isTagModal.value = false;
  };
  const onAddTag = () => {
    isAddNewTag.value = true;
    selectedTag.value = null;

    isTagModal.value = true;
  };

  const onUpdateTag = (caseTag: CaseTag) => {
    isAddNewTag.value = false;
    selectedTag.value = caseTag;

    isTagModal.value = true;
  };

  const createTag = async (caseTag: CaseTag) => {
    console.log("🚀 ~ file: tags.vue:167 ~ createTag ~ caseTag:", caseTag);
    const result = await tagsStore.createCaseTag(caseTag);
    selectedTag.value = null;

    if (result) {
      toast.add({
        title: t("Add new success"),
        description: t("Add new case tag successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const deleteTag = async (caseTag: CaseTag) => {
    const result = await tagsStore.deleteTag(caseTag);

    if (result) {
      toast.add({
        title: t("Delete success"),
        description: t("Delete case tag successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const updateTag = async (caseTag: CaseTag) => {
    const result = await tagsStore.updateTag(caseTag);
    selectedTag.value = null;

    if (result) {
      toast.add({
        title: t("Update success"),
        description: t("Update case tag successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };
</script>
