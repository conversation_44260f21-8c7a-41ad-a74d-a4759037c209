<template>
  <div>
    <div
      class="sticky top-0 bg-gray-100 bg-opacity-95 dark:bg-gray-800 z-50 pt-3 pb-2 px-4 border-b dark:border-gray-700 font-semibold text-md flex flex-inline justify-between"
    >
      <div class="truncate">定型文</div>
    </div>
    <div class="p-6">
      <BaseTable
        title="定型文一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalSampleMessagesCount"
      >
        <template #header-right>
          <UButton
            v-if="
              userPermissions.includes('create:app-settings-sample-messages') ||
              userPermissions.includes(
                'create:app-settings-sample-messages-personal',
              )
            "
            size="md"
            label="追加"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="onAddSampleMessage"
          />
        </template>

        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="sampleMessagesStore.fetchSampleMessages()"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="sapmleMessages"
          v-model:sort="sort"
          :loading="loadings['fetchSampleMessages']"
        >
          <template #type-data="{ row }">
            <div>
              <UBadge :color="getSampleMessageTypeColor(row.type)">
                {{ $t(row.type) }}
              </UBadge>
            </div>
          </template>
          <template #action-data="{ row }">
            <div
              v-if="
                userPermissions.includes('update:app-settings-sample-messages') || row.type === 'personal'
              "
              class="flex space-x-2"
            >
              <UButton
                icon="i-heroicons-trash"
                size="xs"
                color="red"
                variant="soft"
                label="削除"
                :trailing="false"
                :key="row.textTemplateId"
                v-confirm="{
                  title: '定型文の削除',
                  message: `「${row.title}」の定型文を削除してもよろしいですか？`,
                  confirmButtonText: 'はい、削除する',
                  cancelButtonText: 'いいえ',
                  onConfirm: () => deleteSampleMessage(row),
                }"
                :loading="
                  loadings.deleteSampleMessage
                    ? loadings['deleteSampleMessage'][row.textTemplateId || '']
                    : false
                "
              />
              <UButton
                icon="i-heroicons-pencil-square"
                size="xs"
                color="primary"
                variant="soft"
                label="編集"
                :trailing="false"
                @click="onUpdateSampleMessage(row)"
                :loading="
                  loadings.updateSampleMessage
                    ? loadings.updateSampleMessage[row.textTemplateId]
                    : false
                "
              />
            </div>
            <div v-else></div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
    <SampleMessagesModal
      :show="isShowSampleMessageModal"
      :is-add-new="isAddNewSampleMessage"
      :sample-message="selectedSampleMessage"
      @add-new="createSampleMessage"
      @update="updateSampleMessage"
      @close="closeSampleMessageModal"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import type { SampleMessage } from "~/types";
  import { useSampleMessagesStore } from "~/stores/app/sample-messages";
  import { useAppCustomersStore } from "~/stores/app/customers";
  definePageMeta({
    middleware: ["permissions"],
  });
  const userPermissions = usePermissions();
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
  const toast = useToast();
  const { t } = useI18n();
  const route = useRoute();
  const sampleMessagesStore = useSampleMessagesStore();

  const {
    loadings,
    pagination,
    sapmleMessages,
    pageTo,
    pageFrom,
    sortConditions,
    totalSampleMessagesCount,
  } = storeToRefs(sampleMessagesStore);

  const columns = computed(() => {
    return [
      {
        key: "type",
        label: "種類",
        sortable: true,
      },
      {
        key: "group",
        label: "グループ",
        sortable: true,
      },
      {
        key: "title",
        label: "定型文タイトル",
        sortable: true,
      },
      {
        key: "createdAt",
        label: "作成日時",
        sortable: true,
      },
      {
        label: "#",
        key: "action",
        class: "text-center w-0",
        canNotUse:
          !userPermissions.value.includes(
            "update:app-settings-sample-messages",
          ) &&
          !userPermissions.value.includes(
            "update:app-settings-sample-messages-personal",
          ),
      },
    ].filter((column) => !column.canNotUse);
  });

  watch(
    () => pagination.value,
    (newValue, oldValue) => {
      if (Object.keys(pagination.value).length > 0 && oldValue?.page) {
        sampleMessagesStore.fetchSampleMessages();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => sortConditions.value,
    (newValue, oldValue) => {
      if (Object.keys(sortConditions.value).length > 0 && oldValue?.sortBy) {
        sampleMessagesStore.fetchSampleMessages();
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => currentCustomer.value,
    (newValue, oldValue: any) => {
      if (newValue?.customerId !== oldValue?.customerId) {
        sampleMessagesStore.fetchSampleMessages();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  onMounted(() => {
    const query = route.query;
    let _pagination, _sortConditions;
    if (query) {
      _pagination = {
        page: Number(query.page) || 1,
        pageRangeDisplayed: Number(query.pageRangeDisplayed) || 10,
      };

      _sortConditions = {
        sortBy: query.sortBy as string,
        sortDesc: query.sortDesc === "true" ? true : false,
      };

      sampleMessagesStore.updateConditions(_pagination, _sortConditions);
    }
  });

  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const isShowSampleMessageModal = ref(false);
  const isAddNewSampleMessage = ref(false);
  const selectedSampleMessage = ref(null) as Ref<SampleMessage | null>;
  const closeSampleMessageModal = () => {
    isShowSampleMessageModal.value = false;
  };
  const onAddSampleMessage = () => {
    isAddNewSampleMessage.value = true;
    selectedSampleMessage.value = null;

    isShowSampleMessageModal.value = true;
  };

  const onUpdateSampleMessage = (sampleMessage: SampleMessage) => {
    isAddNewSampleMessage.value = false;
    selectedSampleMessage.value = sampleMessage;

    isShowSampleMessageModal.value = true;
  };

  const createSampleMessage = async (sampleMessage: SampleMessage) => {
    const result = await sampleMessagesStore.createSampleMessage(sampleMessage);
    selectedSampleMessage.value = null;

    if (result) {
      toast.add({
        title: t("Add new success"),
        description: t("Add new sample message successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const deleteSampleMessage = async (sampleMessage: SampleMessage) => {
    const result = await sampleMessagesStore.deleteSampleMessage(sampleMessage);

    if (result) {
      toast.add({
        title: t("Delete success"),
        description: t("Delete sample message successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const updateSampleMessage = async (sampleMessage: SampleMessage) => {
    const result = await sampleMessagesStore.updateSampleMessage(sampleMessage);
    selectedSampleMessage.value = null;

    if (result) {
      toast.add({
        title: t("Update success"),
        description: t("Update sample message successfully"),
        icon: "i-heroicons-check-circle",
      });
    }
  };
</script>
