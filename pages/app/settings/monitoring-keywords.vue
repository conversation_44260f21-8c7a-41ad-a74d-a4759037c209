<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="監視キーワード " />
    <div class="p-6">
      <UCard>
        <div
          v-if="
            userPermissions.includes('create:app-settings-monitoring-keywords')
          "
        >
          <UButton
            class="mb-2"
            icon="i-heroicons-plus"
            size="sm"
            variant="soft"
            @click="onAddNewKeyword"
            >監視キーワードを追加</UButton
          >
          <UDivider class="mb-3 mt-2" />
        </div>
        <div
          class="flex flex-col space-y-3"
          v-if="monitoringKeywordsLocal.length"
        >
          <div
            v-for="(keyword, index) in monitoringKeywordsLocal"
            class="flex items-start flex-row space-x-2"
          >
            <UFormGroup
              v-slot="{ error }"
              :error="errors[index]"
              class="w-full"
            >
              <UInput
                ref="inputEls"
                v-model="monitoringKeywordsLocal[index]"
                variant="outline"
                class="w-full"
                :trailing-icon="
                  error
                    ? 'i-heroicons-exclamation-triangle-20-solid'
                    : undefined
                "
                placeholder="監視キーワードを入力してください。"
                :autofocus="index === 0 ? true : false"
                :disabled="
                  !userPermissions.includes(
                    'update:app-settings-monitoring-keywords',
                  )
                "
              />
            </UFormGroup>
            <UButton
              v-can="'update:app-settings-monitoring-keywords'"
              icon="i-heroicons-trash"
              size="sm"
              color="red"
              square
              variant="soft"
              @click="onDeleteKeyword(index)"
            >
            </UButton>
          </div>
        </div>
        <div v-else class="">
          <BaseEmpty
            text="監視キーワードがありません"
            icon="oui:vis-text"
            class="flex flex-col items-center justify-center py-10 gap-3"
          />
        </div>
        <template #footer>
          <UButton
            v-if="
              userPermissions.includes(
                'create:app-settings-monitoring-keywords',
              )
            "
            icon="i-ic-outline-save"
            class="px-6"
            :disabled="!isAllInputHasValue"
            @click="onUpdate"
            :loading="loadings.updateMonitoringKeywords"
          >
            {{ $t("Save") }}
          </UButton>
        </template>
      </UCard>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </div>
</template>

<script setup lang="ts">
  import { useMonitoringKeywordsStore } from "~/stores/app/monitoring-words";
  import { storeToRefs } from "pinia";
  definePageMeta({
    middleware: ["permissions"],
  });
  const monitoringWordsStore = useMonitoringKeywordsStore();
  const { monitoringKeywords, loadings } = storeToRefs(monitoringWordsStore);
  const userPermissions = usePermissions();
  const monitoringKeywordsLocal = ref([] as string[]);
  const errors = ref({} as ErrorDynamic);
  const inputEls = ref([] as any[]);
  watch(
    () => monitoringKeywordsLocal.value,
    (value) => {
      const _errors = {} as ErrorDynamic;
      monitoringKeywordsLocal.value.forEach((keyword, index) => {
        if (
          keyword &&
          monitoringKeywordsLocal.value?.filter((v) => v === keyword).length > 1
        ) {
          _errors[index] = "監視キーワードが重複しています。";
        }
      });

      errors.value = _errors;
    },
    { immediate: true, deep: true },
  );

  watch(
    monitoringKeywords,
    (monitoringKeywords) => {
      monitoringKeywordsLocal.value = monitoringKeywords;
    },
    { immediate: true },
  );

  const isAllInputHasValue = computed(() => {
    return (
      monitoringKeywordsLocal.value.filter((v) => v === "").length === 0 &&
      Object.keys(errors.value).length === 0
    );
  });
  const onDeleteKeyword = (index: number) => {
    monitoringKeywordsLocal.value.splice(index, 1);
  };

  const onAddNewKeyword = () => {
    monitoringKeywordsLocal.value.unshift("");
    nextTick(() => {
      inputEls.value[0]?.input.focus();
    });
  };

  const toast = useToast();
  const onUpdate = async () => {
    const result = await monitoringWordsStore.updateMonitoringKeywords(
      monitoringKeywordsLocal.value,
    );
    if (result) {
      toast.add({
        title: "更新成功",
        icon: "i-heroicons-check-circle",
        description: "監視キーワードを更新しました。",
      });
    }
  };
</script>
