<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="アカウント設定" />
    <div class="p-6">
      <UCard>
        <div class="flex justify-between">
          <div class="flex flex-col space-y-4">
            <div v-if="localCustomer.basic" class="flex items-end space-x-3">
              <UFormGroup label="タイトル">
                <UInput
                  icon="i-carbon-label"
                  size="md"
                  class="w-[370px]"
                  :ui="{ icon: { trailing: { pointer: '' } } }"
                  :disabled="
                    !userPermissions.includes('update:app-settings-accounts')
                  "
                  v-model="localCustomer.basic.customerName"
                >
                </UInput>
              </UFormGroup>
            </div>
            <div class="">
              <div class="flex flex-col space-y-2">
                <div class="text-sm">テーマ</div>
                <div class="grid grid-cols-4 gap-4">
                  <UCard
                    v-for="color in appConfig.ui.colors.filter(
                      (c) => c !== 'primary' && c !== 'theme',
                    )"
                    :key="color"
                    :ui="{
                      body: {
                        padding: '!px-3 !py-2',
                      },
                    }"
                    class="group cursor-pointer hover:shadow-lg border-2 border-transparent"
                    :class="[
                      {
                        'border-primary-500 dark:border-gray-300':
                          localCustomer.theme === color,
                      },
                    ]"
                    @click="setPrimaryColor(color)"
                  >
                    <div class="flex items-center space-x-2">
                      <div
                        class="h-4 w-4 rounded-full cursor-pointer"
                        :class="[`bg-${color}-500`]"
                      />
                      <div class="text-sm">{{ $t(color) }}</div>
                    </div>
                  </UCard>
                </div>
              </div>
            </div>
          </div>
          <div
            class="relative border-l dark:border-gray-700 w-1/3 flex flex-col items-center justify-center space-y-3 mt-2"
          >
            <UAvatar
              :ui="{
                rounded: 'rounded-xl',
                background: 'bg-gray-300 dark:bg-gray-400',
                placeholder:
                  'text-4xl uppercase text-gray-700 dark:text-gray-800',
                wrapper: 'border',
                size: {
                  '3xl': 'h-32 w-32',
                },
              }"
              :src="localCustomer?.basic?.customerImage"
              :alt="localCustomer?.basic?.customerName"
              size="3xl"
              chip-position="top-right"
            >
              <UButton
                v-if="localCustomer?.basic?.customerImage"
                v-can="'update:app-settings-accounts'"
                class="absolute -top-3 -right-3 z-10"
                variant="soft"
                icon="i-heroicons-x-mark-solid"
                size="xs"
                color="red"
                square
                :ui="{ rounded: 'rounded-full' }"
                @click="onRemoveAvatar"
              >
              </UButton>
            </UAvatar>
            <div class="flex flex-col space-y-1">
              <input
                ref="fileInputEl"
                type="file"
                class="hidden"
                accept="image/*"
                @change="handleFileUpload"
              />
              <UButton
                v-can="'update:app-settings-accounts'"
                variant="outline"
                icon="i-mdi-image-plus"
                size="sm"
                @click="fileInputEl?.click()"
              >
                アバター変更
              </UButton>
            </div>
          </div>
        </div>
        <UAlert
          v-if="errors['currentCustomer']"
          class="mt-6"
          icon="i-heroicons-information-circle"
          color="red"
          variant="subtle"
          title="エラーが発生しました。"
          :description="errors['currentCustomer']"
        />
        <template #footer>
          <div
            v-can="'update:app-settings-accounts'"
            class="flex flex-row space-x-3"
          >
            <UButton
              icon="i-ic-outline-save"
              class="px-6"
              @click="onSaveSetting"
              :loading="loadings['updateCustomer']"
            >
              {{ $t("Save") }}
            </UButton>
            <UButton
              icon="i-material-symbols-reset-wrench"
              color="gray"
              class="px-6"
              @click="onResetSetting"
              :disabled="loadings['updateCustomer']"
            >
              リセット
            </UButton>
          </div>
        </template>
      </UCard>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { cloneDeep } from "lodash";
  import { useAppUIStore } from "~/stores/app/ui";
  definePageMeta({
    middleware: ["permissions"],
  });
  const userPermissions = usePermissions();
  const appCustomersStore = useAppCustomersStore();
  const { loadings, currentCustomer, errors } = storeToRefs(appCustomersStore);
  const appConfig = useAppConfig();
  const appUIStore = useAppUIStore();
  const { projectColors } = storeToRefs(appUIStore);
  const setPrimaryColor = (color: string) => {
    if (userPermissions.value.includes("update:app-settings-accounts")) {
      appConfig.ui.primary = color;
      localCustomer.value.theme = color;
    }
  };

  const localCustomer = ref(cloneDeep(currentCustomer.value));

  const fileInputEl = ref<HTMLInputElement | null>(null);
  const imageFile = ref<string | null>(
    localCustomer.value.basic?.customerImage || null,
  );
  const handleFileUpload = (e: Event) => {
    const files = (e.target as HTMLInputElement).files as FileList;
    if (files?.length > 0) {
      const reader = new FileReader();
      reader.onload = (e) => {
        imageFile.value = e.target?.result as string;
        localCustomer.value.basic.customerImage = e.target?.result as string;
      };
      reader.readAsDataURL(files[0]);
    }
  };

  const onRemoveAvatar = () => {
    imageFile.value = null;
    localCustomer.value.basic.customerImage = "";
  };
  watch(
    () => currentCustomer.value,
    (value) => {
      localCustomer.value = cloneDeep(value);
    },
  );
  const toast = useToast();
  const { t } = useI18n();
  const onSaveSetting = async () => {
    const result = await appCustomersStore.updateCustomer(localCustomer.value);
    if (result) {
      toast.add({
        title: t("Update success"),
        description: t("Your changes have been successfully saved"),
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const onResetSetting = () => {
    localCustomer.value = cloneDeep(currentCustomer.value);
    appConfig.ui.primary = currentCustomer.value.theme || "primary";
    projectColors.value[currentCustomer.value.customerId || ""].primary =
      currentCustomer.value.theme || "primary";
  };
</script>
