<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="相談期間" />
    <div class="p-6">
      <UCard>
        <div class="space-y-2">
          <div>営業時間の設定</div>
          <BaseDaySelect
            :disabled="
              !userPermissions.includes('update:app-settings-counseling-term')
            "
          />
        </div>
        <UDivider class="mt-6 mb-5" />
        <div class="space-y-2">
          <div class="flex flex-row space-x-4">
            <div class="flex-1 flex flex-col">
              <WorkingDayTimeSpecialTargets
                :disabled="
                  !userPermissions.includes(
                    'update:app-settings-counseling-term',
                  )
                "
              />
            </div>
            <div class="flex flex-col space-y-4">
              <WorkingDayTimeCalendar
                :disabled="
                  !userPermissions.includes(
                    'update:app-settings-counseling-term',
                  )
                "
              />
              <UCard
                :ui="{
                  body: {
                    padding: '!pb-2 !pt-2 !px-4',
                  },
                }"
              >
                <div class="flex flex-col space-y-2">
                  <div class="font-bold text-sm">注記</div>
                  <div class="grid grid-cols-2 gap-1">
                    <div class="inline-flex items-center text-xs space-x-1">
                      <div class="w-1.5 h-1.5 rounded-full bg-green-600" />
                      <div>固定営業日</div>
                    </div>
                    <div class="inline-flex items-center text-xs space-x-1">
                      <div class="w-1.5 h-1.5 rounded-full bg-red-600" />
                      <div>定休日</div>
                    </div>
                  </div>
                  <UDivider />
                  <div class="grid grid-cols-2 gap-1">
                    <div class="inline-flex items-center text-xs space-x-1">
                      <div class="w-5 h-5 rounded-full bg-green-500/50" />
                      <div>特別営業日</div>
                    </div>
                    <div class="inline-flex items-center text-xs space-x-1">
                      <div class="w-5 h-5 rounded-full bg-red-500/50" />
                      <div>特別休業日</div>
                    </div>
                  </div>
                  <UDivider />
                  <div class="grid grid-cols-2 gap-1">
                    <div class="inline-flex items-center text-xs space-x-1">
                      <div class="w-6 h-1 bg-fuchsia-500/80" />
                      <div>祝日</div>
                    </div>
                  </div>
                </div>
              </UCard>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useCounselingTermsStore } from "~/stores/app/counseling-terms";
  import { useAppCustomersStore } from "~/stores/app/customers";
  definePageMeta({
    middleware: ["permissions"],
  });
  const userPermissions = usePermissions();
  const counselingTermsStore = useCounselingTermsStore();
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer, currentCustomerId } = storeToRefs(appCustomersStore);
  const { loadings } = storeToRefs(counselingTermsStore);

  onMounted(async () => {
    await counselingTermsStore.fetchCounselingTerms(
      currentCustomerId.value as string,
    );
    counselingTermsStore.updateAttribute();
  });
  watch(
    () => currentCustomer.value,
    async () => {
      await counselingTermsStore.fetchCounselingTerms(
        currentCustomer.value.customerId as string,
      );
      counselingTermsStore.updateAttribute();
    },
  );
</script>
