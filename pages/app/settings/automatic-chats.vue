<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="自動チャット設定 " />
    <div class="flex flex-row px-6 space-x-6">
      <div class="py-6 flex-1">
        <UCard>
          <div class="flex flex-col space-y-4">
            <div>
              <div class="font-semibold">相談プラットフォーム</div>
            </div>
            <div class="flex flex-row space-x-3 w-full h-full">
              <div v-for="(item, index) in chatTypes" class="w-full h-full">
                <SettingsChatType
                  v-model="chatTypes[index]"
                  :active="chatType === item.value"
                  @select="showBrowser = true"
                />
              </div>
            </div>
            <UDivider />
            <div>
              <div class="font-semibold">自動メッセージ設定</div>
            </div>
            <div v-for="(item, index) in autoMessageSettings">
              <SettingsAutoMessage
                v-model="settings"
                v-bind="item"
                :active="activeSettingAutoMessage.title === item.title"
                @select="
                  activeSettingAutoMessage = item;
                  showBrowser = false;
                "
                :disabled="
                  !userPermissions.includes(
                    'update:app-settings-automatic-chats',
                  )
                "
              />
            </div>
          </div>
          <UAlert
            v-if="errors['updateSettings']"
            class="mt-6"
            icon="i-heroicons-information-circle"
            color="red"
            variant="subtle"
            title="エラーが発生しました。"
            :description="errors['updateSettings']"
          />

          <template #footer>
            <UButton
              v-can="'update:app-settings-automatic-chats'"
              icon="i-ic-outline-save"
              class="px-6"
              @click="onUpdateSettings"
              :loading="loadings['updateSettings'] === true"
            >
              {{ $t("Save") }}
            </UButton>
          </template>
        </UCard>

        <BaseCopyright
          class="mt-4 flex flex-row items-center justify-center"
          textClass="!text-gray-800 dark:!text-gray-300"
          wrapClass="bg-transparent"
        />
      </div>
      <div class="pt-6">
        <BaseiPhone12Mockup
          class="sticky top-[70px]"
          :loading="loadings['fetchSettings'] === true"
        >
          <BaseLineAppLoader v-if="loadings['fetchSettings'] === true" />
          <div class="h-full" v-show="loadings['fetchSettings'] === false">
            <Transition
              name="slide-up"
              enter-active-class="animate__animated animate__slideInUp animate__faster"
              leave-active-class="animate__animated animate__slideOutDown animate__faster"
            >
              <BaseLiffBrowserFrame
                v-if="settings.chatType === 'liff' && showBrowser"
                @close="showBrowser = false"
              >
                <iframe
                  class="w-full h-full"
                  src="/liff-app/chat"
                  frameborder="0"
                />
              </BaseLiffBrowserFrame>
            </Transition>
            <Transition
              name="slide-up"
              enter-active-class="animate__animated animate__slideInRight animate__faster"
              leave-active-class="animate__animated animate__slideOutRight animate__faster"
            >
              <BaseExternalBrowserFrame
                v-if="settings.chatType === 'browser' && showBrowser"
              >
                <iframe
                  class="w-full h-full"
                  src="/liff-app/chat"
                  frameborder="0"
                />
              </BaseExternalBrowserFrame>
            </Transition>
            <Transition>
              <BaseLineAppFrame v-show="loadings['fetchSettings'] === false">
                <div class="py-2">
                  <BaseLineMessage
                    :message="
                      (settings[activeSettingAutoMessage.isEnable] &&
                        settings[activeSettingAutoMessage.message]) ||
                      activeSettingAutoMessage.title
                    "
                    :survey="
                      activeSettingAutoMessage.hasSurvey && settings.survey
                    "
                  />
                </div>
              </BaseLineAppFrame>
            </Transition>
          </div>
        </BaseiPhone12Mockup>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useAutomaticChatsStore } from "~/stores/app/automatic-chats";
  import { storeToRefs } from "pinia";
  import type { SettingsChatType } from "#build/components";
  import { useAppCustomersStore } from "~/stores/app/customers";
  definePageMeta({
    middleware: ["permissions"],
  });
  const userPermissions = usePermissions();
  const automaticChatsStore = useAutomaticChatsStore();
  const { chatType, settings, loadings, activeSettingAutoMessage, errors } =
    storeToRefs(automaticChatsStore);

  const autoMessageSettings = ref([
    {
      icon: "i-mingcute-user-add-line",
      title: "友達追加時の自動メッセージ",
      isEnable: "useFollowWord",
      message: "followWord",
    },
    {
      icon: "i-tabler-message-question",
      title: "自動質問前の自動メッセージ",
      isEnable: "useBeforeSurveyWord",
      message: "beforeSurveyWord",
      hasSurvey: true,
    },
    {
      icon: "i-tabler-message-2-off",
      title: "受付期間前の自動メッセージ",
      isEnable: "useBeforeWorkTimeWord",
      message: "beforeWorkTimeWord",
    },
    {
      icon: "i-tabler-message-2-search",
      title: "未対応の自動メッセージ",
      isEnable: "useNotStartedWord",
      message: "notStartedWord",
    },
    {
      icon: "i-tabler-message-bolt",
      title: "混雑時の自動メッセージ",
      isEnable: "useCrowdedWord",
      message: "crowdedWord",
    },
    {
      icon: "i-tabler-message-2-off",
      title: "受付期間後の自動メッセージ",
      isEnable: "useAfterWorkTimeWord",
      message: "afterWorkTimeWord",
    },
    {
      icon: "i-tabler-message-2-pause",
      title: "時間外の自動メッセージ",
      isEnable: "useCloseDayWord",
      message: "closeDayWord",
    },
    {
      icon: "i-tabler-message-exclamation",
      title: "テキスト以外のメッセージが送られた時",
      isEnable: "useNotTextMessageWord",
      message: "notTextMessageWord",
    },
  ]);

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);

  const chatTypes = ref([
    {
      icon: "i-uil-line",
      title: "LINEアプリ",
      iconColor: "text-green-500",
      description: "LINEアプリを利用して相談する",
      value: "line",
    },
    {
      icon: "i-twemoji-mobile-phone",
      title: "LIFFブラウザ",
      iconColor: "text-primary-500",
      description: "LINEアプリを利用して相談する",
      value: "liff",
    },
    {
      icons: [
        "i-logos-chrome",
        "i-logos-firefox",
        "i-logos-safari",
        "i-logos-microsoft-edge",
      ],
      title: "外部ブラウザ",
      iconColor: "text-green-500",
      description: "LINEアプリを利用して相談する",
      value: "browser",
    },
  ]);

  const toast = useToast();
  const onUpdateSettings = async () => {
    const result = await automaticChatsStore.updateSettings(
      currentCustomer?.value?.customerId || "",
    );
    if (result) {
      toast.add({
        title: "更新成功",
        description: "自動チャット設定を更新しました。",
        icon: "i-heroicons-check-circle",
      });
    }
  };

  const showBrowser = ref(true);
  onMounted(() => {
    automaticChatsStore.fetchSettings(currentCustomer?.value?.customerId || "");
  });

  watch(
    () => currentCustomer.value,
    () => {
      automaticChatsStore.fetchSettings(
        currentCustomer?.value?.customerId || "",
      );
    },
  );
</script>
