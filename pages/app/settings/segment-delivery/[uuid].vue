<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="セグメント配信作成" class="sticky top-0 z-10">
      <template #left>
        <div class="flex flex-row items-center space-x-2 border-r pr-2 dark:border-gray-500">
          <UButton
            icon="i-heroicons-arrow-left"
            size="sm"
            color="gray"
            variant="ghost"
            label="戻る"
            :trailing="false"
            :padded="false"
            @click="navigateTo('/app/settings/segment-delivery')"
          />
        </div>
      </template>
    </AppPageHeader>

    <div class="p-6">
      <div class="grid grid-cols-3 gap-6">
        <div class="relative col-span-2">
          <UCard :ui="{ base: 'overflow-visible' }">
            <SegmentDeliveryForm />
          </UCard>
        </div>

        <div class="relative">
          <UCard class="sticky top-[70px]">
            <div>
              <SegmentDeliveryTargetStatistics
                :targetsCount="targetStatistics.targetsCount"
                :totalCount="targetStatistics.totalCount"
                :loading="loadings['fetchTargets']"
              />
              <SegmentDeliveryTargetList
                :show="isShowTargetListModal"
                :targets="targetList"
                @close="closeTargetListModal"
              />
              <UButton
                class="mb-4"
                v-if="targetList.length"
                block
                size="sm"
                variant="soft"
                @click="isShowTargetListModal = true"
                :disabled="loadings['fetchTargets']"
                >対象一覧を表示</UButton
              >
            </div>
            <UButton
              :key="targetStatistics"
              block
              icon="i-ic-outline-send"
              class="px-6"
              :disabled="!isSendable"
              @click="onCreateSegmentDelivery"
              :loading="loadings['createSegmentDelivery']"
            >
              すぐ送信
            </UButton>
            <div class="mt-4 text-sm border-t dark:border-gray-700 pt-3">
              <div
                class="flex flex-row items-center space-x-1 text-primary-600"
              >
                <UIcon
                  name="i-heroicons-question-mark-circle-solid"
                  class="text-base"
                />
                <div>セグメント配信作成の流れ</div>
              </div>
              <div class="text-xs mt-2 flex flex-col space-y-1">
                <div
                  class="flex flex-row items-center space-x-1 justify-between"
                  :class="{
                    'text-primary-600': targetStatistics.targetsCount,
                  }"
                >
                  <div>・配信先対象の設定、配信先対象を確認</div>
                  <UIcon
                    v-if="targetStatistics.targetsCount"
                    name="i-heroicons-check-circle-solid"
                    class="text-base"
                  />
                </div>
                <div
                  class="flex flex-row items-center space-x-1 justify-between"
                  :class="{
                    'text-primary-600': segmentDelivery.content,
                  }"
                >
                  <div>・送信内容を入力する</div>
                  <UIcon
                    v-if="segmentDelivery.content"
                    name="i-heroicons-check-circle-solid"
                    class="text-base"
                  />
                </div>
                <div
                  class="flex flex-row items-center space-x-1 justify-between"
                  :class="{
                    'text-primary-600': loadings['createSegmentDelivery'],
                  }"
                >
                  <div>・送信する</div>
                  <UIcon
                    v-if="loadings['createSegmentDelivery']"
                    name="i-heroicons-check-circle-solid"
                    class="text-base"
                  />
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import type { ReportExport } from "~/types";
  import { useSegmentDeliveryStore } from "~/stores/app/segment-delivery";
  const segmentDeliveryStore = useSegmentDeliveryStore();
  const {
    targetStatistics,
    loadings,
    targetList,
    isSendable,
    segmentDelivery,
  } = storeToRefs(segmentDeliveryStore);

  const isShowTargetListModal = ref(false);
  const closeTargetListModal = () => (isShowTargetListModal.value = false);
  const toast = useToast();
  const dialogsStore = useDialogsStore();
  const onCreateSegmentDelivery = async () => {
    dialogsStore.onOpenConfirmDialog({
      title: `${targetStatistics.value.targetsCount}人にメッセージを送信してもよろしいですか`,
      message: segmentDelivery.value.content.text,
      confirmButtonText: "送信",
      cancelButtonText: "キャンセル",
      messageClass: "p-4 bg-gray-50 rounded-lg border text-sm bg-white dark:bg-gray-800 dark:border-gray-700",
      survey: segmentDelivery.value.survey,
      onConfirm: async () => {
        const result = await segmentDeliveryStore.createSegmentDelivery();
        if (result) {
          navigateTo("/app/settings/segment-delivery");
          toast.add({
            title: "セグメント送信しました",
            description: "セグメント配信一覧から確認できます",
            icon: "i-heroicons-check-circle",
            color: "green",
          });
        }
      },
    });
  };
</script>
