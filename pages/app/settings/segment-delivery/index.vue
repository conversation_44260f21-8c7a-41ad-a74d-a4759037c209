<template>
  <div class="flex flex-col w-full h-full">
    <AppPageHeader title="セグメント配信" />

    <div class="p-4 ">
      <SegmentDeliverySearchForm />
    </div>
    <div class="px-6">
      <BaseTable
        title="セグメント配信履歴一覧"
        :pagination="pagination"
        :page-from="pageFrom"
        :page-to="pageTo"
        @update:page-count="(value: number) => (pagination.pageRangeDisplayed = value)"
        @update:page="(value: number) => (pagination.page = value)"
        :total="totalSegmentDeliveriesCount"
      >
        <template #header-right>
          <UButton
            v-can="'create:app-settings-segment-delivery'"
            size="md"
            label="新規手動配信"
            variant="soft"
            icon="i-heroicons-plus-circle-solid"
            color="blue"
            @click="navigateTo('/app/settings/segment-delivery/new')"
          />
        </template>

        <template #action>
          <UButton
            size="xs"
            variant="solid"
            icon="i-heroicons-arrow-path-solid"
            color="gray"
            label="リロード"
            @click="segmentDeliveryStore.fetchData()"
          />
        </template>

        <UTable
          :columns="columns"
          :rows="data"
          v-model:sort="sort"
          sort-mode="manual"
          sort-asc-icon="i-heroicons-arrow-up"
          sort-desc-icon="i-heroicons-arrow-down"
          :loading="loadings['fetchData']"
        >
          <template #segmentId-data="{ row }">
            <div>
              <UTooltip
                class=""
                :text="row.segmentId"
                :popper="{ placement: 'top' }"
                :ui="{ base: 'h-10', container: 'z-50' }"
              >
                <template #text>
                  <div>
                    <div class="text-center">{{ row.segmentId }}</div>
                    <div class="text-xs text-gray-500 text-center">
                      (ボタンをクリックして配信IDがコピーされます)
                    </div>
                  </div>
                </template>
                <UButton
                  size="xs"
                  variant="outline"
                  color="gray"
                  class="px-6"
                  @click="onCopyId(row)"
                >
                  <UIcon
                    name="i-lets-icons-check-fill"
                    v-if="coppied[row.segmentId]"
                    class="text-primary-500 text-lg"
                  />
                  <span v-else> #ID </span>
                </UButton>
              </UTooltip>
            </div>
          </template>
          <template #filter-data="{ row }">
            <div
              class="cursor-pointer hover:underline text-primary-500"
              @click="openFilterDetail(row)"
            >
              <span> {{ Object.keys(row.filter).length }} 条件 </span>
            </div>
          </template>

          <template #createdAt-data="{ row }">
            <div>
              <span>
                {{
                  formatDate(new Date(row.createdAt), "YYYY年MM月DD日 HH時mm分")
                }}
              </span>
            </div>
          </template>
          <template #status-data="{ row }">
            <div class="text-center">
              <UBadge :color="getSegmentDeliveryStatusColor(row.status)">
                {{ $t(row.status) }}
              </UBadge>
            </div>
          </template>
          <template #totalDelivery-data="{ row }">
            <div class="flex flex-row justify-end">
              <span> {{ row.recipientList?.length }} / </span>
              <span>
                {{ row.scheduledRecipientList?.length }}
              </span>
            </div>
          </template>
        </UTable>
      </BaseTable>
    </div>

    <BaseCopyright
      class="mt-4 flex flex-row items-center justify-center"
      textClass="!text-gray-800 dark:!text-gray-300"
      wrapClass="bg-transparent"
    />
    <SegmentDeliveryFilterDetail
      :show="isShowFilterDetailModal"
      :segment="segmentDetail"
      @close="closeFilterDetailModal"
    />
  </div>
</template>

<script setup lang="ts">
  import { storeToRefs } from "pinia";
  import { useSegmentDeliveryStore } from "~/stores/app/segment-delivery";
  const userPermissions = usePermissions();
  const segmentDeliveryStore = useSegmentDeliveryStore();

  const {
    loadings,
    pagination,
    data,
    pageTo,
    pageFrom,
    sortConditions,
    totalSegmentDeliveriesCount,
  } = storeToRefs(segmentDeliveryStore);

  const columns = [
    {
      key: "segmentId",
      label: "配信ID",
      sortable: true,
    },
    {
      key: "segmentName",
      label: "配信名",
      sortable: true,
    },
    {
      key: "filter",
      label: "配信対象条件",
    },
    {
      key: "createdAt",
      label: "作成日時",
      sortable: true,
    },
    {
      key: "status",
      label: "ステータス",
      sortable: true,
      class: "text-center",
    },
    {
      label: "合計配信数",
      key: "totalDelivery",
      class: "text-right",
    },
  ];

  const sort = computed({
    get: () => {
      return {
        column: sortConditions.value.sortBy,
        direction: sortConditions.value.sortDesc ? "desc" : "asc",
      };
    },
    set: (value) => {
      sortConditions.value.sortBy = value.column;
      sortConditions.value.sortDesc = value.direction === "desc";
    },
  });

  const coppied = ref({} as { [key: string]: boolean });
  const onCopyId = (row: any) => {
    navigator.clipboard.writeText(row.segmentId);

    coppied.value[row.segmentId] = true;
    setTimeout(() => {
      coppied.value[row.segmentId] = false;
    }, 1000);
  };

  const isShowFilterDetailModal = ref(false);
  const segmentDetail = ref(null);
  const closeFilterDetailModal = () => {
    segmentDetail.value = null;

    isShowFilterDetailModal.value = false;
  };
  const openFilterDetail = (row: any) => {
    segmentDetail.value = row;
    isShowFilterDetailModal.value = true;
  };
</script>
