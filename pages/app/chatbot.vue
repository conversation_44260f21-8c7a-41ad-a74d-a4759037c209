<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppUIStore } from "~/stores/app/ui";
const appUIStore = useAppUIStore();
const { isSubNavigationMini } = storeToRefs(appUIStore);
definePageMeta({
  navigationPage: "app-chatbot",
  middleware: ["permissions"],
});
</script>

<template>
  <div>
    <AppSubNavigation title="チャットボット">
      <div class="p-2">
        <ChatbotMenu />
      </div>
    </AppSubNavigation>
    <main
      class="relative h-auto min-h-screen transition-all duration-200 space-y-6"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <NuxtPage />
    </main>
  </div>
</template>
