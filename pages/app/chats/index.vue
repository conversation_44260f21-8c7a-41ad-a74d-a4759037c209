<script setup lang="ts">
  import { Splitpanes, Pane } from "splitpanes";
  import { storeToRefs } from "pinia";
  import { useAppUIStore } from "~/stores/app/ui";
  import { useCasesStore } from "~/stores/app/cases";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { Icon } from "@iconify/vue";
  definePageMeta({
    middleware: ["permissions"],
  });
  let autoIntervals: any[] = [];
  const casesStore = useCasesStore();
  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer, currentCustomerId } = storeToRefs(appCustomersStore);
  const appUIStore = useAppUIStore();
  const { isSubNavigationMini } = storeToRefs(appUIStore);

  const { caseDetail, loadings } = storeToRefs(casesStore);
  const router = useRouter();
  const route = useRoute();
  const runtimeConfig = useRuntimeConfig();

  onMounted(async () => {
    const { caseId } = route.query;
    if (!caseId) {
      caseDetail.value = {} as any;
    }

    await casesStore.fetchHandlingCases(
      currentCustomerId.value as string,
      !caseId,
    );
    // auto refresh
    autoIntervals.push(
      setInterval(() => {
        casesStore.fetchHandlingCases(
          currentCustomerId.value as string,
          false,
          false,
        );
      }, runtimeConfig.public.autoRefreshs.chats),
    );
  });

  onBeforeUnmount(() => {
    autoIntervals.forEach((interval) => {
      clearInterval(interval);
    });
  });

  watch(
    () => currentCustomer.value,
    async (newValue, oldValue) => {
      if (
        newValue?.customerId !== oldValue?.customerId &&
        newValue?.customerId
      ) {
        router.push({
          query: {},
        });
        caseDetail.value = {} as any;
        nextTick(() => {
          casesStore.fetchHandlingCases(newValue.customerId as string, true);
        });
      }
    },
  );

  const counselee = computed(() => {
    return {
      userId: caseDetail.value?.counseleeId,
      userName: caseDetail.value?.counseleeName,
      userAvatar: caseDetail.value?.counseleeImage,
      snsChannel: caseDetail.value?.channel,
      isBlock: caseDetail.value?.isBlock,
    };
  });

  const onSend = (message: string, survey: any) => {
    if (message.trim().length === 0 && !survey) {
      return;
    }
    casesStore.sendMessage(caseDetail.value.caseId, { text: message }, survey);
  };

  const onOpenSurveyResult = (surveyResult: any) => {
    caseDetail.value.currentSurveyResult = surveyResult;
    caseDetail.value.chatExtendActiveTab = 2;
  };
</script>

<template>
  <div>
    <AppSubNavigation>
      <div class="p-2">
        <div class="flex flex-row justify-between items-center">
          <div v-if="!isSubNavigationMini" class="font-semibold text-sm px-3">
            相談中のユーザ一覧
          </div>
          <UButton
            icon="i-tabler-refresh"
            size="xs"
            color="gray"
            square
            variant="ghost"
            :ui="{
              rounded: 'rounded-full',
            }"
            @click="
              casesStore.fetchHandlingCases(
                currentCustomer.customerId as string,
                true,
              )
            "
            :class="{
              'mx-auto': isSubNavigationMini,
            }"
          />
        </div>
        <ChatUserList />
      </div>
    </AppSubNavigation>
    <main
      class="relative h-screen transition-all duration-200 space-y-6"
      :class="{
        'md:ml-60': !isSubNavigationMini,
        'md:ml-16': isSubNavigationMini,
      }"
    >
      <div
        v-if="loadings['fetchCaseDetail']"
        class="w-full h-full flex flex-col items-center justify-center space-y-1"
      >
        <Icon icon="eos-icons:loading" class="text-primary-500 text-5xl" />
        <div class="text-gray-500">データを読み込み中</div>
      </div>
      <div v-else class="w-full h-full">
        <splitpanes
          v-if="caseDetail?.caseId"
          class="w-full h-full flex flex-inline"
        >
          <pane class="min-w-[300px] flex flex-col justify-between">
            <ChatHeader :user="counselee" :title="`${caseDetail.count}回目`">
              <template #title>
                <CaseStatusChange :case="caseDetail" :key="caseDetail.status" />
              </template>
            </ChatHeader>
            <ChatMessageList
              class="px-3 pt-4 pb-10"
              :messages="caseDetail.chat"
              :userAvatar="caseDetail.counseleeImage"
              :surveyResults="caseDetail.surveyResults"
              @open-survey-result="onOpenSurveyResult"
            />
            <ChatMessageBox
              class="px-3 pb-3"
              v-model="caseDetail.messageText"
              @send="onSend"
              :loading="loadings.sendMessage[caseDetail.caseId]"
              @select:survey="caseDetail.selectedSurvey = $event"
              :disabled="
                caseDetail.messageText?.trim().length === 0 &&
                !caseDetail.selectedSurvey
              "
            />
          </pane>
          <pane class="bg-gray-100 dark:bg-gray-900 min-w-[300px]">
            <ChatExtends class="h-full" />
          </pane>
        </splitpanes>
        <div
          v-else
          class="h-full flex flex-col justify-center items-center text-gray-400"
        >
          <UIcon name="i-heroicons-chat-bubble-left-right" class="text-7xl" />
          <div class="text-md font-thin mt-4">相談者を選択してください</div>
        </div>
      </div>
    </main>
  </div>
</template>

<style lang="scss" scope>
  .splitpanes__splitter {
    position: relative;
  }
  .splitpanes__splitter:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    transition: opacity 0.4s;
    background-color: rgba(0, 0, 0, 0.1);
    opacity: 0;
    z-index: 1;
  }
  .splitpanes__splitter:hover:before {
    opacity: 1;
  }
  .splitpanes--vertical > .splitpanes__splitter:before {
    left: -5px;
    right: -5px;
    height: 100%;
  }
  .splitpanes--horizontal > .splitpanes__splitter:before {
    top: -5px;
    bottom: -5px;
    width: 100%;
  }
</style>
