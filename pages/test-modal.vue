<template>
  <div class="p-8">
    <h1 class="text-3xl font-bold mb-6">Test Customer Account <PERSON></h1>
    
    <div class="space-y-4">
      <UButton @click="openAddModal" color="primary" size="lg">
        Add New Customer
      </UButton>
      
      <UButton @click="openEditModal" color="gray" size="lg">
        Edit Existing Customer
      </UButton>
    </div>

    <div v-if="selectedFeatures.length > 0" class="mt-8">
      <h2 class="text-xl font-semibold mb-4">Selected Features:</h2>
      <div class="grid grid-cols-4 gap-4">
        <div
          v-for="feature in selectedFeatures"
          :key="feature"
          class="p-3 bg-blue-100 rounded-lg text-center"
        >
          {{ getFeatureName(feature) }}
        </div>
      </div>
    </div>

    <div v-if="chatbotConfig" class="mt-8">
      <h2 class="text-xl font-semibold mb-4">Chatbot Configuration:</h2>
      <UCard>
        <div class="space-y-3">
          <div><strong>Name:</strong> {{ chatbotConfig.name }}</div>
          <div><strong>Welcome Message:</strong> {{ chatbotConfig.welcomeMessage }}</div>
          <div><strong>Auto Response:</strong> {{ chatbotConfig.autoResponse ? 'Enabled' : 'Disabled' }}</div>
          <div v-if="chatbotConfig.autoResponse">
            <strong>Response Delay:</strong> {{ chatbotConfig.responseDelay }} seconds
          </div>
        </div>
      </UCard>
    </div>

    <CustomerAccountModal
      :show="showModal"
      :is-add-new="isAddNew"
      :customer="editCustomer"
      @close="closeModal"
      @addNew="onAddNew"
      @update="onUpdate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { RequestCustomer, Customer } from '~/types'
import { CustomerFeature } from '~/types/enums.d'

const showModal = ref(false)
const isAddNew = ref(true)
const selectedFeatures = ref<CustomerFeature[]>([])
const chatbotConfig = ref<any>(null)
const editCustomer = ref<Customer | null>(null)

const { featureList } = useConstants()

const getFeatureName = (feature: CustomerFeature) => {
  const item = featureList.value.find(f => f.value === feature)
  return item?.title || feature
}

const openAddModal = () => {
  isAddNew.value = true
  editCustomer.value = null
  showModal.value = true
}

const openEditModal = () => {
  isAddNew.value = false
  // Mock customer data for testing
  editCustomer.value = {
    customerId: 'test-123',
    slug: 'test-customer',
    basic: {
      customerName: 'Test Customer',
      customerImage: 'https://via.placeholder.com/150'
    },
    line: {
      isActive: true,
      chatType: 'liff',
      messaging: {
        channelId: 'test-channel-id',
        secret: 'test-secret',
        accessToken: 'test-token'
      }
    },
    application: {
      isActive: false,
      accessCode: '',
      browserAccessCode: '',
      showBrowserAccessCode: true
    },
    facebook: {
      isActive: false,
      pageId: '',
      pageAccessToken: ''
    },
    counseleeLimit: 100,
    ipWhiteLists: ['***********', '********'],
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    contractedLines: 5,
    featureList: [CustomerFeature.Chatbot, CustomerFeature.Wizard, CustomerFeature.TextTemplate],
    chatbot: {
      name: 'サポートボット',
      welcomeMessage: 'こんにちは！何かお困りのことはありませんか？お気軽にお声かけください。',
      autoResponse: true,
      responseDelay: 60
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  } as Customer
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
}

const onAddNew = (customer: RequestCustomer) => {
  console.log('New customer:', customer)
  selectedFeatures.value = customer.featureList || []
  chatbotConfig.value = customer.chatbot || null
  showModal.value = false
}

const onUpdate = (customer: RequestCustomer) => {
  console.log('Updated customer:', customer)
  selectedFeatures.value = customer.featureList || []
  chatbotConfig.value = customer.chatbot || null
  showModal.value = false
}
</script>
