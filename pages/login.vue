<script setup lang="ts">
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useAuthStore } from "~/stores/auth";

  const toast = useToast();
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });

  const authStore = useAuthStore();
  const { loadings, errors } = storeToRefs(authStore);
  const { object, string, boolean } = useYup();
  const { t } = useI18n();
  const schema = object({
    username: string().min(6).required(),
    password: string().min(8).required(),
    rememberMe: boolean().default(false),
  });

  type Schema = InferType<typeof schema>;

  const state = reactive({
    username: undefined as string | undefined,
    password: undefined,
  });
  async function onSubmit(event: FormSubmitEvent<Schema>) {
    // Do something with event.data
    const loginResult = await authStore.login(event.data);
    console.log(
      "🚀 ~ file: login.vue:32 ~ onSubmit ~ loginResult:",
      loginResult,
    );
    if (loginResult) {
      navigateTo("/");
    }
  }
  const isShowAPIUrlSettingDialog = ref(false);
  const useMock = ref(localStorage.getItem("useMock") === "true");
  watch(useMock, (value) => {
    localStorage.setItem("useMock", value.toString());
    if (!value) {
      isShowAPIUrlSettingDialog.value = true;
    } else {
      window.location.reload();
    }
  });

  const showPassword = ref(false);

  const route = useRoute();
  onMounted(() => {
    const { username } = route.query;
    if (username) {
      state.username = username as string;
    }
  });
</script>

<template>
  <div>
    <NuxtLayout name="blank">
      <div class="flex flex-row space-x-2 mb-6">
        <!-- <app-logo class="max-w-[222px] mb-2" /> -->

        <SystemLogo class="mt-5" />
      </div>
      <div
        class="w-full bg-white backdrop-blur rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700"
      >
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1
            class="text-lg font-bold leading-tight tracking-tight text-gray-900 md:text-xl dark:text-white"
            data-testid="login-title"
          >
            {{ $t("Sign in to your account") }}
          </h1>
          <UForm
            class="space-y-6"
            :schema="schema"
            :state="state"
            @submit="onSubmit"
            data-testid="login-form"
          >
            <UFormGroup :label="$t('Login ID')" required name="username">
              <UInput
                v-model="state.username"
                :placeholder="$t('Your login ID')"
                icon="i-heroicons-identification"
                size="lg"
                data-testid="username"
              />
            </UFormGroup>

            <UFormGroup :label="$t('Password')" required name="password">
              <UInput
                v-model="state.password"
                :placeholder="$t('Your password')"
                icon="i-heroicons-lock-closed"
                size="lg"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="off"
                :ui="{ icon: { trailing: { pointer: '' } } }"
                data-testid="password"
              >
                <template #trailing>
                  <UButton
                    :padded="false"
                    @click="showPassword = !showPassword"
                    color="gray"
                    variant="link"
                    :icon="
                      showPassword
                        ? 'i-heroicons-eye-slash-solid'
                        : 'i-heroicons-eye-solid'
                    "
                    data-testid="toggle-password"
                  />
                </template>
              </UInput>
            </UFormGroup>

            <div class="flex items-center justify-between">
              <UCheckbox name="rememberMe" :label="$t('Remember me')" data-testid="remember-me" />
              <ULink
                to="/forgot-password"
                active-class="text-primary"
                inactive-class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500"
                data-testid="forgot-password-link"
              >
                {{ $t("Forgot password?") }}
              </ULink>
            </div>
            <UAlert
              v-if="errors.login"
              icon="i-heroicons-exclamation-triangle"
              :description="$t(errors.login)"
              :title="$t('Login failed')"
              color="red"
              variant="subtle"
              data-testid="login-error"
            />
            <UButton
              block
              :label="$t('Login')"
              type="submit"
              :loading="loadings.login === true"
              data-testid="login-submit"
            />
          </UForm>
        </div>
      </div>

      <BaseAPIUrlModal
        :show="isShowAPIUrlSettingDialog"
        @close="isShowAPIUrlSettingDialog = false"
      />
    </NuxtLayout>
  </div>
</template>
