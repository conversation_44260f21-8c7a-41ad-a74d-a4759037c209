<script setup lang="ts">
  import { useAuthStore } from "~/stores/auth";
  import { Icon } from "@iconify/vue";
  import { useAppCustomersStore } from "~/stores/app/customers";
  import { storeToRefs } from "pinia";

  const appCustomersStore = useAppCustomersStore();
  const { currentCustomer } = storeToRefs(appCustomersStore);
  const socketStore = useSocketStore();
  const authStore = useAuthStore();
  const permissionsStore = usePermissionsStore();
  const { loadings, user } = storeToRefs(authStore);
  definePageMeta({
    layout: false,
    middleware: "auth",
  });

  watch(
    () => user.value,
    (value) => {
      if (value) {
        permissionsStore.setPermissionsAndRoles(
          currentCustomer.value?.customerId as string,
        );
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  onMounted(() => {
    authStore.fetchUserMe();
    // socketStore.online();
  });
</script>

<template>
  <div
    v-if="loadings['fetchUserMe']"
    class="bg-primary-800 dark:bg-gray-900 flex flex-col space-y-2 w-full h-screen justify-center items-center text-white"
  >
    <div class="flex flex-row space-x-2">
      <!-- <app-logo class="max-w-[222px] mb-2" /> -->

      <SystemLogo class="mt-6" />
    </div>

    <Icon icon="eos-icons:loading" class="text-4xl" />
    <div class="text-center">只今準備中です。もうしばらくお待ちください。</div>
  </div>
  <div v-else>
    <NuxtLayout name="app">
      <NuxtPage />
    </NuxtLayout>

    <AppCounselorModal />
  </div>
</template>
