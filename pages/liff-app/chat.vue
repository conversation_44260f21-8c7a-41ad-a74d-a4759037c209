<script setup lang="ts">
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });

  import { storeToRefs } from "pinia";
  import { useLiffAppStore } from "~/stores/liff-app";
  let autoIntervals: any[] = [];
  const { liff, relogin } = useLiff();
  const liffAppStore = useLiffAppStore();
  const { loadings, customer, messages, lineUser, errors } =
    storeToRefs(liffAppStore);
  const messageText = ref<string>("");
  const onSend = (message: string) => {
    liffAppStore.sendMessage(message);
  };
  const onEndChat = () => {
    liffAppStore.endChat();
    liff.closeWindow();
  };
  const runtimeConfig = useRuntimeConfig();
  const useMock = ref(localStorage.getItem("useMock") === "true");
  watch(useMock, (value) => {
    localStorage.setItem("useMock", value.toString());
    window.location.reload();
  });

  watch(
    () => errors.value,
    (errors) => {
      if (
        [errors["fetchCounseleeInfo"], errors["sendMessage"]].includes(
          "ID_TOKEN_EXPIRED",
        )
      ) {
        relogin();
      }
    },
    {
      deep: true,
      immediate: true,
    },
  );

  onMounted(() => {
    autoIntervals.push(
      setInterval(() => {
        liffAppStore.fetchCounseleeInfo(
          customer.value?.customerId as string,
          false,
        );
      }, runtimeConfig.public.autoRefreshs.counseleeChat),
    );
  });

  onBeforeUnmount(() => {
    autoIntervals.forEach((interval) => {
      clearInterval(interval);
    });
  });
</script>

<template>
  <NuxtLayout name="liff-app">
    <div
      class="relative h-full flex flex-col bg-slate-50/95 w-full max-w-xl md:rounded-t-2xl"
    >
      <div class="fixed w-full max-w-xl">
        <LiffAppChatHeader :user="customer" class="w-full z-50">
          <template #right>
            <UTooltip v-if="messages.length > 0" text="リフレッシュ">
              <UButton
                icon="i-charm-refresh"
                size="xs"
                square
                variant="soft"
                :ui="{
                  rounded: 'rounded-full',
                }"
                class="mr-2"
                @click="
                  liffAppStore.fetchCounseleeInfo(
                    customer?.customerId as string,
                  )
                "
              />
            </UTooltip>
            <UButton
              v-if="liff.isInClient()"
              label="閉じる"
              class="px-3"
              size="xs"
              square
              color="red"
              variant="soft"
              :ui="{
                rounded: 'rounded-full',
              }"
              v-confirm="{
                title: '閉じる',
                message: '相談を閉じますか？',
                confirmButtonText: 'はい',
                cancelButtonText: 'いいえ',
                onConfirm: () => onEndChat(),
              }"
            />
          </template>
        </LiffAppChatHeader>
      </div>

      <div v-if="messages.length === 0" class="h-full w-full px-3">
        <div
          v-if="lineUser"
          class="flex flex-col items-center h-full justify-center space-y-4"
        >
          <UAvatar
            size="2xl"
            :src="lineUser.pictureUrl"
            :alt="lineUser.displayName"
          />
          <div
            v-if="loadings['fetchCounseleeInfo']"
            class="text-center text-gray-500 font-thin pb-1"
          >
            接続中です<br />しばらくお待ちください。
          </div>
          <div v-else class="text-center text-gray-500 font-thin pb-1">
            <strong>{{ lineUser.displayName }}</strong
            >様、こんにちは<br />
            ご相談をお待ちしております。
          </div>
        </div>
      </div>
      <LiffAppChatMessageList
        v-else
        class="h-full w-full px-1 lg:px-4 mt-[50px] pt-4 pb-32"
        :messages="messages"
        :loading="loadings.fetchCounseleeInfo"
      />
      <div
        v-if="!loadings['fetchCounseleeInfo']"
        class="fixed bottom-0 w-full max-w-xl"
      >
        <LiffAppChatMessageBox
          class="px-4 w-full"
          v-model="messageText"
          @send="onSend"
        />
        <div
          class="text-center text-gray-500 text-xs pt-1 font-light pb-1 bg-slate-50"
        >
          Powered by {{ $t("system_name") }}
        </div>
      </div>
    </div>
  </NuxtLayout>
</template>
