<script setup lang="ts">
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useAuthStore } from "~/stores/auth";
  import dayjs from "dayjs";
  const yup = useYup();
  const toast = useToast();
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });

  const authStore = useAuthStore();
  const {
    loadings,
    errors,
    forgotPasswordTime,
    forgotPasswordRequested,
    forgotPasswordUserName,
    canResetPasswordAfter,
  } = storeToRefs(authStore);
  const { object, string } = useYup();
  const { t } = useI18n();
  const schema = object({
    loginID: string().min(6).required(),
    otp: string()
      .required()
      .matches(/^\d{6}$/, t("OTP must be 6 digits")),
    newPassword: string().password().min(8).required(),
    confirmNewPassword: string()
      .required()
      .min(8)
      .oneOf([yup.ref("newPassword"), ""], t("Passwords must match")),
  });

  type Schema = InferType<typeof schema>;

  const state = reactive({
    loginID: "",
    otp: "",
    newPassword: "",
    confirmNewPassword: "",
  });

  async function onSubmit(event: FormSubmitEvent<Schema>) {
    // Do something with event.data
    const result = await authStore.resetPassword(
      event.data?.loginID,
      event.data?.otp,
      event.data?.newPassword,
    );
    if (result) {
      toast.add({
        title: "パスワードを再設定しました",
        description: "ログインしてみてください",
        icon: "i-heroicons-check-circle",
      });
      forgotPasswordTime.value = null;
      forgotPasswordUserName.value = null;
      forgotPasswordRequested.value = false;

      navigateTo({
        name: "login",
        query: {
          username: state.loginID,
        },
      });
    }
  }

  const validCountDown = ref(0);

  const validCountDownFormatted = computed(() => {
    return dayjs().startOf("day").second(validCountDown.value).format("mm:ss");
  });
  let countDownInterval = null as any;
  const route = useRoute();
  onMounted(() => {
    errors.value.resetPassword = null;
    const { username } = route.query;
    if (!username || username !== forgotPasswordUserName.value) {
      navigateTo({
        name: "login",
      });
    } else {
      state.loginID = username;
      const canResetBefore = dayjs(forgotPasswordTime.value).add(
        canResetPasswordAfter.value,
        "second",
      );
      if (dayjs().isBefore(canResetBefore)) {
        validCountDown.value = canResetBefore.diff(dayjs(), "second");
      }
      countDownInterval = setInterval(() => {
        validCountDown.value = canResetBefore.diff(dayjs(), "second");
        if (validCountDown.value <= 0) {
          forgotPasswordTime.value = null;
          forgotPasswordUserName.value = null;
          forgotPasswordRequested.value = false;
          navigateTo({
            name: "login",
          });
          clearInterval(countDownInterval);
        }
      }, 1000);
    }
  });

  onBeforeUnmount(() => {
    clearInterval(countDownInterval);
  });

  const passwordPolicyCheck = computed(() => {
    return [
      {
        label: "8文字以上",
        valid: state.newPassword.length >= 8,
      },
      {
        label: "必ず１つ大文字を含む",
        valid: /[A-Z]/.test(state.newPassword),
      },
      {
        label: "必ず１つ小文字を含む",
        valid: /[a-z]/.test(state.newPassword),
      },
      {
        label: "必ず１つ数字を含む",
        valid: /\d/.test(state.newPassword),
      },
      {
        label: "必ず１つ特殊文字を含む（使用可能な特殊文字：!@$%^&*+#)",
        valid: /[!@$%^&*+#]/.test(state.newPassword),
      },
    ];
  });
</script>

<template>
  <div>
    <NuxtLayout name="blank">
      <div class="flex flex-row space-x-2 mb-6">
        <!-- <app-logo class="max-w-[222px] mb-2" /> -->

        <SystemLogo class="mt-5" />
      </div>
      <div
        class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 z-10"
      >
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1
            class="text-lg font-bold leading-tight tracking-tight text-gray-900 md:text-xl dark:text-white"
          >
            パスワード再設定
          </h1>
          <UForm
            class="space-y-4"
            :schema="schema"
            :state="state"
            @submit="onSubmit"
          >
            <UFormGroup :label="$t('Login ID')" required name="loginID">
              <UInput
                :model-value="state.loginID"
                icon="i-heroicons-identification"
                size="lg"
                color="gray"
                disabled
              />
            </UFormGroup>
            <UFormGroup
              label="OTP"
              required
              name="otp"
              :hint="`メールに送られた6桁の数字`"
            >
              <UInput
                v-model="state.otp"
                placeholder="OTP"
                icon="i-tabler-password-mobile-phone"
                size="lg"
                type="text"
              >
                <template #trailing>
                  <span class="text-orange-500 text-xs"
                    >{{ validCountDownFormatted }}
                    後に有効期限切れ
                  </span>
                </template>
              </UInput>
            </UFormGroup>

            <UFormGroup
              :label="$t('New password')"
              required
              name="newPassword"
            >
              <BasePasswordInput
                v-model="state.newPassword"
                :placeholder="$t('Your new password')"
                icon="i-heroicons-lock-closed"
                size="lg"
              />
              <template #error>
                <div class="flex flex-col space-y-1">
                  <div class="text-xs text-gray-700">
                    パスワードは以下の条件を満たす必要があります
                  </div>
                  <div
                    v-for="policy in passwordPolicyCheck"
                    class="flex items-center space-x-1 !text-gray-500"
                  >
                    <UIcon
                      :name="
                        policy.valid
                          ? 'i-heroicons-check-circle-solid'
                          : 'i-heroicons-minus-circle'
                      "
                      class="text-sm"
                      :class="policy.valid ? 'text-green-600' : 'text-red-500'"
                    />
                    <span
                      :class="policy.valid ? 'text-green-600' : 'text-red-500'"
                      class="text-xs"
                      >{{ policy.label }}</span
                    >
                  </div>
                </div>
              </template>
            </UFormGroup>

            <UFormGroup
              :label="$t('Confirm new password')"
              required
              name="confirmNewPassword"
            >
              <BasePasswordInput
                v-model="state.confirmNewPassword"
                :placeholder="$t('Confirm new password')"
                icon="i-heroicons-lock-closed"
                size="lg"
              />
            </UFormGroup>
            <UAlert
              v-if="errors.resetPassword"
              icon="i-heroicons-exclamation-triangle"
              :description="$t(errors.resetPassword)"
              title="パスワードの再設定に失敗しました"
              color="red"
              variant="subtle"
            />
            <UButton
              block
              :label="`パスワードを再設定`"
              type="submit"
              :loading="loadings.resetPassword"
            />
            <div class="flex items-center">
              <ULink
                to="/login"
                active-class="text-primary"
                inactive-class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500"
              >
                <UIcon name="i-heroicons-arrow-left" />
                {{ $t("Back to login") }}
              </ULink>
            </div>
          </UForm>
        </div>
      </div>
    </NuxtLayout>
  </div>
</template>
