<script setup lang="ts"></script>

<template>
  <div>
    <div
      v-if="loadings['fetchCustomerInfo']"
      class="flex flex-col h-screen p-6 justify-center items-center space-y-4"
    >
      <Icon icon="eos-icons:loading" class="text-5xl text-gray-500" />
      <div class="text-sm text-gray-500">データを読み込んでいます</div>
    </div>
    <NuxtPage v-else/>
  </div>
</template>
<script setup lang="ts">
  import { Icon } from "@iconify/vue";
  const wizardStore = useWizardStore();

  const { loadings, customer, errors } = storeToRefs(wizardStore);
  const route = useRoute();
  onMounted(() => {
    wizardStore.fetchCustomerInfo(route.params.slug);
  });

  watch(
    () => errors.value,
    (error) => {
      console.log("🚀 ~ error:", error);
      if (error.fetchCustomerInfo) {
        navigateTo("/wizard-app/not-found");
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
