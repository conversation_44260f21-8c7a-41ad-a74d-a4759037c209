<template>
  <NuxtLayout name="wizard-app">
    <div class="md:pb-20 pb-40">
      <div v-for="wizardElm in currentFormElements">
        <component
          v-if="wizardElm"
          :is="WizardElementComponent[wizardElm?.type]"
          :schema="wizardElm"
          :isCanNext="isCanNext"
          @click:button="onClick"
          :loading="loadings[wizardElm.id]"
        />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
  import type { WizardElementButton, WizardActionType } from "~/types";

  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });
  const { WizardElementComponent } = useWizard();
  const wizardStore = useWizardStore();
  const { currentFormElements, isCanNext, loadings, nextAction, stepsHistory } =
    storeToRefs(wizardStore);

  const route = useRoute();
  const router = useRouter();
  onMounted(() => {
    // wizardStore.fetchWizard();
    // const step = Number(route.query.step);
    // if (step) {
    //   currentStep.value = step;
    //   router.push({
    //     query: {
    //       step: step,
    //     },
    //   });
    // } else {

    // }

    // router.push({
    //   query: {
    //     step: 1,
    //   },
    // });
    stepsHistory.value = [1];

  });

  // watch(
  //   () => currentStep.value,
  //   (value) => {
  //     if (value) {
  //       router.push({
  //         query: {
  //           step: value,
  //         },
  //       });
  //     }
  //   },
  // );

  // watch(
  //   () => route.query.step,
  //   (value) => {
  //     if (value) {
  //       currentStep.value = Number(value);
  //     }
  //   },
  // );

  const onClick = async (
    button: WizardElementButton,
    schema: WizardElement,
  ) => {
    if (button.isPrev) {
      wizardStore.prevStep();
    } else {
      loadings.value[schema.id as string] = true;
      await wizardStore.nextStep(nextAction.value || button.next, schema);
      loadings.value[schema.id as string] = false;
    }
  };
</script>
