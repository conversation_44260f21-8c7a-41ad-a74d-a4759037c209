<script setup lang="ts">
  import { type InferType } from "yup";
  import { storeToRefs } from "pinia";
  import type { FormSubmitEvent } from "#ui/types";
  import { useAuthStore } from "~/stores/auth";
  import dayjs from "dayjs";
  const toast = useToast();
  definePageMeta({
    layout: false,
    noNeedAuth: true,
  });

  const authStore = useAuthStore();
  const {
    loadings,
    errors,
    forgotPasswordTime,
    canSendOTPAfter,
    forgotPasswordRequested,
  } = storeToRefs(authStore);
  const { object, string } = useYup();
  const { t } = useI18n();
  const schema = object({
    loginID: string().min(6).required(),
    email: string().email("有効なメールアドレスを入力してください").required(),
  });

  type Schema = InferType<typeof schema>;

  const state = reactive({
    loginID: "",
    email: "",
  });

  async function onSubmit(event: FormSubmitEvent<Schema>) {
    // Do something with event.data
    const result = await authStore.forgotPassword(
      event.data?.loginID,
      event.data?.email,
    );
    if (result) {
      toast.add({
        title: "パスワードリセットのリクエストが成功しました",
        description: "メールをご確認ください",
        icon: "i-heroicons-check-circle",
      });

      navigateTo({
        name: "set-password",
        query: {
          username: event.data?.loginID,
        },
      });
    }
  }

  const validCountDown = ref(0);

  const validCountDownFormatted = computed(() => {
    return dayjs().startOf("day").second(validCountDown.value).format("mm:ss");
  });
  let countDownInterval = null as any;
  onMounted(() => {
    errors.value.forgotPassword = null;
    const canResetBefore = dayjs(forgotPasswordTime.value).add(
      canSendOTPAfter.value,
      "second",
    );
    if (dayjs().isBefore(canResetBefore)) {
      validCountDown.value = canResetBefore.diff(dayjs(), "second");
    }
    countDownInterval = setInterval(() => {
      validCountDown.value = canResetBefore.diff(dayjs(), "second");
      if (validCountDown.value <= 0) {
        clearInterval(countDownInterval);
      }
    }, 1000);
  });

  onBeforeUnmount(() => {
    clearInterval(countDownInterval);
  });
</script>

<template>
  <div>
    <NuxtLayout name="blank">
      <div class="flex flex-row space-x-2 mb-6">
        <SystemLogo class="mt-5" />
      </div>
      <div
        class="w-full bg-white rounded-lg shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700"
      >
        <div class="p-6 space-y-4 md:space-y-6 sm:p-8">
          <h1
            class="text-lg font-bold leading-tight tracking-tight text-gray-900 md:text-xl dark:text-white"
          >
            {{ $t("Forgot password") }}
          </h1>
          <UForm
            class="space-y-6"
            :schema="schema"
            :state="state"
            @submit="onSubmit"
          >
            <UFormGroup :label="$t('Login ID')" required name="loginID">
              <UInput
                v-model="state.loginID"
                :placeholder="$t('Your login ID')"
                icon="i-heroicons-identification"
                size="lg"
              />
            </UFormGroup>

            <UFormGroup label="メールアドレス" required name="email">
              <UInput
                v-model="state.email"
                placeholder="メールアドレスを入力してください"
                icon="i-material-symbols-alternate-email"
                size="lg"
              />
            </UFormGroup>

            <UAlert
              v-if="errors.forgotPassword"
              icon="i-heroicons-exclamation-triangle"
              :description="$t(errors.forgotPassword)"
              title="パスワードリセットのリクエストが失敗しました"
              color="red"
              variant="subtle"
            />
            <UButton
              block
              :label="
                validCountDown > 0
                  ? `${validCountDownFormatted} 後にOTPコードを再送可能`
                  : $t('Reset password')
              "
              type="submit"
              :loading="loadings.forgotPassword"
              :disabled="validCountDown > 0"
            />
            <div class="flex items-center">
              <ULink
                to="/login"
                active-class="text-primary"
                inactive-class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500"
              >
                <UIcon name="i-heroicons-arrow-left" />
                {{ $t("Back to login") }}
              </ULink>
            </div>
          </UForm>
        </div>
      </div>
    </NuxtLayout>
  </div>
</template>
