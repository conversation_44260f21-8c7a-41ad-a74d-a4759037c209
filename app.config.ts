export default defineAppConfig({
  ui: {
    primary: "cyan",
    gray: "neutral",
    colorMode: {
      preference: "light",
    },
    input: {
      color: {
        white: {
          // outline: 'dark:bg-gray-700'
        },
      },
    },
    button: {
      default: {
        size: "md",
      },
    },
    verticalNavigation: {
      size: "text-md",
      icon: {
        base: "w-5 h-5",
      },
      padding: "px-3 py-2",
      active:
        "text-gray-900 dark:text-white before:bg-primary-100 dark:before:bg-primary-900",
    },
    card: {
      background: "bg-white dark:bg-gray-900",
    },
    modal: {
      background: "bg-white dark:bg-gray-800",
    },
    table: {
      emptyState: {
        label: "データがありません",
      },
    },
  },
});
